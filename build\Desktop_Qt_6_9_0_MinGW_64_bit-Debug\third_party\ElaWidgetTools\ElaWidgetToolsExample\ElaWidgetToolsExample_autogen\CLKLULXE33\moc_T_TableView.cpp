/****************************************************************************
** Meta object code from reading C++ file 'T_TableView.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetToolsExample/ExamplePage/T_TableView.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'T_TableView.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN11T_TableViewE_t {};
} // unnamed namespace

template <> constexpr inline auto T_TableView::qt_create_metaobjectdata<qt_meta_tag_ZN11T_TableViewE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "T_TableView",
        "",
        "QWidget*",
        "parent"
    };

    QtMocHelpers::UintData qt_methods {
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    using Constructor = QtMocHelpers::NoType;
    QtMocHelpers::UintData qt_constructors {
        QtMocHelpers::ConstructorData<Constructor(QWidget *)>(1, QMC::AccessPublic, {{
            { 0x80000000 | 2, 3 },
        }}),
        QtMocHelpers::ConstructorData<Constructor()>(1, QMC::AccessPublic | QMC::MethodCloned),
    };
    return QtMocHelpers::metaObjectData<T_TableView, qt_meta_tag_ZN11T_TableViewE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums, qt_constructors);
}
Q_CONSTINIT const QMetaObject T_TableView::staticMetaObject = { {
    QMetaObject::SuperData::link<T_BasePage::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11T_TableViewE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11T_TableViewE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN11T_TableViewE_t>.metaTypes,
    nullptr
} };

void T_TableView::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<T_TableView *>(_o);
    if (_c == QMetaObject::CreateInstance) {
        switch (_id) {
        case 0: { T_TableView *_r = new T_TableView((*reinterpret_cast<std::add_pointer_t<QWidget*>>(_a[1])));
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        case 1: { T_TableView *_r = new T_TableView();
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        default: break;
        }
    }
    if (_c == QMetaObject::ConstructInPlace) {
        switch (_id) {
        case 0: { new (_a[0]) T_TableView((*reinterpret_cast<std::add_pointer_t<QWidget*>>(_a[1]))); } break;
        case 1: { new (_a[0]) T_TableView(); } break;
        default: break;
        }
    }
    (void)_t;
}

const QMetaObject *T_TableView::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *T_TableView::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11T_TableViewE_t>.strings))
        return static_cast<void*>(this);
    return T_BasePage::qt_metacast(_clname);
}

int T_TableView::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = T_BasePage::qt_metacall(_c, _id, _a);
    return _id;
}
QT_WARNING_POP
