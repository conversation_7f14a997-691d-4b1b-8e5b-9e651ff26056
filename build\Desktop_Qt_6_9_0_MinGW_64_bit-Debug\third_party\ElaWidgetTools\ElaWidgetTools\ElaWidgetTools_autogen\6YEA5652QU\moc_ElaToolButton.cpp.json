{"classes": [{"className": "ElaToolButton", "lineNumber": 9, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pBorderRadius", "notify": "pBorderRadiusChanged", "read": "getBorderRadius", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBorderRadius"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pIsSelected", "notify": "pIsSelectedChanged", "read": "getIsSelected", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setIsSelected"}], "qualifiedClassName": "ElaToolButton", "signals": [{"access": "public", "index": 0, "name": "pBorderRadiusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pIsSelectedChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QToolButton"}]}], "inputFile": "ElaToolButton.h", "outputRevision": 69}