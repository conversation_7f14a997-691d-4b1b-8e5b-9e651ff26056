{"classes": [{"className": "ElaPromotionView", "lineNumber": 10, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pCardExpandWidth", "notify": "pCardExpandWidthChanged", "read": "getCardExpandWidth", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setCardExpandWidth"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pCardCollapse<PERSON><PERSON><PERSON>", "notify": "pCardCollapseWidthChanged", "read": "getCardCollapseWidth", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setCardCollapseWidth"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "pCurrentIndex", "notify": "pCurrentIndexChanged", "read": "getCurrentIndex", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setCurrentIndex"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "pIsAutoScroll", "notify": "pIsAutoScrollChanged", "read": "getIsAutoScroll", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setIsAutoScroll"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "pAutoScrollInterval", "notify": "pAutoScrollIntervalChanged", "read": "getAutoScrollInterval", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setAutoScrollInterval"}], "qualifiedClassName": "ElaPromotionView", "signals": [{"access": "public", "index": 0, "name": "pCardExpandWidthChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pCardCollapseWidthChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "pCurrentIndexChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "pIsAutoScrollChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "pAutoScrollIntervalChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "ElaPromotionView.h", "outputRevision": 69}