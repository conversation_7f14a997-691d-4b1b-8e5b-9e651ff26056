﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{2B00ACB5-DE28-3672-8636-F28ADBA637DD}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ElaWidgetTools_automoc_json_extraction</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\CMakeFiles\037459b149022cfbf4e80e66df742cf5\ElaWidgetTools_automoc_json_extraction.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Running AUTOMOC file extraction for target ElaWidgetTools</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\Program\Qt\6.9.0\mingw_64\bin\cmake_automoc_parser.exe --cmake-autogen-cache-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/ElaWidgetTools_autogen.dir/ParseCache_Debug.txt --cmake-autogen-info-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/ElaWidgetTools_autogen.dir/AutogenInfo.json --output-file-path C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/ElaWidgetTools_json_file_list.txt --timestamp-file-path C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/ElaWidgetTools_json_file_list.txt.timestamp --cmake-autogen-include-dir-path C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/ElaWidgetTools_autogen/include_Debug --cmake-multi-config
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Program\Qt\6.9.0\mingw_64\bin\cmake_automoc_parser.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\CMakeFiles\ElaWidgetTools_automoc_json_extraction</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Running AUTOMOC file extraction for target ElaWidgetTools</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\Program\Qt\6.9.0\mingw_64\bin\cmake_automoc_parser.exe --cmake-autogen-cache-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/ElaWidgetTools_autogen.dir/ParseCache_Release.txt --cmake-autogen-info-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/ElaWidgetTools_autogen.dir/AutogenInfo.json --output-file-path C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/ElaWidgetTools_json_file_list.txt --timestamp-file-path C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/ElaWidgetTools_json_file_list.txt.timestamp --cmake-autogen-include-dir-path C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/ElaWidgetTools_autogen/include_Release --cmake-multi-config
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Program\Qt\6.9.0\mingw_64\bin\cmake_automoc_parser.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\CMakeFiles\ElaWidgetTools_automoc_json_extraction</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Running AUTOMOC file extraction for target ElaWidgetTools</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\Program\Qt\6.9.0\mingw_64\bin\cmake_automoc_parser.exe --cmake-autogen-cache-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/ElaWidgetTools_autogen.dir/ParseCache_MinSizeRel.txt --cmake-autogen-info-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/ElaWidgetTools_autogen.dir/AutogenInfo.json --output-file-path C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/ElaWidgetTools_json_file_list.txt --timestamp-file-path C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/ElaWidgetTools_json_file_list.txt.timestamp --cmake-autogen-include-dir-path C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/ElaWidgetTools_autogen/include_MinSizeRel --cmake-multi-config
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Program\Qt\6.9.0\mingw_64\bin\cmake_automoc_parser.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\CMakeFiles\ElaWidgetTools_automoc_json_extraction</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Running AUTOMOC file extraction for target ElaWidgetTools</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\Program\Qt\6.9.0\mingw_64\bin\cmake_automoc_parser.exe --cmake-autogen-cache-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/ElaWidgetTools_autogen.dir/ParseCache_RelWithDebInfo.txt --cmake-autogen-info-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/ElaWidgetTools_autogen.dir/AutogenInfo.json --output-file-path C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/ElaWidgetTools_json_file_list.txt --timestamp-file-path C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/ElaWidgetTools_json_file_list.txt.timestamp --cmake-autogen-include-dir-path C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/ElaWidgetTools_autogen/include_RelWithDebInfo --cmake-multi-config
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Program\Qt\6.9.0\mingw_64\bin\cmake_automoc_parser.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\CMakeFiles\ElaWidgetTools_automoc_json_extraction</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -SC:/Users/<USER>/Desktop/Civitai_IMG -BC:/Users/<USER>/Desktop/Civitai_IMG/build --check-stamp-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CMakePackageConfigHelpers.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\WriteBasicConfigVersionFile.cmake;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetToolsConfig.cmake.in;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaWidgetTools.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -SC:/Users/<USER>/Desktop/Civitai_IMG -BC:/Users/<USER>/Desktop/Civitai_IMG/build --check-stamp-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CMakePackageConfigHelpers.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\WriteBasicConfigVersionFile.cmake;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetToolsConfig.cmake.in;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaWidgetTools.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -SC:/Users/<USER>/Desktop/Civitai_IMG -BC:/Users/<USER>/Desktop/Civitai_IMG/build --check-stamp-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CMakePackageConfigHelpers.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\WriteBasicConfigVersionFile.cmake;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetToolsConfig.cmake.in;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaWidgetTools.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -SC:/Users/<USER>/Desktop/Civitai_IMG -BC:/Users/<USER>/Desktop/Civitai_IMG/build --check-stamp-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CMakePackageConfigHelpers.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\WriteBasicConfigVersionFile.cmake;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetToolsConfig.cmake.in;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaWidgetTools.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\CMakeFiles\ElaWidgetTools_automoc_json_extraction">
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\ZERO_CHECK.vcxproj">
      <Project>{442297F5-2886-3548-A699-3DC346C07EB5}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>