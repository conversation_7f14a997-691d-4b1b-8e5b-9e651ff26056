/****************************************************************************
** Meta object code from reading C++ file 'ElaKeyBinder.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaKeyBinder.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaKeyBinder.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN12ElaKeyBinderE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaKeyBinder::qt_create_metaobjectdata<qt_meta_tag_ZN12ElaKeyBinderE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaKeyBinder",
        "pBorderRadiusChanged",
        "",
        "pBinderKeyTextChanged",
        "pNativeVirtualBinderKeyChanged",
        "binderKeyTextChanged",
        "binderKeyText",
        "nativeVirtualBinderKeyChanged",
        "binderKey",
        "pBorderRadius",
        "pBinderKeyText",
        "pNativeVirtualBinderKey"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pBorderRadiusChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pBinderKeyTextChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pNativeVirtualBinderKeyChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'binderKeyTextChanged'
        QtMocHelpers::SignalData<void(QString)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 },
        }}),
        // Signal 'nativeVirtualBinderKeyChanged'
        QtMocHelpers::SignalData<void(quint32)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::UInt, 8 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pBorderRadius'
        QtMocHelpers::PropertyData<int>(9, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pBinderKeyText'
        QtMocHelpers::PropertyData<QString>(10, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pNativeVirtualBinderKey'
        QtMocHelpers::PropertyData<quint32>(11, QMetaType::UInt, QMC::DefaultPropertyFlags | QMC::Writable, 2),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaKeyBinder, qt_meta_tag_ZN12ElaKeyBinderE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaKeyBinder::staticMetaObject = { {
    QMetaObject::SuperData::link<QLabel::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN12ElaKeyBinderE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN12ElaKeyBinderE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN12ElaKeyBinderE_t>.metaTypes,
    nullptr
} };

void ElaKeyBinder::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaKeyBinder *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pBorderRadiusChanged(); break;
        case 1: _t->pBinderKeyTextChanged(); break;
        case 2: _t->pNativeVirtualBinderKeyChanged(); break;
        case 3: _t->binderKeyTextChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 4: _t->nativeVirtualBinderKeyChanged((*reinterpret_cast< std::add_pointer_t<quint32>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaKeyBinder::*)()>(_a, &ElaKeyBinder::pBorderRadiusChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaKeyBinder::*)()>(_a, &ElaKeyBinder::pBinderKeyTextChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaKeyBinder::*)()>(_a, &ElaKeyBinder::pNativeVirtualBinderKeyChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaKeyBinder::*)(QString )>(_a, &ElaKeyBinder::binderKeyTextChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaKeyBinder::*)(quint32 )>(_a, &ElaKeyBinder::nativeVirtualBinderKeyChanged, 4))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = _t->getBorderRadius(); break;
        case 1: *reinterpret_cast<QString*>(_v) = _t->getBinderKeyText(); break;
        case 2: *reinterpret_cast<quint32*>(_v) = _t->getNativeVirtualBinderKey(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setBorderRadius(*reinterpret_cast<int*>(_v)); break;
        case 1: _t->setBinderKeyText(*reinterpret_cast<QString*>(_v)); break;
        case 2: _t->setNativeVirtualBinderKey(*reinterpret_cast<quint32*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaKeyBinder::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaKeyBinder::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN12ElaKeyBinderE_t>.strings))
        return static_cast<void*>(this);
    return QLabel::qt_metacast(_clname);
}

int ElaKeyBinder::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QLabel::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 5;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void ElaKeyBinder::pBorderRadiusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaKeyBinder::pBinderKeyTextChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaKeyBinder::pNativeVirtualBinderKeyChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaKeyBinder::binderKeyTextChanged(QString _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1);
}

// SIGNAL 4
void ElaKeyBinder::nativeVirtualBinderKeyChanged(quint32 _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}
QT_WARNING_POP
