#ifndef CIVITAIIMAGEINFO_H
#define CIVITAIIMAGEINFO_H

#include <QString>
#include <QPixmap>
#include <QJsonObject>
#include <QDateTime>
#include <QAbstractItemModel>
#include <QMetaType>

/**
 * @brief 图片统计信息结构体
 */
struct CivitaiImageStats {
    int cryCount = 0;
    int laughCount = 0;
    int likeCount = 0;
    int heartCount = 0;
    int commentCount = 0;
    
    // 构造函数
    CivitaiImageStats() = default;
    
    // 从JSON对象构造
    explicit CivitaiImageStats(const QJsonObject& statsJson);
    
    // 转换为JSON对象
    QJsonObject toJson() const;
};

/**
 * @brief Civitai图片信息主类
 * 
 * 存储从Civitai API获取的图片信息，包括基本属性、统计数据和元数据
 */
class CivitaiImageInfo
{
public:
    // 构造函数
    CivitaiImageInfo();
    explicit CivitaiImageInfo(const QJsonObject& apiResponse);
    
    // 拷贝构造和赋值
    CivitaiImageInfo(const CivitaiImageInfo& other);
    CivitaiImageInfo& operator=(const CivitaiImageInfo& other);
    
    // 移动构造和赋值
    CivitaiImageInfo(CivitaiImageInfo&& other) noexcept;
    CivitaiImageInfo& operator=(CivitaiImageInfo&& other) noexcept;
    
    // 析构函数
    ~CivitaiImageInfo();

    // 基本属性访问器
    int id() const { return m_id; }
    void setId(int id) { m_id = id; }
    
    QString url() const { return m_url; }
    void setUrl(const QString& url) { m_url = url; }
    
    QString thumbnailUrl() const { return m_thumbnailUrl; }
    void setThumbnailUrl(const QString& url) { m_thumbnailUrl = url; }
    
    int width() const { return m_width; }
    void setWidth(int width) { m_width = width; }
    
    int height() const { return m_height; }
    void setHeight(int height) { m_height = height; }
    
    QString nsfwLevel() const { return m_nsfwLevel; }
    void setNsfwLevel(const QString& level) { m_nsfwLevel = level; }
    
    bool isNsfw() const { return m_nsfw; }
    void setNsfw(bool nsfw) { m_nsfw = nsfw; }
    
    QDateTime createdAt() const { return m_createdAt; }
    void setCreatedAt(const QDateTime& dateTime) { m_createdAt = dateTime; }
    
    int postId() const { return m_postId; }
    void setPostId(int postId) { m_postId = postId; }
    
    QString authorUsername() const { return m_authorUsername; }
    void setAuthorUsername(const QString& username) { m_authorUsername = username; }
    
    CivitaiImageStats stats() const { return m_stats; }
    void setStats(const CivitaiImageStats& stats) { m_stats = stats; }

    // 图片相关
    QPixmap previewPixmap() const { return m_previewPixmap; }
    void setPreviewPixmap(const QPixmap& pixmap) { m_previewPixmap = pixmap; }
    bool isPreviewLoaded() const { return !m_previewPixmap.isNull(); }
    
    // 元数据相关
    QJsonObject metaData() const { return m_metaData; }
    void setMetaData(const QJsonObject& meta);
    
    QString formattedMetaJsonString() const { return m_formattedMetaJsonString; }
    
    // 树视图模型（按需生成）
    QAbstractItemModel* createMetaTreeModel(QObject* parent = nullptr) const;
    
    // 工具方法
    bool isValid() const { return m_id > 0; }
    QString displayTitle() const;
    QString sizeString() const;
    
    // 从API响应解析
    void parseFromApiResponse(const QJsonObject& apiResponse);
    
    // 转换为JSON（用于缓存等）
    QJsonObject toJson() const;

private:
    // 基本属性
    int m_id = 0;
    QString m_url;
    QString m_thumbnailUrl;
    int m_width = 0;
    int m_height = 0;
    QString m_nsfwLevel;
    bool m_nsfw = false;
    QDateTime m_createdAt;
    int m_postId = 0;
    QString m_authorUsername;
    CivitaiImageStats m_stats;
    
    // 图片数据
    QPixmap m_previewPixmap;
    
    // 元数据
    QJsonObject m_metaData;
    QString m_formattedMetaJsonString;
    
    // 私有方法
    void updateFormattedMetaString();
    void copyFrom(const CivitaiImageInfo& other);
    void moveFrom(CivitaiImageInfo&& other) noexcept;
};

// 注册元类型，用于Qt的信号槽系统
Q_DECLARE_METATYPE(CivitaiImageInfo)
Q_DECLARE_METATYPE(CivitaiImageStats)

#endif // CIVITAIIMAGEINFO_H
