{"classes": [{"className": "ElaToggleButton", "lineNumber": 8, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pBorderRadius", "notify": "pBorderRadiusChanged", "read": "getBorderRadius", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBorderRadius"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pText", "notify": "pTextChanged", "read": "getText", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}], "qualifiedClassName": "ElaToggleButton", "signals": [{"access": "public", "index": 0, "name": "pBorderRadiusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pTextChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "checked", "type": "bool"}], "index": 2, "name": "toggled", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "ElaToggleButton.h", "outputRevision": 69}