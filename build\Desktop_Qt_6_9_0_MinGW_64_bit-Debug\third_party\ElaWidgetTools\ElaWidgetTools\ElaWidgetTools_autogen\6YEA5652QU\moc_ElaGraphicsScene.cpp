/****************************************************************************
** Meta object code from reading C++ file 'ElaGraphicsScene.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaGraphicsScene.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaGraphicsScene.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN16ElaGraphicsSceneE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaGraphicsScene::qt_create_metaobjectdata<qt_meta_tag_ZN16ElaGraphicsSceneE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaGraphicsScene",
        "pIsCheckLinkPortChanged",
        "",
        "pSerializePathChanged",
        "showItemLink",
        "mouseLeftClickedItem",
        "ElaGraphicsItem*",
        "item",
        "mouseRightClickedItem",
        "mouseDoubleClickedItem",
        "pIsCheckLinkPort",
        "pSerializePath"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pIsCheckLinkPortChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pSerializePathChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'showItemLink'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'mouseLeftClickedItem'
        QtMocHelpers::SignalData<void(ElaGraphicsItem *)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 6, 7 },
        }}),
        // Signal 'mouseRightClickedItem'
        QtMocHelpers::SignalData<void(ElaGraphicsItem *)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 6, 7 },
        }}),
        // Signal 'mouseDoubleClickedItem'
        QtMocHelpers::SignalData<void(ElaGraphicsItem *)>(9, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 6, 7 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pIsCheckLinkPort'
        QtMocHelpers::PropertyData<bool>(10, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pSerializePath'
        QtMocHelpers::PropertyData<QString>(11, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 1),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaGraphicsScene, qt_meta_tag_ZN16ElaGraphicsSceneE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaGraphicsScene::staticMetaObject = { {
    QMetaObject::SuperData::link<QGraphicsScene::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16ElaGraphicsSceneE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16ElaGraphicsSceneE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN16ElaGraphicsSceneE_t>.metaTypes,
    nullptr
} };

void ElaGraphicsScene::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaGraphicsScene *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pIsCheckLinkPortChanged(); break;
        case 1: _t->pSerializePathChanged(); break;
        case 2: _t->showItemLink(); break;
        case 3: _t->mouseLeftClickedItem((*reinterpret_cast< std::add_pointer_t<ElaGraphicsItem*>>(_a[1]))); break;
        case 4: _t->mouseRightClickedItem((*reinterpret_cast< std::add_pointer_t<ElaGraphicsItem*>>(_a[1]))); break;
        case 5: _t->mouseDoubleClickedItem((*reinterpret_cast< std::add_pointer_t<ElaGraphicsItem*>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaGraphicsScene::*)()>(_a, &ElaGraphicsScene::pIsCheckLinkPortChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaGraphicsScene::*)()>(_a, &ElaGraphicsScene::pSerializePathChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaGraphicsScene::*)()>(_a, &ElaGraphicsScene::showItemLink, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaGraphicsScene::*)(ElaGraphicsItem * )>(_a, &ElaGraphicsScene::mouseLeftClickedItem, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaGraphicsScene::*)(ElaGraphicsItem * )>(_a, &ElaGraphicsScene::mouseRightClickedItem, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaGraphicsScene::*)(ElaGraphicsItem * )>(_a, &ElaGraphicsScene::mouseDoubleClickedItem, 5))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<bool*>(_v) = _t->getIsCheckLinkPort(); break;
        case 1: *reinterpret_cast<QString*>(_v) = _t->getSerializePath(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setIsCheckLinkPort(*reinterpret_cast<bool*>(_v)); break;
        case 1: _t->setSerializePath(*reinterpret_cast<QString*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaGraphicsScene::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaGraphicsScene::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16ElaGraphicsSceneE_t>.strings))
        return static_cast<void*>(this);
    return QGraphicsScene::qt_metacast(_clname);
}

int ElaGraphicsScene::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QGraphicsScene::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 6;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 2;
    }
    return _id;
}

// SIGNAL 0
void ElaGraphicsScene::pIsCheckLinkPortChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaGraphicsScene::pSerializePathChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaGraphicsScene::showItemLink()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaGraphicsScene::mouseLeftClickedItem(ElaGraphicsItem * _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1);
}

// SIGNAL 4
void ElaGraphicsScene::mouseRightClickedItem(ElaGraphicsItem * _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}

// SIGNAL 5
void ElaGraphicsScene::mouseDoubleClickedItem(ElaGraphicsItem * _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1);
}
QT_WARNING_POP
