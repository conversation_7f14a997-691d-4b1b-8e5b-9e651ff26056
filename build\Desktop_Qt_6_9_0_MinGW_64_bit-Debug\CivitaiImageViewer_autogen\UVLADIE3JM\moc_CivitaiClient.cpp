/****************************************************************************
** Meta object code from reading C++ file 'CivitaiClient.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/CivitaiClient.h"
#include <QtNetwork/QSslError>
#include <QtCore/qmetatype.h>
#include <QtCore/QList>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'CivitaiClient.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN13CivitaiClientE_t {};
} // unnamed namespace

template <> constexpr inline auto CivitaiClient::qt_create_metaobjectdata<qt_meta_tag_ZN13CivitaiClientE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "CivitaiClient",
        "imagesReceived",
        "",
        "QList<CivitaiImageInfo>",
        "images",
        "PaginationInfo",
        "pagination",
        "imageFetchFailed",
        "errorMessage",
        "httpStatusCode",
        "errorType",
        "requestProgress",
        "current",
        "total",
        "rateLimitWarning",
        "retryAfterSeconds",
        "onNetworkReply",
        "onRateLimitTimer",
        "processRequestQueue"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'imagesReceived'
        QtMocHelpers::SignalData<void(const QList<CivitaiImageInfo> &, const PaginationInfo &)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 3, 4 }, { 0x80000000 | 5, 6 },
        }}),
        // Signal 'imageFetchFailed'
        QtMocHelpers::SignalData<void(const QString &, int, const QString &)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 8 }, { QMetaType::Int, 9 }, { QMetaType::QString, 10 },
        }}),
        // Signal 'requestProgress'
        QtMocHelpers::SignalData<void(int, int)>(11, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 12 }, { QMetaType::Int, 13 },
        }}),
        // Signal 'rateLimitWarning'
        QtMocHelpers::SignalData<void(int)>(14, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 15 },
        }}),
        // Slot 'onNetworkReply'
        QtMocHelpers::SlotData<void()>(16, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onRateLimitTimer'
        QtMocHelpers::SlotData<void()>(17, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'processRequestQueue'
        QtMocHelpers::SlotData<void()>(18, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<CivitaiClient, qt_meta_tag_ZN13CivitaiClientE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject CivitaiClient::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13CivitaiClientE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13CivitaiClientE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN13CivitaiClientE_t>.metaTypes,
    nullptr
} };

void CivitaiClient::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<CivitaiClient *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->imagesReceived((*reinterpret_cast< std::add_pointer_t<QList<CivitaiImageInfo>>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<PaginationInfo>>(_a[2]))); break;
        case 1: _t->imageFetchFailed((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 2: _t->requestProgress((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 3: _t->rateLimitWarning((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 4: _t->onNetworkReply(); break;
        case 5: _t->onRateLimitTimer(); break;
        case 6: _t->processRequestQueue(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
        case 0:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 1:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< PaginationInfo >(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< QList<CivitaiImageInfo> >(); break;
            }
            break;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (CivitaiClient::*)(const QList<CivitaiImageInfo> & , const PaginationInfo & )>(_a, &CivitaiClient::imagesReceived, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (CivitaiClient::*)(const QString & , int , const QString & )>(_a, &CivitaiClient::imageFetchFailed, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (CivitaiClient::*)(int , int )>(_a, &CivitaiClient::requestProgress, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (CivitaiClient::*)(int )>(_a, &CivitaiClient::rateLimitWarning, 3))
            return;
    }
}

const QMetaObject *CivitaiClient::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CivitaiClient::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13CivitaiClientE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int CivitaiClient::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    }
    return _id;
}

// SIGNAL 0
void CivitaiClient::imagesReceived(const QList<CivitaiImageInfo> & _t1, const PaginationInfo & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1, _t2);
}

// SIGNAL 1
void CivitaiClient::imageFetchFailed(const QString & _t1, int _t2, const QString & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1, _t2, _t3);
}

// SIGNAL 2
void CivitaiClient::requestProgress(int _t1, int _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1, _t2);
}

// SIGNAL 3
void CivitaiClient::rateLimitWarning(int _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1);
}
QT_WARNING_POP
