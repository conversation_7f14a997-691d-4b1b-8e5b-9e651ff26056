#include <QApplication>
#include <QDir>
#include <QStandardPaths>
#include <QStyleFactory>
#include <QDebug>
#include <QMessageBox>
#include <QTranslator>
#include <QLibraryInfo>

#include "MainWindow.h"
#include "ConfigManager.h"

#ifdef ELAWIDGETTOOLS_AVAILABLE
#include "ElaApplication.h"
#include "ElaTheme.h"
#endif

/**
 * @brief 初始化应用程序目录
 */
void initializeAppDirectories()
{
    // 创建应用程序数据目录
    QString appDataDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(appDataDir);
    
    // 创建缓存目录
    QString cacheDir = QStandardPaths::writableLocation(QStandardPaths::CacheLocation);
    QDir().mkpath(cacheDir);
    
    // 创建配置目录
    QString configDir = QStandardPaths::writableLocation(QStandardPaths::ConfigLocation);
    QDir().mkpath(configDir);
    
    qDebug() << "App data directory:" << appDataDir;
    qDebug() << "Cache directory:" << cacheDir;
    qDebug() << "Config directory:" << configDir;
}

/**
 * @brief 设置应用程序样式
 */
void setupApplicationStyle(QApplication& app)
{
#ifdef ELAWIDGETTOOLS_AVAILABLE
    // 如果有ElaWidgetTools，使用其主题系统
    ElaTheme::getInstance()->setThemeMode(ElaThemeType::Light);
#else
    // 使用系统默认样式
    app.setStyle(QStyleFactory::create("Fusion"));
    
    // 设置暗色主题（可选）
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::WindowText, Qt::white);
    darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
    darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
    darkPalette.setColor(QPalette::ToolTipText, Qt::white);
    darkPalette.setColor(QPalette::Text, Qt::white);
    darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ButtonText, Qt::white);
    darkPalette.setColor(QPalette::BrightText, Qt::red);
    darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::HighlightedText, Qt::black);
    
    // 暂时使用亮色主题，用户可以在设置中切换
    // app.setPalette(darkPalette);
#endif
}

/**
 * @brief 设置国际化
 */
void setupInternationalization(QApplication& app)
{
    // 加载Qt自带的翻译文件
    QTranslator* qtTranslator = new QTranslator(&app);
    if (qtTranslator->load("qt_" + QLocale::system().name(),
                          QLibraryInfo::path(QLibraryInfo::TranslationsPath))) {
        app.installTranslator(qtTranslator);
    }
    
    // 加载应用程序翻译文件（如果存在）
    QTranslator* appTranslator = new QTranslator(&app);
    QString translationPath = QApplication::applicationDirPath() + "/translations";
    if (appTranslator->load("civitai_" + QLocale::system().name(), translationPath)) {
        app.installTranslator(appTranslator);
    }
}

/**
 * @brief 检查系统要求
 */
bool checkSystemRequirements()
{
    // 检查网络连接（简单检查）
    // 这里可以添加更多的系统要求检查
    
    return true;
}

/**
 * @brief 显示启动画面（可选）
 */
void showSplashScreen()
{
    // 如果有启动画面图片，可以在这里显示
    // QSplashScreen splash(QPixmap(":/images/splash.png"));
    // splash.show();
    // QApplication::processEvents();
}

int main(int argc, char *argv[])
{
    // 设置应用程序属性
    QApplication::setApplicationName("Civitai Image Viewer");
    QApplication::setApplicationVersion("1.0.0");
    QApplication::setApplicationDisplayName("Civitai 图片查看器");
    QApplication::setOrganizationName("CivitaiImageViewer");
    QApplication::setOrganizationDomain("civitai-image-viewer.local");
    
    // 启用高DPI支持
    QApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);

#ifdef ELAWIDGETTOOLS_AVAILABLE
    // 使用ElaApplication
    ElaApplication app(argc, argv);
#else
    // 使用标准QApplication
    QApplication app(argc, argv);
#endif

    // 检查系统要求
    if (!checkSystemRequirements()) {
        QMessageBox::critical(nullptr, "系统要求", 
                             "系统不满足运行要求，请检查网络连接和系统配置。");
        return -1;
    }

    // 初始化应用程序目录
    initializeAppDirectories();
    
    // 设置样式
    setupApplicationStyle(app);
    
    // 设置国际化
    setupInternationalization(app);
    
    // 显示启动画面（可选）
    showSplashScreen();
    
    try {
        // 创建并显示主窗口
        MainWindow window;
        window.show();
        
        qDebug() << "Application started successfully";
        
        // 运行应用程序事件循环
        return app.exec();
        
    } catch (const std::exception& e) {
        qCritical() << "Application error:" << e.what();
        QMessageBox::critical(nullptr, "应用程序错误", 
                             QString("应用程序遇到错误：%1").arg(e.what()));
        return -1;
    } catch (...) {
        qCritical() << "Unknown application error";
        QMessageBox::critical(nullptr, "应用程序错误", "应用程序遇到未知错误。");
        return -1;
    }
}
