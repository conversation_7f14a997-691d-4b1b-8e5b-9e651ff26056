{"classes": [{"className": "ElaMessageButton", "lineNumber": 10, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pBorderRadius", "notify": "pBorderRadiusChanged", "read": "getBorderRadius", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBorderRadius"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "p<PERSON>ar<PERSON><PERSON><PERSON>", "notify": "pBarTitleChanged", "read": "getBarTitle", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setBarTitle"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "pBarText", "notify": "pBarTextChanged", "read": "getBarText", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setBarText"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "pDisplayMsec", "notify": "pDisplayMsecChanged", "read": "getDisplayMsec", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setDisplayMsec"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "pMessageTargetWidget", "notify": "pMessageTargetWidgetChanged", "read": "getMessageTargetWidget", "required": false, "scriptable": true, "stored": true, "type": "QWidget*", "user": false, "write": "setMessageTargetWidget"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "pMessageMode", "notify": "pMessageModeChanged", "read": "getMessageMode", "required": false, "scriptable": true, "stored": true, "type": "ElaMessageBarType::MessageMode", "user": false, "write": "setMessageMode"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "pPositionPolicy", "notify": "pPositionPolicyChanged", "read": "getPositionPolicy", "required": false, "scriptable": true, "stored": true, "type": "ElaMessageBarType::PositionPolicy", "user": false, "write": "setPositionPolicy"}], "qualifiedClassName": "ElaMessageButton", "signals": [{"access": "public", "index": 0, "name": "pBorderRadiusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pBarTitleChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "pBarTextChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "pDisplayMsecChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "pMessageTargetWidgetChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "pMessageModeChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "pPositionPolicyChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPushButton"}]}], "inputFile": "ElaMessageButton.h", "outputRevision": 69}