{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["tests/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 64, "parent": 0}]}, "id": "run_tests::@a44f0ac069e85531cdee", "name": "run_tests", "paths": {"build": "tests", "source": "tests"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/tests/CMakeFiles/run_tests", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/tests/CMakeFiles/run_tests.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}