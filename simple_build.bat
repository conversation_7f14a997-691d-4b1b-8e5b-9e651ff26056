@echo off
echo 开始构建 Civitai 图片查看器...
echo.

REM 创建构建目录
if exist build rmdir /s /q build
mkdir build
cd build

echo 正在配置项目...
cmake .. -DCMAKE_BUILD_TYPE=Release

if %ERRORLEVEL% neq 0 (
    echo 配置失败，尝试指定生成器...
    cmake .. -DCMAKE_BUILD_TYPE=Release -G "Visual Studio 16 2019" -A x64
    
    if %ERRORLEVEL% neq 0 (
        cmake .. -DCMAKE_BUILD_TYPE=Release -G "Visual Studio 17 2022" -A x64
        
        if %ERRORLEVEL% neq 0 (
            echo 配置失败！请检查环境。
            pause
            exit /b 1
        )
    )
)

echo 正在构建项目...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo 构建失败！
    pause
    exit /b 1
)

echo.
echo 构建成功！
echo 查找可执行文件...

dir /s /b *.exe | findstr CivitaiImageViewer

echo.
echo 构建完成！请运行找到的 .exe 文件。
pause
