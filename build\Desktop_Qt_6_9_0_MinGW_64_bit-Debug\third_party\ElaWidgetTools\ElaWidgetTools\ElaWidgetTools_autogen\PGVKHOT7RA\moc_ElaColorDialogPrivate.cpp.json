{"classes": [{"className": "ElaColorDialogPrivate", "lineNumber": 24, "object": true, "qualifiedClassName": "ElaColorDialogPrivate", "slots": [{"access": "public", "arguments": [{"name": "selectedColor", "type": "QColor"}], "index": 0, "name": "onColorPickerColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "int"}], "index": 1, "name": "onColorValueSliderChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 2, "name": "onColorModeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 3, "name": "onHtmlEditFocusOut", "returnType": "void"}, {"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 4, "name": "onHtmlEditChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 5, "name": "onColorEditChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "QModelIndex"}], "index": 6, "name": "onBasicColorViewClicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "QModelIndex"}], "index": 7, "name": "onCustomColorViewClicked", "returnType": "void"}, {"access": "public", "index": 8, "name": "onAddCustomColorButtonClicked", "returnType": "void"}, {"access": "public", "index": 9, "name": "onRemoveCustomColorButtonClicked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "ElaColorDialogPrivate.h", "outputRevision": 69}