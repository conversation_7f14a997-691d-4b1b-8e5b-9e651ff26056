/****************************************************************************
** Meta object code from reading C++ file 'ElaNavigationBar.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaNavigationBar.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaNavigationBar.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN16ElaNavigationBarE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaNavigationBar::qt_create_metaobjectdata<qt_meta_tag_ZN16ElaNavigationBarE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaNavigationBar",
        "pIsTransparentChanged",
        "",
        "pIsAllowPageOpenInNewWindowChanged",
        "pageOpenInNewWindow",
        "nodeKey",
        "userInfoCardClicked",
        "navigationNodeClicked",
        "ElaNavigationType::NavigationNodeType",
        "nodeType",
        "navigationNodeAdded",
        "QWidget*",
        "page",
        "navigationNodeRemoved",
        "pIsTransparent",
        "pIsAllowPageOpenInNewWindow"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pIsTransparentChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pIsAllowPageOpenInNewWindowChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pageOpenInNewWindow'
        QtMocHelpers::SignalData<void(QString)>(4, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 5 },
        }}),
        // Signal 'userInfoCardClicked'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'navigationNodeClicked'
        QtMocHelpers::SignalData<void(ElaNavigationType::NavigationNodeType, QString)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 8, 9 }, { QMetaType::QString, 5 },
        }}),
        // Signal 'navigationNodeAdded'
        QtMocHelpers::SignalData<void(ElaNavigationType::NavigationNodeType, QString, QWidget *)>(10, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 8, 9 }, { QMetaType::QString, 5 }, { 0x80000000 | 11, 12 },
        }}),
        // Signal 'navigationNodeRemoved'
        QtMocHelpers::SignalData<void(ElaNavigationType::NavigationNodeType, QString)>(13, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 8, 9 }, { QMetaType::QString, 5 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pIsTransparent'
        QtMocHelpers::PropertyData<bool>(14, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pIsAllowPageOpenInNewWindow'
        QtMocHelpers::PropertyData<bool>(15, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 1),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaNavigationBar, qt_meta_tag_ZN16ElaNavigationBarE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaNavigationBar::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16ElaNavigationBarE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16ElaNavigationBarE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN16ElaNavigationBarE_t>.metaTypes,
    nullptr
} };

void ElaNavigationBar::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaNavigationBar *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pIsTransparentChanged(); break;
        case 1: _t->pIsAllowPageOpenInNewWindowChanged(); break;
        case 2: _t->pageOpenInNewWindow((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->userInfoCardClicked(); break;
        case 4: _t->navigationNodeClicked((*reinterpret_cast< std::add_pointer_t<ElaNavigationType::NavigationNodeType>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 5: _t->navigationNodeAdded((*reinterpret_cast< std::add_pointer_t<ElaNavigationType::NavigationNodeType>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QWidget*>>(_a[3]))); break;
        case 6: _t->navigationNodeRemoved((*reinterpret_cast< std::add_pointer_t<ElaNavigationType::NavigationNodeType>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
        case 5:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 2:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< QWidget* >(); break;
            }
            break;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaNavigationBar::*)()>(_a, &ElaNavigationBar::pIsTransparentChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaNavigationBar::*)()>(_a, &ElaNavigationBar::pIsAllowPageOpenInNewWindowChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaNavigationBar::*)(QString )>(_a, &ElaNavigationBar::pageOpenInNewWindow, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaNavigationBar::*)()>(_a, &ElaNavigationBar::userInfoCardClicked, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaNavigationBar::*)(ElaNavigationType::NavigationNodeType , QString )>(_a, &ElaNavigationBar::navigationNodeClicked, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaNavigationBar::*)(ElaNavigationType::NavigationNodeType , QString , QWidget * )>(_a, &ElaNavigationBar::navigationNodeAdded, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaNavigationBar::*)(ElaNavigationType::NavigationNodeType , QString )>(_a, &ElaNavigationBar::navigationNodeRemoved, 6))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<bool*>(_v) = _t->getIsTransparent(); break;
        case 1: *reinterpret_cast<bool*>(_v) = _t->getIsAllowPageOpenInNewWindow(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setIsTransparent(*reinterpret_cast<bool*>(_v)); break;
        case 1: _t->setIsAllowPageOpenInNewWindow(*reinterpret_cast<bool*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaNavigationBar::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaNavigationBar::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16ElaNavigationBarE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ElaNavigationBar::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 2;
    }
    return _id;
}

// SIGNAL 0
void ElaNavigationBar::pIsTransparentChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaNavigationBar::pIsAllowPageOpenInNewWindowChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaNavigationBar::pageOpenInNewWindow(QString _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1);
}

// SIGNAL 3
void ElaNavigationBar::userInfoCardClicked()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaNavigationBar::navigationNodeClicked(ElaNavigationType::NavigationNodeType _t1, QString _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1, _t2);
}

// SIGNAL 5
void ElaNavigationBar::navigationNodeAdded(ElaNavigationType::NavigationNodeType _t1, QString _t2, QWidget * _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1, _t2, _t3);
}

// SIGNAL 6
void ElaNavigationBar::navigationNodeRemoved(ElaNavigationType::NavigationNodeType _t1, QString _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 6, nullptr, _t1, _t2);
}
QT_WARNING_POP
