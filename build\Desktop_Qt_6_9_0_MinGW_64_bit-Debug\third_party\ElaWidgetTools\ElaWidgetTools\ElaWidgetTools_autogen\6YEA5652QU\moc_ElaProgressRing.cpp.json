{"classes": [{"className": "ElaProgressRing", "lineNumber": 9, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pIsBusying", "notify": "pIsBusyingChanged", "read": "getIsBusying", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setIsBusying"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pIs<PERSON><PERSON><PERSON><PERSON><PERSON>", "notify": "pIsTransparentChanged", "read": "getIsTransparent", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setIsTransparent"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "pIsDisplayValue", "notify": "pIsDisplayValueChanged", "read": "getIsDisplayValue", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setIsDisplayValue"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "pValueDisplayMode", "notify": "pValueDisplayModeChanged", "read": "getValueDisplayMode", "required": false, "scriptable": true, "stored": true, "type": "ElaProgressRingType::ValueDisplayMode", "user": false, "write": "setValueDisplayMode"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "pB<PERSON><PERSON><PERSON><PERSON><PERSON>", "notify": "pBusyingWidthChanged", "read": "getBusyingWidth", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "set<PERSON><PERSON><PERSON><PERSON>id<PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "pBusyingDurationTime", "notify": "pBusyingDurationTimeChanged", "read": "getBusyingDurationTime", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBusyingDurationTime"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "pMinimum", "notify": "pMinimumChanged", "read": "getMinimum", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMinimum"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "pMaximum", "notify": "pMaximumChanged", "read": "getMaximum", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMaximum"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "pValue", "notify": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "getValue", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setValue"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "pValuePixelSize", "notify": "pValuePixelSizeChanged", "read": "getValuePixelSize", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setValuePixelSize"}], "qualifiedClassName": "ElaProgressRing", "signals": [{"access": "public", "index": 0, "name": "pIsBusyingChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pIsTransparentChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "pIsDisplayValueChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "pValueDisplayModeChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "pBusyingWidthChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "pBusyingDurationTimeChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "pMinimumChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "pMaximumChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 9, "name": "pValuePixelSizeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "min", "type": "int"}, {"name": "max", "type": "int"}], "index": 10, "name": "rangeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "ElaProgressRing.h", "outputRevision": 69}