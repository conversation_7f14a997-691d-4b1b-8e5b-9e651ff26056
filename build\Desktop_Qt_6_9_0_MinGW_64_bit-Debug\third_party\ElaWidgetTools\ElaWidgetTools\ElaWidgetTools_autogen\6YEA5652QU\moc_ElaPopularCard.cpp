/****************************************************************************
** Meta object code from reading C++ file 'ElaPopularCard.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaPopularCard.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaPopularCard.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN14ElaPopularCardE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaPopularCard::qt_create_metaobjectdata<qt_meta_tag_ZN14ElaPopularCardE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaPopularCard",
        "pBorderRadiusChanged",
        "",
        "pCardPixmapChanged",
        "pTitleChanged",
        "pSubTitleChanged",
        "pInteractiveTipsChanged",
        "pDetailedTextChanged",
        "pCardButtontextChanged",
        "pCardFloatAreaChanged",
        "pCardFloatPixmapChanged",
        "popularCardClicked",
        "popularCardButtonClicked",
        "pBorderRadius",
        "pCardPixmap",
        "pTitle",
        "pSubTitle",
        "pInteractiveTips",
        "pDetailedText",
        "pCardButtontext",
        "pCardFloatArea",
        "QWidget*",
        "pCardFloatPixmap"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pBorderRadiusChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCardPixmapChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pTitleChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pSubTitleChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pInteractiveTipsChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pDetailedTextChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCardButtontextChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCardFloatAreaChanged'
        QtMocHelpers::SignalData<void()>(9, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCardFloatPixmapChanged'
        QtMocHelpers::SignalData<void()>(10, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'popularCardClicked'
        QtMocHelpers::SignalData<void()>(11, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'popularCardButtonClicked'
        QtMocHelpers::SignalData<void()>(12, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pBorderRadius'
        QtMocHelpers::PropertyData<int>(13, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pCardPixmap'
        QtMocHelpers::PropertyData<QPixmap>(14, QMetaType::QPixmap, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pTitle'
        QtMocHelpers::PropertyData<QString>(15, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pSubTitle'
        QtMocHelpers::PropertyData<QString>(16, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 3),
        // property 'pInteractiveTips'
        QtMocHelpers::PropertyData<QString>(17, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 4),
        // property 'pDetailedText'
        QtMocHelpers::PropertyData<QString>(18, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 5),
        // property 'pCardButtontext'
        QtMocHelpers::PropertyData<QString>(19, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 6),
        // property 'pCardFloatArea'
        QtMocHelpers::PropertyData<QWidget*>(20, 0x80000000 | 21, QMC::DefaultPropertyFlags | QMC::Writable | QMC::EnumOrFlag, 7),
        // property 'pCardFloatPixmap'
        QtMocHelpers::PropertyData<QPixmap>(22, QMetaType::QPixmap, QMC::DefaultPropertyFlags | QMC::Writable, 8),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaPopularCard, qt_meta_tag_ZN14ElaPopularCardE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaPopularCard::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14ElaPopularCardE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14ElaPopularCardE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN14ElaPopularCardE_t>.metaTypes,
    nullptr
} };

void ElaPopularCard::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaPopularCard *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pBorderRadiusChanged(); break;
        case 1: _t->pCardPixmapChanged(); break;
        case 2: _t->pTitleChanged(); break;
        case 3: _t->pSubTitleChanged(); break;
        case 4: _t->pInteractiveTipsChanged(); break;
        case 5: _t->pDetailedTextChanged(); break;
        case 6: _t->pCardButtontextChanged(); break;
        case 7: _t->pCardFloatAreaChanged(); break;
        case 8: _t->pCardFloatPixmapChanged(); break;
        case 9: _t->popularCardClicked(); break;
        case 10: _t->popularCardButtonClicked(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaPopularCard::*)()>(_a, &ElaPopularCard::pBorderRadiusChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPopularCard::*)()>(_a, &ElaPopularCard::pCardPixmapChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPopularCard::*)()>(_a, &ElaPopularCard::pTitleChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPopularCard::*)()>(_a, &ElaPopularCard::pSubTitleChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPopularCard::*)()>(_a, &ElaPopularCard::pInteractiveTipsChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPopularCard::*)()>(_a, &ElaPopularCard::pDetailedTextChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPopularCard::*)()>(_a, &ElaPopularCard::pCardButtontextChanged, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPopularCard::*)()>(_a, &ElaPopularCard::pCardFloatAreaChanged, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPopularCard::*)()>(_a, &ElaPopularCard::pCardFloatPixmapChanged, 8))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPopularCard::*)()>(_a, &ElaPopularCard::popularCardClicked, 9))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPopularCard::*)()>(_a, &ElaPopularCard::popularCardButtonClicked, 10))
            return;
    }
    if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 7:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QWidget* >(); break;
        }
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = _t->getBorderRadius(); break;
        case 1: *reinterpret_cast<QPixmap*>(_v) = _t->getCardPixmap(); break;
        case 2: *reinterpret_cast<QString*>(_v) = _t->getTitle(); break;
        case 3: *reinterpret_cast<QString*>(_v) = _t->getSubTitle(); break;
        case 4: *reinterpret_cast<QString*>(_v) = _t->getInteractiveTips(); break;
        case 5: *reinterpret_cast<QString*>(_v) = _t->getDetailedText(); break;
        case 6: *reinterpret_cast<QString*>(_v) = _t->getCardButtontext(); break;
        case 7: *reinterpret_cast<QWidget**>(_v) = _t->getCardFloatArea(); break;
        case 8: *reinterpret_cast<QPixmap*>(_v) = _t->getCardFloatPixmap(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setBorderRadius(*reinterpret_cast<int*>(_v)); break;
        case 1: _t->setCardPixmap(*reinterpret_cast<QPixmap*>(_v)); break;
        case 2: _t->setTitle(*reinterpret_cast<QString*>(_v)); break;
        case 3: _t->setSubTitle(*reinterpret_cast<QString*>(_v)); break;
        case 4: _t->setInteractiveTips(*reinterpret_cast<QString*>(_v)); break;
        case 5: _t->setDetailedText(*reinterpret_cast<QString*>(_v)); break;
        case 6: _t->setCardButtontext(*reinterpret_cast<QString*>(_v)); break;
        case 7: _t->setCardFloatArea(*reinterpret_cast<QWidget**>(_v)); break;
        case 8: _t->setCardFloatPixmap(*reinterpret_cast<QPixmap*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaPopularCard::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaPopularCard::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14ElaPopularCardE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ElaPopularCard::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 11)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 11;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    }
    return _id;
}

// SIGNAL 0
void ElaPopularCard::pBorderRadiusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaPopularCard::pCardPixmapChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaPopularCard::pTitleChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaPopularCard::pSubTitleChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaPopularCard::pInteractiveTipsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ElaPopularCard::pDetailedTextChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void ElaPopularCard::pCardButtontextChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void ElaPopularCard::pCardFloatAreaChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}

// SIGNAL 8
void ElaPopularCard::pCardFloatPixmapChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 8, nullptr);
}

// SIGNAL 9
void ElaPopularCard::popularCardClicked()
{
    QMetaObject::activate(this, &staticMetaObject, 9, nullptr);
}

// SIGNAL 10
void ElaPopularCard::popularCardButtonClicked()
{
    QMetaObject::activate(this, &staticMetaObject, 10, nullptr);
}
QT_WARNING_POP
