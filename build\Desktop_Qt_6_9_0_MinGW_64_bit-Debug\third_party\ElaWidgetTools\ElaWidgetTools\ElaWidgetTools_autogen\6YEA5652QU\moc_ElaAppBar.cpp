/****************************************************************************
** Meta object code from reading C++ file 'ElaAppBar.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaAppBar.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaAppBar.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN9ElaAppBarE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaAppBar::qt_create_metaobjectdata<qt_meta_tag_ZN9ElaAppBarE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaAppBar",
        "pIsStayTopChanged",
        "",
        "pIsFixedSizeChanged",
        "pIsDefaultClosedChanged",
        "pIsOnlyAllowMinAndCloseChanged",
        "pAppBarHeightChanged",
        "pCustomWidgetMaximumWidthChanged",
        "routeBackButtonClicked",
        "navigationButtonClicked",
        "themeChangeButtonClicked",
        "closeButtonClicked",
        "customWidgetChanged",
        "pIsStayTop",
        "pIsFixedSize",
        "pIsDefaultClosed",
        "pIsOnlyAllowMinAndClose",
        "pAppBarHeight",
        "pCustomWidgetMaximumWidth"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pIsStayTopChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pIsFixedSizeChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pIsDefaultClosedChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pIsOnlyAllowMinAndCloseChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pAppBarHeightChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCustomWidgetMaximumWidthChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'routeBackButtonClicked'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'navigationButtonClicked'
        QtMocHelpers::SignalData<void()>(9, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'themeChangeButtonClicked'
        QtMocHelpers::SignalData<void()>(10, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'closeButtonClicked'
        QtMocHelpers::SignalData<void()>(11, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'customWidgetChanged'
        QtMocHelpers::SignalData<void()>(12, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pIsStayTop'
        QtMocHelpers::PropertyData<bool>(13, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pIsFixedSize'
        QtMocHelpers::PropertyData<bool>(14, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pIsDefaultClosed'
        QtMocHelpers::PropertyData<bool>(15, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pIsOnlyAllowMinAndClose'
        QtMocHelpers::PropertyData<bool>(16, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 3),
        // property 'pAppBarHeight'
        QtMocHelpers::PropertyData<int>(17, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 4),
        // property 'pCustomWidgetMaximumWidth'
        QtMocHelpers::PropertyData<int>(18, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 5),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaAppBar, qt_meta_tag_ZN9ElaAppBarE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaAppBar::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9ElaAppBarE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9ElaAppBarE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN9ElaAppBarE_t>.metaTypes,
    nullptr
} };

void ElaAppBar::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaAppBar *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pIsStayTopChanged(); break;
        case 1: _t->pIsFixedSizeChanged(); break;
        case 2: _t->pIsDefaultClosedChanged(); break;
        case 3: _t->pIsOnlyAllowMinAndCloseChanged(); break;
        case 4: _t->pAppBarHeightChanged(); break;
        case 5: _t->pCustomWidgetMaximumWidthChanged(); break;
        case 6: _t->routeBackButtonClicked(); break;
        case 7: _t->navigationButtonClicked(); break;
        case 8: _t->themeChangeButtonClicked(); break;
        case 9: _t->closeButtonClicked(); break;
        case 10: _t->customWidgetChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaAppBar::*)()>(_a, &ElaAppBar::pIsStayTopChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAppBar::*)()>(_a, &ElaAppBar::pIsFixedSizeChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAppBar::*)()>(_a, &ElaAppBar::pIsDefaultClosedChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAppBar::*)()>(_a, &ElaAppBar::pIsOnlyAllowMinAndCloseChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAppBar::*)()>(_a, &ElaAppBar::pAppBarHeightChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAppBar::*)()>(_a, &ElaAppBar::pCustomWidgetMaximumWidthChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAppBar::*)()>(_a, &ElaAppBar::routeBackButtonClicked, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAppBar::*)()>(_a, &ElaAppBar::navigationButtonClicked, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAppBar::*)()>(_a, &ElaAppBar::themeChangeButtonClicked, 8))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAppBar::*)()>(_a, &ElaAppBar::closeButtonClicked, 9))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAppBar::*)()>(_a, &ElaAppBar::customWidgetChanged, 10))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<bool*>(_v) = _t->getIsStayTop(); break;
        case 1: *reinterpret_cast<bool*>(_v) = _t->getIsFixedSize(); break;
        case 2: *reinterpret_cast<bool*>(_v) = _t->getIsDefaultClosed(); break;
        case 3: *reinterpret_cast<bool*>(_v) = _t->getIsOnlyAllowMinAndClose(); break;
        case 4: *reinterpret_cast<int*>(_v) = _t->getAppBarHeight(); break;
        case 5: *reinterpret_cast<int*>(_v) = _t->getCustomWidgetMaximumWidth(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setIsStayTop(*reinterpret_cast<bool*>(_v)); break;
        case 1: _t->setIsFixedSize(*reinterpret_cast<bool*>(_v)); break;
        case 2: _t->setIsDefaultClosed(*reinterpret_cast<bool*>(_v)); break;
        case 3: _t->setIsOnlyAllowMinAndClose(*reinterpret_cast<bool*>(_v)); break;
        case 4: _t->setAppBarHeight(*reinterpret_cast<int*>(_v)); break;
        case 5: _t->setCustomWidgetMaximumWidth(*reinterpret_cast<int*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaAppBar::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaAppBar::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9ElaAppBarE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ElaAppBar::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 11)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 11;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    }
    return _id;
}

// SIGNAL 0
void ElaAppBar::pIsStayTopChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaAppBar::pIsFixedSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaAppBar::pIsDefaultClosedChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaAppBar::pIsOnlyAllowMinAndCloseChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaAppBar::pAppBarHeightChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ElaAppBar::pCustomWidgetMaximumWidthChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void ElaAppBar::routeBackButtonClicked()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void ElaAppBar::navigationButtonClicked()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}

// SIGNAL 8
void ElaAppBar::themeChangeButtonClicked()
{
    QMetaObject::activate(this, &staticMetaObject, 8, nullptr);
}

// SIGNAL 9
void ElaAppBar::closeButtonClicked()
{
    QMetaObject::activate(this, &staticMetaObject, 9, nullptr);
}

// SIGNAL 10
void ElaAppBar::customWidgetChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 10, nullptr);
}
QT_WARNING_POP
