#include "ImageCardWidget.h"
#include <QPainter>
#include <QStyleOption>
#include <QApplication>
#include <QPalette>
#include <QFontMetrics>
#include <QDebug>

// 静态常量定义
const int ImageCardWidget::DEFAULT_CARD_WIDTH = 280;
const int ImageCardWidget::DEFAULT_CARD_HEIGHT = 360;
const int ImageCardWidget::IMAGE_HEIGHT = 240;
const int ImageCardWidget::INFO_HEIGHT = 120;
const int ImageCardWidget::BORDER_WIDTH = 2;
const int ImageCardWidget::BORDER_RADIUS = 8;
const int ImageCardWidget::MARGIN = 8;
const int ImageCardWidget::SPACING = 4;

ImageCardWidget::ImageCardWidget(QWidget *parent)
    : QFrame(parent)
    , m_isLoading(false)
    , m_isSelected(false)
    , m_cardSize(DEFAULT_CARD_WIDTH, DEFAULT_CARD_HEIGHT)
    , m_mainLayout(nullptr)
    , m_imageLabel(nullptr)
    , m_titleLabel(nullptr)
    , m_authorLabel(nullptr)
    , m_sizeLabel(nullptr)
    , m_statsLabel(nullptr)
    , m_nsfwLabel(nullptr)
    , m_infoWidget(nullptr)
    , m_infoLayout(nullptr)
    , m_isHovered(false)
{
    initializeUI();
    applyStyle();
}

ImageCardWidget::ImageCardWidget(const CivitaiImageInfo& imageInfo, QWidget *parent)
    : ImageCardWidget(parent)
{
    setImageInfo(imageInfo);
}

ImageCardWidget::~ImageCardWidget()
{
    // Qt会自动清理子组件
}

void ImageCardWidget::setImageInfo(const CivitaiImageInfo& imageInfo)
{
    m_imageInfo = imageInfo;
    updateDisplay();

    // 如果图片未加载，请求加载
    if (!m_imageInfo.isPreviewLoaded() && !m_imageInfo.url().isEmpty()) {
        requestImageLoad();
    }
}

void ImageCardWidget::setPixmap(const QPixmap& pixmap)
{
    if (m_imageLabel) {
        // 缩放图片以适应标签大小
        QSize labelSize = m_imageLabel->size();
        if (labelSize.isEmpty()) {
            labelSize = QSize(m_cardSize.width() - 2 * MARGIN, IMAGE_HEIGHT);
        }

        QPixmap scaledPixmap = pixmap.scaled(labelSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);
        m_imageLabel->setPixmap(scaledPixmap);

        // 更新图片信息中的预览图
        m_imageInfo.setPreviewPixmap(pixmap);
    }

    setLoading(false);
}

QPixmap ImageCardWidget::pixmap() const
{
    if (m_imageLabel) {
        // Qt 6: pixmap() returns QPixmap, not QPixmap*
        QPixmap pixmap = m_imageLabel->pixmap();
        return pixmap;
    }
    return QPixmap();
}

void ImageCardWidget::setLoading(bool loading)
{
    if (m_isLoading == loading) {
        return;
    }

    m_isLoading = loading;
    updateImageDisplay();
}

void ImageCardWidget::setError(const QString& errorMessage)
{
    m_errorMessage = errorMessage;
    m_isLoading = false;
    updateImageDisplay();
}

void ImageCardWidget::clearError()
{
    m_errorMessage.clear();
    updateImageDisplay();
}

void ImageCardWidget::setCardSize(const QSize& size)
{
    m_cardSize = size;
    setFixedSize(size);
    updateDisplay();
}

QSize ImageCardWidget::cardSize() const
{
    return m_cardSize;
}

void ImageCardWidget::setSelected(bool selected)
{
    if (m_isSelected == selected) {
        return;
    }

    m_isSelected = selected;
    applyStyle();
    update();
}

void ImageCardWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        emit cardClicked(m_imageInfo);
    }
    QFrame::mousePressEvent(event);
}

void ImageCardWidget::enterEvent(QEnterEvent *event)
{
    m_isHovered = true;
    applyStyle();
    update();
    QFrame::enterEvent(event);
}

void ImageCardWidget::leaveEvent(QEvent *event)
{
    m_isHovered = false;
    applyStyle();
    update();
    QFrame::leaveEvent(event);
}

void ImageCardWidget::paintEvent(QPaintEvent *event)
{
    QFrame::paintEvent(event);

    // 绘制自定义边框和背景
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    QRect rect = this->rect().adjusted(BORDER_WIDTH/2, BORDER_WIDTH/2, -BORDER_WIDTH/2, -BORDER_WIDTH/2);

    // 绘制背景
    painter.setBrush(m_backgroundColor);
    painter.setPen(Qt::NoPen);
    painter.drawRoundedRect(rect, BORDER_RADIUS, BORDER_RADIUS);

    // 绘制边框
    QColor borderColor = m_normalBorderColor;
    if (m_isSelected) {
        borderColor = m_selectedBorderColor;
    } else if (m_isHovered) {
        borderColor = m_hoverBorderColor;
    }

    painter.setBrush(Qt::NoBrush);
    painter.setPen(QPen(borderColor, BORDER_WIDTH));
    painter.drawRoundedRect(rect, BORDER_RADIUS, BORDER_RADIUS);
}

void ImageCardWidget::resizeEvent(QResizeEvent *event)
{
    QFrame::resizeEvent(event);
    updateImageDisplay();
}

void ImageCardWidget::initializeUI()
{
    setFixedSize(m_cardSize);
    setFrameStyle(QFrame::NoFrame);

    // 主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(MARGIN, MARGIN, MARGIN, MARGIN);
    m_mainLayout->setSpacing(SPACING);

    // 图片标签
    m_imageLabel = new QLabel();
    m_imageLabel->setFixedHeight(IMAGE_HEIGHT);
    m_imageLabel->setAlignment(Qt::AlignCenter);
    m_imageLabel->setStyleSheet("QLabel { border: 1px solid #ddd; border-radius: 4px; background-color: #f5f5f5; }");
    m_imageLabel->setScaledContents(false);
    m_mainLayout->addWidget(m_imageLabel);

    // 信息区域
    m_infoWidget = new QWidget();
    m_infoWidget->setFixedHeight(INFO_HEIGHT);
    m_infoLayout = new QVBoxLayout(m_infoWidget);
    m_infoLayout->setContentsMargins(0, 0, 0, 0);
    m_infoLayout->setSpacing(2);

    // 标题标签
    m_titleLabel = new QLabel();
    m_titleLabel->setWordWrap(true);
    m_titleLabel->setMaximumHeight(40);
    QFont titleFont = m_titleLabel->font();
    titleFont.setBold(true);
    titleFont.setPointSize(titleFont.pointSize() + 1);
    m_titleLabel->setFont(titleFont);
    m_infoLayout->addWidget(m_titleLabel);

    // 作者标签
    m_authorLabel = new QLabel();
    m_authorLabel->setMaximumHeight(20);
    QFont authorFont = m_authorLabel->font();
    authorFont.setPointSize(authorFont.pointSize() - 1);
    m_authorLabel->setFont(authorFont);
    m_authorLabel->setStyleSheet("QLabel { color: #666; }");
    m_infoLayout->addWidget(m_authorLabel);

    // 尺寸标签
    m_sizeLabel = new QLabel();
    m_sizeLabel->setMaximumHeight(20);
    m_sizeLabel->setFont(authorFont);
    m_sizeLabel->setStyleSheet("QLabel { color: #888; }");
    m_infoLayout->addWidget(m_sizeLabel);

    // 统计标签
    m_statsLabel = new QLabel();
    m_statsLabel->setMaximumHeight(20);
    m_statsLabel->setFont(authorFont);
    m_statsLabel->setStyleSheet("QLabel { color: #888; }");
    m_infoLayout->addWidget(m_statsLabel);

    // NSFW标签
    m_nsfwLabel = new QLabel();
    m_nsfwLabel->setMaximumHeight(20);
    m_nsfwLabel->setFont(authorFont);
    m_nsfwLabel->setStyleSheet("QLabel { color: #ff6b6b; font-weight: bold; }");
    m_nsfwLabel->hide(); // 默认隐藏
    m_infoLayout->addWidget(m_nsfwLabel);

    m_infoLayout->addStretch();
    m_mainLayout->addWidget(m_infoWidget);

    // 设置初始显示
    updateDisplay();
}

void ImageCardWidget::updateDisplay()
{
    updateImageDisplay();
    updateInfoDisplay();
}

void ImageCardWidget::updateImageDisplay()
{
    if (!m_imageLabel) {
        return;
    }

    QSize imageSize(m_cardSize.width() - 2 * MARGIN, IMAGE_HEIGHT);

    if (!m_errorMessage.isEmpty()) {
        // 显示错误图片
        QPixmap errorPixmap = createErrorPixmap(imageSize);
        m_imageLabel->setPixmap(errorPixmap);
    } else if (m_isLoading) {
        // 显示加载图片
        QPixmap loadingPixmap = createLoadingPixmap(imageSize);
        m_imageLabel->setPixmap(loadingPixmap);
    } else if (m_imageInfo.isPreviewLoaded()) {
        // 显示实际图片
        QPixmap scaledPixmap = m_imageInfo.previewPixmap().scaled(imageSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);
        m_imageLabel->setPixmap(scaledPixmap);
    } else {
        // 显示占位符
        QPixmap placeholderPixmap = createPlaceholderPixmap(imageSize);
        m_imageLabel->setPixmap(placeholderPixmap);
    }
}

void ImageCardWidget::updateInfoDisplay()
{
    if (!m_imageInfo.isValid()) {
        // 清空所有信息
        if (m_titleLabel) m_titleLabel->clear();
        if (m_authorLabel) m_authorLabel->clear();
        if (m_sizeLabel) m_sizeLabel->clear();
        if (m_statsLabel) m_statsLabel->clear();
        if (m_nsfwLabel) m_nsfwLabel->hide();
        return;
    }

    // 更新标题
    if (m_titleLabel) {
        QString title = m_imageInfo.displayTitle();
        m_titleLabel->setText(title);
        m_titleLabel->setToolTip(title);
    }

    // 更新作者
    if (m_authorLabel) {
        QString author = m_imageInfo.authorUsername();
        if (!author.isEmpty()) {
            m_authorLabel->setText(QString("by %1").arg(author));
            m_authorLabel->show();
        } else {
            m_authorLabel->hide();
        }
    }

    // 更新尺寸
    if (m_sizeLabel) {
        QString sizeStr = m_imageInfo.sizeString();
        if (!sizeStr.isEmpty()) {
            m_sizeLabel->setText(sizeStr);
            m_sizeLabel->show();
        } else {
            m_sizeLabel->hide();
        }
    }

    // 更新统计信息
    if (m_statsLabel) {
        QString statsStr = formatStats();
        if (!statsStr.isEmpty()) {
            m_statsLabel->setText(statsStr);
            m_statsLabel->show();
        } else {
            m_statsLabel->hide();
        }
    }

    // 更新NSFW标签
    if (m_nsfwLabel) {
        if (m_imageInfo.isNsfw() && m_imageInfo.nsfwLevel() != "None") {
            m_nsfwLabel->setText(QString("NSFW: %1").arg(m_imageInfo.nsfwLevel()));
            m_nsfwLabel->show();
        } else {
            m_nsfwLabel->hide();
        }
    }
}

void ImageCardWidget::applyStyle()
{
    // 获取当前主题颜色
    QPalette palette = QApplication::palette();
    m_backgroundColor = palette.color(QPalette::Base);
    m_textColor = palette.color(QPalette::Text);
    m_normalBorderColor = palette.color(QPalette::Mid);
    m_hoverBorderColor = palette.color(QPalette::Highlight).lighter(150);
    m_selectedBorderColor = palette.color(QPalette::Highlight);

    // 应用样式到子组件
    QString baseStyle = QString("QLabel { color: %1; }").arg(m_textColor.name());

    if (m_titleLabel) {
        m_titleLabel->setStyleSheet(baseStyle);
    }
}

void ImageCardWidget::requestImageLoad()
{
    if (m_imageInfo.isValid() && !m_imageInfo.url().isEmpty()) {
        setLoading(true);
        emit imageLoadRequested(m_imageInfo.id(), m_imageInfo.url());
    }
}

QPixmap ImageCardWidget::createPlaceholderPixmap(const QSize& size) const
{
    QPixmap pixmap(size);
    pixmap.fill(QColor(240, 240, 240));

    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing);

    // 绘制占位符图标
    QRect iconRect = pixmap.rect();
    iconRect.setSize(QSize(64, 64));
    iconRect.moveCenter(pixmap.rect().center());

    painter.setPen(QPen(QColor(180, 180, 180), 2));
    painter.setBrush(Qt::NoBrush);
    painter.drawRoundedRect(iconRect, 8, 8);

    // 绘制图片图标
    QRect innerRect = iconRect.adjusted(16, 16, -16, -16);
    painter.drawEllipse(innerRect.topLeft() + QPoint(8, 8), 8, 8);
    painter.drawLine(innerRect.bottomLeft() + QPoint(0, -8),
                    innerRect.bottomRight() + QPoint(0, -8));

    return pixmap;
}

QPixmap ImageCardWidget::createLoadingPixmap(const QSize& size) const
{
    QPixmap pixmap(size);
    pixmap.fill(QColor(250, 250, 250));

    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing);

    // 绘制加载动画（简单的旋转圆圈）
    QRect rect(0, 0, 32, 32);
    rect.moveCenter(pixmap.rect().center());

    painter.setPen(QPen(QColor(100, 150, 255), 3));
    painter.setBrush(Qt::NoBrush);

    // 绘制部分圆弧表示加载
    painter.drawArc(rect, 0, 270 * 16); // 270度的圆弧

    // 添加文本
    painter.setPen(QColor(120, 120, 120));
    painter.drawText(pixmap.rect(), Qt::AlignCenter | Qt::TextWordWrap, "Loading...");

    return pixmap;
}

QPixmap ImageCardWidget::createErrorPixmap(const QSize& size) const
{
    QPixmap pixmap(size);
    pixmap.fill(QColor(255, 245, 245));

    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing);

    // 绘制错误图标
    QRect iconRect(0, 0, 48, 48);
    iconRect.moveCenter(pixmap.rect().center());
    iconRect.moveTop(iconRect.top() - 20);

    painter.setPen(QPen(QColor(255, 100, 100), 3));
    painter.setBrush(Qt::NoBrush);
    painter.drawEllipse(iconRect);

    // 绘制X
    int margin = 12;
    painter.drawLine(iconRect.topLeft() + QPoint(margin, margin),
                    iconRect.bottomRight() - QPoint(margin, margin));
    painter.drawLine(iconRect.topRight() + QPoint(-margin, margin),
                    iconRect.bottomLeft() + QPoint(margin, -margin));

    // 添加错误文本
    painter.setPen(QColor(180, 60, 60));
    QRect textRect = pixmap.rect();
    textRect.setTop(iconRect.bottom() + 10);
    painter.drawText(textRect, Qt::AlignCenter | Qt::TextWordWrap,
                    m_errorMessage.isEmpty() ? "Load Failed" : m_errorMessage);

    return pixmap;
}

QString ImageCardWidget::formatStats() const
{
    CivitaiImageStats stats = m_imageInfo.stats();
    QStringList statsList;

    if (stats.likeCount > 0) {
        statsList << QString("❤ %1").arg(stats.likeCount);
    }
    if (stats.commentCount > 0) {
        statsList << QString("💬 %1").arg(stats.commentCount);
    }

    return statsList.join(" ");
}
