/****************************************************************************
** Meta object code from reading C++ file 'ElaImageCard.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaImageCard.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaImageCard.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN12ElaImageCardE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaImageCard::qt_create_metaobjectdata<qt_meta_tag_ZN12ElaImageCardE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaImageCard",
        "pCardImageChanged",
        "",
        "pBorderRadiusChanged",
        "pIsPreserveAspectCropChanged",
        "pMaximumAspectRatioChanged",
        "pCardImage",
        "pBorderRadius",
        "pIsPreserveAspectCrop",
        "pMaximumAspectRatio"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pCardImageChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pBorderRadiusChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pIsPreserveAspectCropChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pMaximumAspectRatioChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pCardImage'
        QtMocHelpers::PropertyData<QImage>(6, QMetaType::QImage, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pBorderRadius'
        QtMocHelpers::PropertyData<int>(7, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pIsPreserveAspectCrop'
        QtMocHelpers::PropertyData<bool>(8, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pMaximumAspectRatio'
        QtMocHelpers::PropertyData<qreal>(9, QMetaType::QReal, QMC::DefaultPropertyFlags | QMC::Writable, 3),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaImageCard, qt_meta_tag_ZN12ElaImageCardE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaImageCard::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN12ElaImageCardE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN12ElaImageCardE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN12ElaImageCardE_t>.metaTypes,
    nullptr
} };

void ElaImageCard::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaImageCard *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pCardImageChanged(); break;
        case 1: _t->pBorderRadiusChanged(); break;
        case 2: _t->pIsPreserveAspectCropChanged(); break;
        case 3: _t->pMaximumAspectRatioChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaImageCard::*)()>(_a, &ElaImageCard::pCardImageChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaImageCard::*)()>(_a, &ElaImageCard::pBorderRadiusChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaImageCard::*)()>(_a, &ElaImageCard::pIsPreserveAspectCropChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaImageCard::*)()>(_a, &ElaImageCard::pMaximumAspectRatioChanged, 3))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<QImage*>(_v) = _t->getCardImage(); break;
        case 1: *reinterpret_cast<int*>(_v) = _t->getBorderRadius(); break;
        case 2: *reinterpret_cast<bool*>(_v) = _t->getIsPreserveAspectCrop(); break;
        case 3: *reinterpret_cast<qreal*>(_v) = _t->getMaximumAspectRatio(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setCardImage(*reinterpret_cast<QImage*>(_v)); break;
        case 1: _t->setBorderRadius(*reinterpret_cast<int*>(_v)); break;
        case 2: _t->setIsPreserveAspectCrop(*reinterpret_cast<bool*>(_v)); break;
        case 3: _t->setMaximumAspectRatio(*reinterpret_cast<qreal*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaImageCard::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaImageCard::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN12ElaImageCardE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ElaImageCard::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 4;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void ElaImageCard::pCardImageChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaImageCard::pBorderRadiusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaImageCard::pIsPreserveAspectCropChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaImageCard::pMaximumAspectRatioChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}
QT_WARNING_POP
