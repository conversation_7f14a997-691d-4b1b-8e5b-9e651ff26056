﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{C843D979-85F5-34D8-95EB-F29F4145A46C}"
	ProjectSection(ProjectDependencies) = postProject
		{E373B025-08E8-3046-A048-3D2157436208} = {E373B025-08E8-3046-A048-3D2157436208}
		{119E5808-FF94-3275-B96F-D93D7569C57B} = {119E5808-FF94-3275-B96F-D93D7569C57B}
		{442297F5-2886-3548-A699-3DC346C07EB5} = {442297F5-2886-3548-A699-3DC346C07EB5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ElaWidgetTools", "ElaWidgetTools\ElaWidgetTools.vcxproj", "{E373B025-08E8-3046-A048-3D2157436208}"
	ProjectSection(ProjectDependencies) = postProject
		{2B00ACB5-DE28-3672-8636-F28ADBA637DD} = {2B00ACB5-DE28-3672-8636-F28ADBA637DD}
		{442297F5-2886-3548-A699-3DC346C07EB5} = {442297F5-2886-3548-A699-3DC346C07EB5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ElaWidgetToolsExample", "ElaWidgetToolsExample\ElaWidgetToolsExample.vcxproj", "{119E5808-FF94-3275-B96F-D93D7569C57B}"
	ProjectSection(ProjectDependencies) = postProject
		{E373B025-08E8-3046-A048-3D2157436208} = {E373B025-08E8-3046-A048-3D2157436208}
		{442297F5-2886-3548-A699-3DC346C07EB5} = {442297F5-2886-3548-A699-3DC346C07EB5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ElaWidgetTools_automoc_json_extraction", "ElaWidgetTools\ElaWidgetTools_automoc_json_extraction.vcxproj", "{2B00ACB5-DE28-3672-8636-F28ADBA637DD}"
	ProjectSection(ProjectDependencies) = postProject
		{442297F5-2886-3548-A699-3DC346C07EB5} = {442297F5-2886-3548-A699-3DC346C07EB5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{2846A51D-759F-30E6-9397-E8D3B42936AA}"
	ProjectSection(ProjectDependencies) = postProject
		{C843D979-85F5-34D8-95EB-F29F4145A46C} = {C843D979-85F5-34D8-95EB-F29F4145A46C}
		{442297F5-2886-3548-A699-3DC346C07EB5} = {442297F5-2886-3548-A699-3DC346C07EB5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{442297F5-2886-3548-A699-3DC346C07EB5}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C843D979-85F5-34D8-95EB-F29F4145A46C}.Debug|x64.ActiveCfg = Debug|x64
		{C843D979-85F5-34D8-95EB-F29F4145A46C}.Debug|x64.Build.0 = Debug|x64
		{C843D979-85F5-34D8-95EB-F29F4145A46C}.Release|x64.ActiveCfg = Release|x64
		{C843D979-85F5-34D8-95EB-F29F4145A46C}.Release|x64.Build.0 = Release|x64
		{C843D979-85F5-34D8-95EB-F29F4145A46C}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{C843D979-85F5-34D8-95EB-F29F4145A46C}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{C843D979-85F5-34D8-95EB-F29F4145A46C}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C843D979-85F5-34D8-95EB-F29F4145A46C}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{E373B025-08E8-3046-A048-3D2157436208}.Debug|x64.ActiveCfg = Debug|x64
		{E373B025-08E8-3046-A048-3D2157436208}.Debug|x64.Build.0 = Debug|x64
		{E373B025-08E8-3046-A048-3D2157436208}.Release|x64.ActiveCfg = Release|x64
		{E373B025-08E8-3046-A048-3D2157436208}.Release|x64.Build.0 = Release|x64
		{E373B025-08E8-3046-A048-3D2157436208}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{E373B025-08E8-3046-A048-3D2157436208}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{E373B025-08E8-3046-A048-3D2157436208}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{E373B025-08E8-3046-A048-3D2157436208}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{119E5808-FF94-3275-B96F-D93D7569C57B}.Debug|x64.ActiveCfg = Debug|x64
		{119E5808-FF94-3275-B96F-D93D7569C57B}.Debug|x64.Build.0 = Debug|x64
		{119E5808-FF94-3275-B96F-D93D7569C57B}.Release|x64.ActiveCfg = Release|x64
		{119E5808-FF94-3275-B96F-D93D7569C57B}.Release|x64.Build.0 = Release|x64
		{119E5808-FF94-3275-B96F-D93D7569C57B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{119E5808-FF94-3275-B96F-D93D7569C57B}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{119E5808-FF94-3275-B96F-D93D7569C57B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{119E5808-FF94-3275-B96F-D93D7569C57B}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{2B00ACB5-DE28-3672-8636-F28ADBA637DD}.Debug|x64.ActiveCfg = Debug|x64
		{2B00ACB5-DE28-3672-8636-F28ADBA637DD}.Debug|x64.Build.0 = Debug|x64
		{2B00ACB5-DE28-3672-8636-F28ADBA637DD}.Release|x64.ActiveCfg = Release|x64
		{2B00ACB5-DE28-3672-8636-F28ADBA637DD}.Release|x64.Build.0 = Release|x64
		{2B00ACB5-DE28-3672-8636-F28ADBA637DD}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{2B00ACB5-DE28-3672-8636-F28ADBA637DD}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{2B00ACB5-DE28-3672-8636-F28ADBA637DD}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{2B00ACB5-DE28-3672-8636-F28ADBA637DD}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{2846A51D-759F-30E6-9397-E8D3B42936AA}.Debug|x64.ActiveCfg = Debug|x64
		{2846A51D-759F-30E6-9397-E8D3B42936AA}.Release|x64.ActiveCfg = Release|x64
		{2846A51D-759F-30E6-9397-E8D3B42936AA}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{2846A51D-759F-30E6-9397-E8D3B42936AA}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{442297F5-2886-3548-A699-3DC346C07EB5}.Debug|x64.ActiveCfg = Debug|x64
		{442297F5-2886-3548-A699-3DC346C07EB5}.Debug|x64.Build.0 = Debug|x64
		{442297F5-2886-3548-A699-3DC346C07EB5}.Release|x64.ActiveCfg = Release|x64
		{442297F5-2886-3548-A699-3DC346C07EB5}.Release|x64.Build.0 = Release|x64
		{442297F5-2886-3548-A699-3DC346C07EB5}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{442297F5-2886-3548-A699-3DC346C07EB5}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{442297F5-2886-3548-A699-3DC346C07EB5}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{442297F5-2886-3548-A699-3DC346C07EB5}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {DE5F70A5-5D01-3857-99B3-95575B35736C}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
