/****************************************************************************
** Meta object code from reading C++ file 'ElaPromotionCard.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaPromotionCard.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaPromotionCard.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN16ElaPromotionCardE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaPromotionCard::qt_create_metaobjectdata<qt_meta_tag_ZN16ElaPromotionCardE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaPromotionCard",
        "pBorderRadiusChanged",
        "",
        "pCardPixmapChanged",
        "pCardTitleChanged",
        "pPromotionTitleChanged",
        "pTitleChanged",
        "pSubTitleChanged",
        "pCardTitleColorChanged",
        "pPromotionTitleColorChanged",
        "pPromotionTitleBaseColorChanged",
        "pTitleColorChanged",
        "pSubTitleColorChanged",
        "pCardTitlePixelSizeChanged",
        "pPromotionTitlePixelSizeChanged",
        "pTitlePixelSizeChanged",
        "pSubTitlePixelSizeChanged",
        "pHorizontalCardPixmapRatioChanged",
        "pVerticalCardPixmapRatioChanged",
        "promotionCardClicked",
        "pBorderRadius",
        "pCardPixmap",
        "pCardTitle",
        "pPromotionTitle",
        "pTitle",
        "pSubTitle",
        "pCardTitleColor",
        "pPromotionTitleColor",
        "pPromotionTitleBaseColor",
        "pTitleColor",
        "pSubTitleColor",
        "pCardTitlePixelSize",
        "pPromotionTitlePixelSize",
        "pTitlePixelSize",
        "pSubTitlePixelSize",
        "pHorizontalCardPixmapRatio",
        "pVerticalCardPixmapRatio"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pBorderRadiusChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCardPixmapChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCardTitleChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pPromotionTitleChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pTitleChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pSubTitleChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCardTitleColorChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pPromotionTitleColorChanged'
        QtMocHelpers::SignalData<void()>(9, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pPromotionTitleBaseColorChanged'
        QtMocHelpers::SignalData<void()>(10, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pTitleColorChanged'
        QtMocHelpers::SignalData<void()>(11, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pSubTitleColorChanged'
        QtMocHelpers::SignalData<void()>(12, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCardTitlePixelSizeChanged'
        QtMocHelpers::SignalData<void()>(13, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pPromotionTitlePixelSizeChanged'
        QtMocHelpers::SignalData<void()>(14, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pTitlePixelSizeChanged'
        QtMocHelpers::SignalData<void()>(15, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pSubTitlePixelSizeChanged'
        QtMocHelpers::SignalData<void()>(16, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pHorizontalCardPixmapRatioChanged'
        QtMocHelpers::SignalData<void()>(17, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pVerticalCardPixmapRatioChanged'
        QtMocHelpers::SignalData<void()>(18, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'promotionCardClicked'
        QtMocHelpers::SignalData<void()>(19, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pBorderRadius'
        QtMocHelpers::PropertyData<int>(20, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pCardPixmap'
        QtMocHelpers::PropertyData<QPixmap>(21, QMetaType::QPixmap, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pCardTitle'
        QtMocHelpers::PropertyData<QString>(22, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pPromotionTitle'
        QtMocHelpers::PropertyData<QString>(23, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 3),
        // property 'pTitle'
        QtMocHelpers::PropertyData<QString>(24, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 4),
        // property 'pSubTitle'
        QtMocHelpers::PropertyData<QString>(25, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 5),
        // property 'pCardTitleColor'
        QtMocHelpers::PropertyData<QColor>(26, QMetaType::QColor, QMC::DefaultPropertyFlags | QMC::Writable, 6),
        // property 'pPromotionTitleColor'
        QtMocHelpers::PropertyData<QColor>(27, QMetaType::QColor, QMC::DefaultPropertyFlags | QMC::Writable, 7),
        // property 'pPromotionTitleBaseColor'
        QtMocHelpers::PropertyData<QColor>(28, QMetaType::QColor, QMC::DefaultPropertyFlags | QMC::Writable, 8),
        // property 'pTitleColor'
        QtMocHelpers::PropertyData<QColor>(29, QMetaType::QColor, QMC::DefaultPropertyFlags | QMC::Writable, 9),
        // property 'pSubTitleColor'
        QtMocHelpers::PropertyData<QColor>(30, QMetaType::QColor, QMC::DefaultPropertyFlags | QMC::Writable, 10),
        // property 'pCardTitlePixelSize'
        QtMocHelpers::PropertyData<int>(31, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 11),
        // property 'pPromotionTitlePixelSize'
        QtMocHelpers::PropertyData<int>(32, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 12),
        // property 'pTitlePixelSize'
        QtMocHelpers::PropertyData<int>(33, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 13),
        // property 'pSubTitlePixelSize'
        QtMocHelpers::PropertyData<int>(34, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 14),
        // property 'pHorizontalCardPixmapRatio'
        QtMocHelpers::PropertyData<qreal>(35, QMetaType::QReal, QMC::DefaultPropertyFlags | QMC::Writable, 15),
        // property 'pVerticalCardPixmapRatio'
        QtMocHelpers::PropertyData<qreal>(36, QMetaType::QReal, QMC::DefaultPropertyFlags | QMC::Writable, 16),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaPromotionCard, qt_meta_tag_ZN16ElaPromotionCardE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaPromotionCard::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16ElaPromotionCardE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16ElaPromotionCardE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN16ElaPromotionCardE_t>.metaTypes,
    nullptr
} };

void ElaPromotionCard::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaPromotionCard *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pBorderRadiusChanged(); break;
        case 1: _t->pCardPixmapChanged(); break;
        case 2: _t->pCardTitleChanged(); break;
        case 3: _t->pPromotionTitleChanged(); break;
        case 4: _t->pTitleChanged(); break;
        case 5: _t->pSubTitleChanged(); break;
        case 6: _t->pCardTitleColorChanged(); break;
        case 7: _t->pPromotionTitleColorChanged(); break;
        case 8: _t->pPromotionTitleBaseColorChanged(); break;
        case 9: _t->pTitleColorChanged(); break;
        case 10: _t->pSubTitleColorChanged(); break;
        case 11: _t->pCardTitlePixelSizeChanged(); break;
        case 12: _t->pPromotionTitlePixelSizeChanged(); break;
        case 13: _t->pTitlePixelSizeChanged(); break;
        case 14: _t->pSubTitlePixelSizeChanged(); break;
        case 15: _t->pHorizontalCardPixmapRatioChanged(); break;
        case 16: _t->pVerticalCardPixmapRatioChanged(); break;
        case 17: _t->promotionCardClicked(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCard::*)()>(_a, &ElaPromotionCard::pBorderRadiusChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCard::*)()>(_a, &ElaPromotionCard::pCardPixmapChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCard::*)()>(_a, &ElaPromotionCard::pCardTitleChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCard::*)()>(_a, &ElaPromotionCard::pPromotionTitleChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCard::*)()>(_a, &ElaPromotionCard::pTitleChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCard::*)()>(_a, &ElaPromotionCard::pSubTitleChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCard::*)()>(_a, &ElaPromotionCard::pCardTitleColorChanged, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCard::*)()>(_a, &ElaPromotionCard::pPromotionTitleColorChanged, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCard::*)()>(_a, &ElaPromotionCard::pPromotionTitleBaseColorChanged, 8))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCard::*)()>(_a, &ElaPromotionCard::pTitleColorChanged, 9))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCard::*)()>(_a, &ElaPromotionCard::pSubTitleColorChanged, 10))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCard::*)()>(_a, &ElaPromotionCard::pCardTitlePixelSizeChanged, 11))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCard::*)()>(_a, &ElaPromotionCard::pPromotionTitlePixelSizeChanged, 12))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCard::*)()>(_a, &ElaPromotionCard::pTitlePixelSizeChanged, 13))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCard::*)()>(_a, &ElaPromotionCard::pSubTitlePixelSizeChanged, 14))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCard::*)()>(_a, &ElaPromotionCard::pHorizontalCardPixmapRatioChanged, 15))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCard::*)()>(_a, &ElaPromotionCard::pVerticalCardPixmapRatioChanged, 16))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCard::*)()>(_a, &ElaPromotionCard::promotionCardClicked, 17))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = _t->getBorderRadius(); break;
        case 1: *reinterpret_cast<QPixmap*>(_v) = _t->getCardPixmap(); break;
        case 2: *reinterpret_cast<QString*>(_v) = _t->getCardTitle(); break;
        case 3: *reinterpret_cast<QString*>(_v) = _t->getPromotionTitle(); break;
        case 4: *reinterpret_cast<QString*>(_v) = _t->getTitle(); break;
        case 5: *reinterpret_cast<QString*>(_v) = _t->getSubTitle(); break;
        case 6: *reinterpret_cast<QColor*>(_v) = _t->getCardTitleColor(); break;
        case 7: *reinterpret_cast<QColor*>(_v) = _t->getPromotionTitleColor(); break;
        case 8: *reinterpret_cast<QColor*>(_v) = _t->getPromotionTitleBaseColor(); break;
        case 9: *reinterpret_cast<QColor*>(_v) = _t->getTitleColor(); break;
        case 10: *reinterpret_cast<QColor*>(_v) = _t->getSubTitleColor(); break;
        case 11: *reinterpret_cast<int*>(_v) = _t->getCardTitlePixelSize(); break;
        case 12: *reinterpret_cast<int*>(_v) = _t->getPromotionTitlePixelSize(); break;
        case 13: *reinterpret_cast<int*>(_v) = _t->getTitlePixelSize(); break;
        case 14: *reinterpret_cast<int*>(_v) = _t->getSubTitlePixelSize(); break;
        case 15: *reinterpret_cast<qreal*>(_v) = _t->getHorizontalCardPixmapRatio(); break;
        case 16: *reinterpret_cast<qreal*>(_v) = _t->getVerticalCardPixmapRatio(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setBorderRadius(*reinterpret_cast<int*>(_v)); break;
        case 1: _t->setCardPixmap(*reinterpret_cast<QPixmap*>(_v)); break;
        case 2: _t->setCardTitle(*reinterpret_cast<QString*>(_v)); break;
        case 3: _t->setPromotionTitle(*reinterpret_cast<QString*>(_v)); break;
        case 4: _t->setTitle(*reinterpret_cast<QString*>(_v)); break;
        case 5: _t->setSubTitle(*reinterpret_cast<QString*>(_v)); break;
        case 6: _t->setCardTitleColor(*reinterpret_cast<QColor*>(_v)); break;
        case 7: _t->setPromotionTitleColor(*reinterpret_cast<QColor*>(_v)); break;
        case 8: _t->setPromotionTitleBaseColor(*reinterpret_cast<QColor*>(_v)); break;
        case 9: _t->setTitleColor(*reinterpret_cast<QColor*>(_v)); break;
        case 10: _t->setSubTitleColor(*reinterpret_cast<QColor*>(_v)); break;
        case 11: _t->setCardTitlePixelSize(*reinterpret_cast<int*>(_v)); break;
        case 12: _t->setPromotionTitlePixelSize(*reinterpret_cast<int*>(_v)); break;
        case 13: _t->setTitlePixelSize(*reinterpret_cast<int*>(_v)); break;
        case 14: _t->setSubTitlePixelSize(*reinterpret_cast<int*>(_v)); break;
        case 15: _t->setHorizontalCardPixmapRatio(*reinterpret_cast<qreal*>(_v)); break;
        case 16: _t->setVerticalCardPixmapRatio(*reinterpret_cast<qreal*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaPromotionCard::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaPromotionCard::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16ElaPromotionCardE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ElaPromotionCard::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 18)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 18;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 18)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 18;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 17;
    }
    return _id;
}

// SIGNAL 0
void ElaPromotionCard::pBorderRadiusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaPromotionCard::pCardPixmapChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaPromotionCard::pCardTitleChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaPromotionCard::pPromotionTitleChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaPromotionCard::pTitleChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ElaPromotionCard::pSubTitleChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void ElaPromotionCard::pCardTitleColorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void ElaPromotionCard::pPromotionTitleColorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}

// SIGNAL 8
void ElaPromotionCard::pPromotionTitleBaseColorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 8, nullptr);
}

// SIGNAL 9
void ElaPromotionCard::pTitleColorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 9, nullptr);
}

// SIGNAL 10
void ElaPromotionCard::pSubTitleColorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 10, nullptr);
}

// SIGNAL 11
void ElaPromotionCard::pCardTitlePixelSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 11, nullptr);
}

// SIGNAL 12
void ElaPromotionCard::pPromotionTitlePixelSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 12, nullptr);
}

// SIGNAL 13
void ElaPromotionCard::pTitlePixelSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 13, nullptr);
}

// SIGNAL 14
void ElaPromotionCard::pSubTitlePixelSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 14, nullptr);
}

// SIGNAL 15
void ElaPromotionCard::pHorizontalCardPixmapRatioChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 15, nullptr);
}

// SIGNAL 16
void ElaPromotionCard::pVerticalCardPixmapRatioChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 16, nullptr);
}

// SIGNAL 17
void ElaPromotionCard::promotionCardClicked()
{
    QMetaObject::activate(this, &staticMetaObject, 17, nullptr);
}
QT_WARNING_POP
