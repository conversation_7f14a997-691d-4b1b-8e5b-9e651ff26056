#include "MainWindow.h"
#include <QApplication>
#include <QMessageBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSplitter>
#include <QLabel>
#include <QDebug>

// 静态常量定义
const int MainWindow::DEFAULT_GRID_COLUMNS = 4;
const int MainWindow::MIN_CARD_WIDTH = 200;
const int MainWindow::MAX_CARD_WIDTH = 400;
const int MainWindow::CARD_HEIGHT = 300;

MainWindow::MainWindow(QWidget *parent)
    :
#ifdef ELAWIDGETTOOLS_AVAILABLE
      ElaWindow(parent)
#else
      QMainWindow(parent)
#endif
    , m_civitaiClient(nullptr)
    , m_imageManager(nullptr)
    , m_configManager(nullptr)
    , m_searchLineEdit(nullptr)
    , m_sortComboBox(nullptr)
    , m_periodComboBox(nullptr)
    , m_nsfwComboBox(nullptr)
    , m_searchButton(nullptr)
    , m_imageScroll<PERSON><PERSON>(nullptr)
    , m_imageContainer(nullptr)
    , m_imageLayout(nullptr)
    , m_metaTabWidget(nullptr)
    , m_metaTextEdit(nullptr)
    , m_metaTreeView(nullptr)
    , m_metaViewToggle(nullptr)
    , m_currentMetaModel(nullptr)
    , m_prevPageButton(nullptr)
    , m_nextPageButton(nullptr)
    , m_pageLineEdit(nullptr)
    , m_pageInfoLabel(nullptr)
    , m_progressBar(nullptr)
    , m_statusLabel(nullptr)
#ifdef ELAWIDGETTOOLS_AVAILABLE
    , m_messageBar(nullptr)
#else
    , m_messageLabel(nullptr)
#endif
    , m_settingsAction(nullptr)
    , m_aboutAction(nullptr)
    , m_themeToggleAction(nullptr)
    , m_exitAction(nullptr)
    , m_isLoading(false)
    , m_currentGridColumns(DEFAULT_GRID_COLUMNS)
{
    // 初始化核心组件
    m_configManager = new ConfigManager(this);
    m_civitaiClient = new CivitaiClient(this);
    m_imageManager = new ImageManager(this);

    // 初始化UI
    initializeUI();

    // 初始化连接
    initializeConnections();

    // 加载设置
    loadSettings();

    // 设置窗口属性
    setWindowTitle("Civitai 图片查看器");
    setMinimumSize(1000, 700);
    resize(1400, 900);

    qDebug() << "MainWindow initialized successfully";
}

MainWindow::~MainWindow()
{
    saveSettings();
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    saveSettings();

#ifdef ELAWIDGETTOOLS_AVAILABLE
    ElaWindow::closeEvent(event);
#else
    QMainWindow::closeEvent(event);
#endif
}

void MainWindow::showEvent(QShowEvent *event)
{
#ifdef ELAWIDGETTOOLS_AVAILABLE
    ElaWindow::showEvent(event);
#else
    QMainWindow::showEvent(event);
#endif

    // 首次显示时的初始化
    static bool firstShow = true;
    if (firstShow) {
        firstShow = false;

        // 检查API密钥
        if (m_configManager->apiKey().isEmpty()) {
            QMessageBox::information(this, "欢迎",
                "欢迎使用 Civitai 图片查看器！\n\n"
                "请在设置中配置您的 Civitai API 密钥以开始使用。");
        }
    }
}

void MainWindow::initializeUI()
{
    // 创建中央部件
    initializeCentralWidget();

    // 创建菜单栏
    initializeMenuBar();

    // 创建工具栏
    initializeToolBar();

    // 创建状态栏
    initializeStatusBar();
}

void MainWindow::initializeMenuBar()
{
    QMenuBar* menuBar = this->menuBar();

    // 文件菜单
    QMenu* fileMenu = menuBar->addMenu("文件(&F)");

    m_settingsAction = new QAction("设置(&S)", this);
    m_settingsAction->setShortcut(QKeySequence::Preferences);
    fileMenu->addAction(m_settingsAction);

    fileMenu->addSeparator();

    m_exitAction = new QAction("退出(&X)", this);
    m_exitAction->setShortcut(QKeySequence::Quit);
    fileMenu->addAction(m_exitAction);

    // 视图菜单
    QMenu* viewMenu = menuBar->addMenu("视图(&V)");

    m_themeToggleAction = new QAction("切换主题(&T)", this);
    viewMenu->addAction(m_themeToggleAction);

    // 帮助菜单
    QMenu* helpMenu = menuBar->addMenu("帮助(&H)");

    m_aboutAction = new QAction("关于(&A)", this);
    helpMenu->addAction(m_aboutAction);
}

void MainWindow::initializeToolBar()
{
    QToolBar* toolBar = addToolBar("主工具栏");
    toolBar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);

    // 添加搜索相关的工具
    toolBar->addWidget(new QLabel("搜索:"));

#ifdef ELAWIDGETTOOLS_AVAILABLE
    m_searchLineEdit = new ElaLineEdit();
#else
    m_searchLineEdit = new QLineEdit();
#endif
    m_searchLineEdit->setPlaceholderText("输入搜索关键词...");
    m_searchLineEdit->setMinimumWidth(200);
    toolBar->addWidget(m_searchLineEdit);

#ifdef ELAWIDGETTOOLS_AVAILABLE
    m_searchButton = new ElaPushButton("搜索");
#else
    m_searchButton = new QPushButton("搜索");
#endif
    toolBar->addWidget(m_searchButton);

    toolBar->addSeparator();

    // 添加排序选项
    toolBar->addWidget(new QLabel("排序:"));

#ifdef ELAWIDGETTOOLS_AVAILABLE
    m_sortComboBox = new ElaComboBox();
#else
    m_sortComboBox = new QComboBox();
#endif
    m_sortComboBox->addItems({"Most Reactions", "Most Comments", "Newest", "Oldest"});
    toolBar->addWidget(m_sortComboBox);

    // 添加时间范围选项
    toolBar->addWidget(new QLabel("时间:"));

#ifdef ELAWIDGETTOOLS_AVAILABLE
    m_periodComboBox = new ElaComboBox();
#else
    m_periodComboBox = new QComboBox();
#endif
    m_periodComboBox->addItems({"AllTime", "Year", "Month", "Week", "Day"});
    toolBar->addWidget(m_periodComboBox);

    // 添加NSFW选项
    toolBar->addWidget(new QLabel("NSFW:"));

#ifdef ELAWIDGETTOOLS_AVAILABLE
    m_nsfwComboBox = new ElaComboBox();
#else
    m_nsfwComboBox = new QComboBox();
#endif
    m_nsfwComboBox->addItems({"None", "Soft", "Mature", "X"});
    toolBar->addWidget(m_nsfwComboBox);
}

void MainWindow::initializeStatusBar()
{
    QStatusBar* statusBar = this->statusBar();

    // 状态标签
    m_statusLabel = new QLabel("就绪");
    statusBar->addWidget(m_statusLabel);

    // 进度条
    m_progressBar = new QProgressBar();
    m_progressBar->setVisible(false);
    m_progressBar->setMaximumWidth(200);
    statusBar->addPermanentWidget(m_progressBar);

#ifdef ELAWIDGETTOOLS_AVAILABLE
    // 消息栏
    m_messageBar = new ElaMessageBar(this);
    m_messageBar->setVisible(false);
#else
    // 简单的消息标签
    m_messageLabel = new QLabel();
    m_messageLabel->setStyleSheet("QLabel { color: red; }");
    m_messageLabel->setVisible(false);
    statusBar->addPermanentWidget(m_messageLabel);
#endif
}

void MainWindow::initializeCentralWidget()
{
    // 创建主分割器
    QSplitter* mainSplitter = new QSplitter(Qt::Horizontal, this);
    setCentralWidget(mainSplitter);

    // 左侧：图片画廊
    QWidget* leftWidget = createImageGallery();
    mainSplitter->addWidget(leftWidget);

    // 右侧：元数据面板
    QWidget* rightWidget = createMetaDataPanel();
    mainSplitter->addWidget(rightWidget);

    // 设置分割器比例
    mainSplitter->setSizes({800, 400});
    mainSplitter->setStretchFactor(0, 1);
    mainSplitter->setStretchFactor(1, 0);
}

QWidget* MainWindow::createImageGallery()
{
    QWidget* widget = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(widget);

    // 创建滚动区域
    m_imageScrollArea = new QScrollArea();
    m_imageScrollArea->setWidgetResizable(true);
    m_imageScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_imageScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // 创建图片容器
    m_imageContainer = new QWidget();

#ifdef ELAWIDGETTOOLS_AVAILABLE
    m_imageLayout = new ElaFlowLayout(m_imageContainer);
#else
    m_imageLayout = new QGridLayout(m_imageContainer);
#endif

    m_imageScrollArea->setWidget(m_imageContainer);
    layout->addWidget(m_imageScrollArea);

    // 创建分页面板
    QWidget* paginationPanel = createPaginationPanel();
    layout->addWidget(paginationPanel);

    return widget;
}

void MainWindow::initializeConnections()
{
    // CivitaiClient 连接
    connect(m_civitaiClient, &CivitaiClient::imagesReceived,
            this, &MainWindow::onImagesReceived);
    connect(m_civitaiClient, &CivitaiClient::imageFetchFailed,
            this, &MainWindow::onImageFetchFailed);
    connect(m_civitaiClient, &CivitaiClient::rateLimitWarning,
            this, &MainWindow::onRateLimitWarning);

    // ImageManager 连接
    connect(m_imageManager, &ImageManager::imageLoaded,
            this, &MainWindow::onImageLoaded);
    connect(m_imageManager, &ImageManager::imageLoadFailed,
            this, &MainWindow::onImageLoadFailed);

    // UI 控件连接
    connect(m_searchButton, &QPushButton::clicked,
            this, &MainWindow::onSearchRequested);
    connect(m_searchLineEdit, &QLineEdit::returnPressed,
            this, &MainWindow::onSearchRequested);

    connect(m_prevPageButton, &QPushButton::clicked,
            this, &MainWindow::onPreviousPageRequested);
    connect(m_nextPageButton, &QPushButton::clicked,
            this, &MainWindow::onNextPageRequested);

    // 菜单动作连接
    connect(m_settingsAction, &QAction::triggered,
            this, &MainWindow::onSettingsRequested);
    connect(m_aboutAction, &QAction::triggered,
            this, &MainWindow::onAboutRequested);
    connect(m_themeToggleAction, &QAction::triggered,
            this, &MainWindow::onThemeToggled);
    connect(m_exitAction, &QAction::triggered,
            this, &QWidget::close);

    // 配置管理器连接
    connect(m_configManager, &ConfigManager::apiKeyChanged,
            m_civitaiClient, &CivitaiClient::setApiKey);
    connect(m_configManager, &ConfigManager::themeChanged,
            this, &MainWindow::onThemeToggled);
}

void MainWindow::loadSettings()
{
    // 恢复窗口几何形状
    QByteArray geometry = m_configManager->windowGeometry();
    if (!geometry.isEmpty()) {
        restoreGeometry(geometry);
    }

    QByteArray state = m_configManager->windowState();
    if (!state.isEmpty()) {
        restoreState(state);
    }

    // 设置API密钥
    QString apiKey = m_configManager->apiKey();
    if (!apiKey.isEmpty()) {
        m_civitaiClient->setApiKey(apiKey);
    }

    // 设置默认搜索参数
    m_sortComboBox->setCurrentText(m_configManager->defaultSort());
    m_periodComboBox->setCurrentText(m_configManager->defaultPeriod());
    m_nsfwComboBox->setCurrentText(m_configManager->defaultNsfwLevel());

    // 配置图片管理器
    m_imageManager->setMemoryCacheSize(m_configManager->memoryCacheSize());
    m_imageManager->setDiskCacheEnabled(m_configManager->isDiskCacheEnabled());
    m_imageManager->setDiskCacheDirectory(m_configManager->diskCacheDirectory());
    m_imageManager->setDiskCacheMaxSize(m_configManager->diskCacheMaxSize());
    m_imageManager->setMaxConcurrentDownloads(m_configManager->maxConcurrentDownloads());
    m_imageManager->setDownloadTimeout(m_configManager->downloadTimeout());

    // 配置API客户端
    m_civitaiClient->setRateLimit(m_configManager->apiRateLimit());
    m_civitaiClient->setRetryCount(m_configManager->apiRetryCount());
}

void MainWindow::saveSettings()
{
    // 保存窗口几何形状
    m_configManager->setWindowGeometry(saveGeometry());
    m_configManager->setWindowState(saveState());

    // 保存搜索参数
    m_configManager->setDefaultSort(m_sortComboBox->currentText());
    m_configManager->setDefaultPeriod(m_periodComboBox->currentText());
    m_configManager->setDefaultNsfwLevel(m_nsfwComboBox->currentText());
}

// 槽函数实现
void MainWindow::onImagesReceived(const QList<CivitaiImageInfo>& images, const PaginationInfo& pagination)
{
    m_isLoading = false;
    setUIEnabled(true);
    m_progressBar->setVisible(false);

    updateImageGallery(images);
    updatePaginationControls(pagination);

    showStatusMessage(QString("成功加载 %1 张图片").arg(images.size()));
}

void MainWindow::onImageFetchFailed(const QString& errorMessage, int httpStatusCode, const QString& errorType)
{
    Q_UNUSED(httpStatusCode)
    Q_UNUSED(errorType)

    m_isLoading = false;
    setUIEnabled(true);
    m_progressBar->setVisible(false);

    showErrorMessage("获取图片失败", errorMessage);
}

void MainWindow::onRateLimitWarning(int retryAfterSeconds)
{
    showStatusMessage(QString("API速率限制，将在 %1 秒后重试").arg(retryAfterSeconds));
}

void MainWindow::onSearchRequested()
{
    performSearch();
}

void MainWindow::onImageCardClicked(const CivitaiImageInfo& imageInfo)
{
    m_currentSelectedImage = imageInfo;
    updateMetaDataDisplay(imageInfo);
}

void MainWindow::onMetaViewModeChanged()
{
    // 切换元数据视图模式的逻辑
}

void MainWindow::onThemeToggled()
{
#ifdef ELAWIDGETTOOLS_AVAILABLE
    // 使用ElaTheme切换主题
    ElaThemeType currentTheme = ElaTheme::getInstance()->getThemeMode();
    ElaThemeType newTheme = (currentTheme == ElaThemeType::Light) ? ElaThemeType::Dark : ElaThemeType::Light;
    ElaTheme::getInstance()->setThemeMode(newTheme);

    QString themeName = (newTheme == ElaThemeType::Light) ? "Light" : "Dark";
    m_configManager->setTheme(themeName);
#else
    // 简单的主题切换
    QString currentTheme = m_configManager->theme();
    QString newTheme = (currentTheme == "Light") ? "Dark" : "Light";
    m_configManager->setTheme(newTheme);

    // 这里可以添加更多的主题切换逻辑
#endif
}

void MainWindow::onSettingsRequested()
{
    SettingsDialog dialog(m_configManager, this);
    if (dialog.exec() == QDialog::Accepted) {
        // 设置已保存，重新加载配置
        loadSettings();
        showStatusMessage("设置已保存");
    }
}

void MainWindow::onAboutRequested()
{
    QMessageBox::about(this, "关于",
        "Civitai 图片查看器 v1.0.0\n\n"
        "一个用于浏览和管理 Civitai 图片及元数据的桌面应用程序。\n\n"
        "基于 Qt 和 ElaWidgetTools 构建。");
}

void MainWindow::onPreviousPageRequested()
{
    if (m_currentPagination.hasPrevPage) {
        ApiRequestParams params = getCurrentSearchParams();
        params.page = m_currentPagination.currentPage - 1;
        m_civitaiClient->fetchImages(params);

        setUIEnabled(false);
        m_progressBar->setVisible(true);
        m_isLoading = true;
    }
}

void MainWindow::onNextPageRequested()
{
    if (m_currentPagination.hasNextPage) {
        ApiRequestParams params = getCurrentSearchParams();
        params.page = m_currentPagination.currentPage + 1;
        m_civitaiClient->fetchImages(params);

        setUIEnabled(false);
        m_progressBar->setVisible(true);
        m_isLoading = true;
    }
}

void MainWindow::onPageNumberChanged()
{
    // 页码输入变化的处理
}

void MainWindow::onImageLoaded(int imageId, const QPixmap& pixmap)
{
    // 查找对应的图片卡片并更新
    for (ImageCardWidget* card : m_imageCards) {
        if (card->imageInfo().id() == imageId) {
            card->setPixmap(pixmap);
            break;
        }
    }
}

void MainWindow::onImageLoadFailed(int imageId, const QString& error)
{
    // 查找对应的图片卡片并设置错误状态
    for (ImageCardWidget* card : m_imageCards) {
        if (card->imageInfo().id() == imageId) {
            card->setError(error);
            break;
        }
    }
}

// 业务逻辑方法的简单实现
void MainWindow::performSearch()
{
    if (m_isLoading) {
        return;
    }

    ApiRequestParams params = getCurrentSearchParams();
    params.page = 1; // 重置到第一页

    m_civitaiClient->fetchImages(params);

    setUIEnabled(false);
    m_progressBar->setVisible(true);
    m_isLoading = true;

    showStatusMessage("正在搜索图片...");
}

void MainWindow::updateImageGallery(const QList<CivitaiImageInfo>& images)
{
    clearImageGallery();

    for (const CivitaiImageInfo& imageInfo : images) {
        ImageCardWidget* card = new ImageCardWidget(imageInfo);

        // 连接卡片点击信号
        connect(card, &ImageCardWidget::cardClicked,
                this, &MainWindow::onImageCardClicked);
        // Qt 6: Use lambda to handle parameter mismatch
        connect(card, &ImageCardWidget::imageLoadRequested,
                this, [this](int imageId, const QString& url) {
                    m_imageManager->loadImage(imageId, url);
                });

        m_imageCards.append(card);

#ifdef ELAWIDGETTOOLS_AVAILABLE
        m_imageLayout->addWidget(card);
#else
        int row = m_imageCards.size() / m_currentGridColumns;
        int col = m_imageCards.size() % m_currentGridColumns;
        m_imageLayout->addWidget(card, row, col);
#endif
    }
}

void MainWindow::updateMetaDataDisplay(const CivitaiImageInfo& imageInfo)
{
    // 更新JSON文本视图
    QString jsonText = imageInfo.formattedMetaJsonString();
    if (jsonText.isEmpty()) {
        jsonText = "该图片没有元数据信息。";
    }
    m_metaTextEdit->setPlainText(jsonText);

    // 更新树状视图
    if (m_currentMetaModel) {
        m_currentMetaModel->deleteLater();
    }

    m_currentMetaModel = imageInfo.createMetaTreeModel(this);
    m_metaTreeView->setModel(m_currentMetaModel);
    m_metaTreeView->expandAll();
}

void MainWindow::updatePaginationControls(const PaginationInfo& pagination)
{
    m_currentPagination = pagination;

    m_prevPageButton->setEnabled(pagination.hasPrevPage);
    m_nextPageButton->setEnabled(pagination.hasNextPage);

    m_pageLineEdit->setText(QString::number(pagination.currentPage));
    m_pageInfoLabel->setText(QString("/ %1").arg(pagination.totalPages));
}

void MainWindow::showErrorMessage(const QString& message, const QString& details)
{
#ifdef ELAWIDGETTOOLS_AVAILABLE
    if (m_messageBar) {
        m_messageBar->error(message);
        m_messageBar->setVisible(true);
    }
#else
    if (m_messageLabel) {
        QString fullMessage = message;
        if (!details.isEmpty()) {
            fullMessage += ": " + details;
        }
        m_messageLabel->setText(fullMessage);
        m_messageLabel->setVisible(true);

        // 5秒后自动隐藏
        QTimer::singleShot(5000, m_messageLabel, &QLabel::hide);
    }
#endif

    m_statusLabel->setText(message);
}

void MainWindow::showStatusMessage(const QString& message, int timeout)
{
    m_statusLabel->setText(message);

    if (timeout > 0) {
        QTimer::singleShot(timeout, [this]() {
            m_statusLabel->setText("就绪");
        });
    }
}

void MainWindow::clearImageGallery()
{
    for (ImageCardWidget* card : m_imageCards) {
        card->deleteLater();
    }
    m_imageCards.clear();
}

void MainWindow::setUIEnabled(bool enabled)
{
    m_searchButton->setEnabled(enabled);
    m_searchLineEdit->setEnabled(enabled);
    m_sortComboBox->setEnabled(enabled);
    m_periodComboBox->setEnabled(enabled);
    m_nsfwComboBox->setEnabled(enabled);
    m_prevPageButton->setEnabled(enabled && m_currentPagination.hasPrevPage);
    m_nextPageButton->setEnabled(enabled && m_currentPagination.hasNextPage);
}

ApiRequestParams MainWindow::getCurrentSearchParams() const
{
    ApiRequestParams params;

    params.query = m_searchLineEdit->text().trimmed();
    params.sort = m_sortComboBox->currentText();
    params.period = m_periodComboBox->currentText();
    params.nsfw = m_nsfwComboBox->currentText();
    params.limit = 20; // 每页20张图片

    return params;
}

QWidget* MainWindow::createMetaDataPanel()
{
    QWidget* widget = new QWidget();
    QVBoxLayout* layout = new QVBoxLayout(widget);

    // 创建标签页
    QTabWidget* tabWidget = new QTabWidget();

    // JSON文本视图
    m_metaTextEdit = new QTextEdit();
    m_metaTextEdit->setReadOnly(true);
    m_metaTextEdit->setFont(QFont("Consolas", 9));
    tabWidget->addTab(m_metaTextEdit, "JSON");

    // 树状视图
    m_metaTreeView = new QTreeView();
    m_metaTreeView->setAlternatingRowColors(true);
    m_metaTreeView->setRootIsDecorated(true);
    tabWidget->addTab(m_metaTreeView, "树状视图");

    layout->addWidget(tabWidget);
    return widget;
}

QWidget* MainWindow::createPaginationPanel()
{
    QWidget* widget = new QWidget();
    QHBoxLayout* layout = new QHBoxLayout(widget);

    // 上一页按钮
    m_prevPageButton = new QPushButton("上一页");
    m_prevPageButton->setEnabled(false);
    layout->addWidget(m_prevPageButton);

    // 页码输入
    m_pageLineEdit = new QLineEdit();
    m_pageLineEdit->setMaximumWidth(60);
    m_pageLineEdit->setAlignment(Qt::AlignCenter);
    layout->addWidget(m_pageLineEdit);

    // 页码信息
    m_pageInfoLabel = new QLabel("/ 1");
    layout->addWidget(m_pageInfoLabel);

    // 下一页按钮
    m_nextPageButton = new QPushButton("下一页");
    m_nextPageButton->setEnabled(false);
    layout->addWidget(m_nextPageButton);

    layout->addStretch();

    return widget;
}
