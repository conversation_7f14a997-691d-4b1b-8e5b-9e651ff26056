/****************************************************************************
** Meta object code from reading C++ file 'ElaText.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaText.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaText.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN7ElaTextE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaText::qt_create_metaobjectdata<qt_meta_tag_ZN7ElaTextE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaText",
        "pIsWrapAnywhereChanged",
        "",
        "pTextPixelSizeChanged",
        "pTextPointSizeChanged",
        "pTextStyleChanged",
        "pElaIconChanged",
        "pIsWrapAnywhere",
        "pTextPixelSize",
        "pTextPointSize",
        "pTextStyle",
        "ElaTextType::TextStyle",
        "pElaIcon",
        "ElaIconType::IconName"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pIsWrapAnywhereChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pTextPixelSizeChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pTextPointSizeChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pTextStyleChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pElaIconChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pIsWrapAnywhere'
        QtMocHelpers::PropertyData<bool>(7, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pTextPixelSize'
        QtMocHelpers::PropertyData<int>(8, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pTextPointSize'
        QtMocHelpers::PropertyData<int>(9, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pTextStyle'
        QtMocHelpers::PropertyData<ElaTextType::TextStyle>(10, 0x80000000 | 11, QMC::DefaultPropertyFlags | QMC::Writable | QMC::EnumOrFlag, 3),
        // property 'pElaIcon'
        QtMocHelpers::PropertyData<ElaIconType::IconName>(12, 0x80000000 | 13, QMC::DefaultPropertyFlags | QMC::Writable | QMC::EnumOrFlag, 4),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaText, qt_meta_tag_ZN7ElaTextE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT static const QMetaObject::SuperData qt_meta_extradata_ZN7ElaTextE[] = {
    QMetaObject::SuperData::link<ElaTextType::staticMetaObject>(),
    QMetaObject::SuperData::link<ElaIconType::staticMetaObject>(),
    nullptr
};

Q_CONSTINIT const QMetaObject ElaText::staticMetaObject = { {
    QMetaObject::SuperData::link<QLabel::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN7ElaTextE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN7ElaTextE_t>.data,
    qt_static_metacall,
    qt_meta_extradata_ZN7ElaTextE,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN7ElaTextE_t>.metaTypes,
    nullptr
} };

void ElaText::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaText *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pIsWrapAnywhereChanged(); break;
        case 1: _t->pTextPixelSizeChanged(); break;
        case 2: _t->pTextPointSizeChanged(); break;
        case 3: _t->pTextStyleChanged(); break;
        case 4: _t->pElaIconChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaText::*)()>(_a, &ElaText::pIsWrapAnywhereChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaText::*)()>(_a, &ElaText::pTextPixelSizeChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaText::*)()>(_a, &ElaText::pTextPointSizeChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaText::*)()>(_a, &ElaText::pTextStyleChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaText::*)()>(_a, &ElaText::pElaIconChanged, 4))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<bool*>(_v) = _t->getIsWrapAnywhere(); break;
        case 1: *reinterpret_cast<int*>(_v) = _t->getTextPixelSize(); break;
        case 2: *reinterpret_cast<int*>(_v) = _t->getTextPointSize(); break;
        case 3: *reinterpret_cast<ElaTextType::TextStyle*>(_v) = _t->getTextStyle(); break;
        case 4: *reinterpret_cast<ElaIconType::IconName*>(_v) = _t->getElaIcon(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setIsWrapAnywhere(*reinterpret_cast<bool*>(_v)); break;
        case 1: _t->setTextPixelSize(*reinterpret_cast<int*>(_v)); break;
        case 2: _t->setTextPointSize(*reinterpret_cast<int*>(_v)); break;
        case 3: _t->setTextStyle(*reinterpret_cast<ElaTextType::TextStyle*>(_v)); break;
        case 4: _t->setElaIcon(*reinterpret_cast<ElaIconType::IconName*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaText::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaText::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN7ElaTextE_t>.strings))
        return static_cast<void*>(this);
    return QLabel::qt_metacast(_clname);
}

int ElaText::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QLabel::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 5;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    }
    return _id;
}

// SIGNAL 0
void ElaText::pIsWrapAnywhereChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaText::pTextPixelSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaText::pTextPointSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaText::pTextStyleChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaText::pElaIconChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}
QT_WARNING_POP
