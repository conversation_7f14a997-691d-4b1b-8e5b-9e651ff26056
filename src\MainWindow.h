#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSplitter>
#include <QScrollArea>
#include <QTextEdit>
#include <QTreeView>
#include <QLineEdit>
#include <QComboBox>
#include <QPushButton>
#include <QLabel>
#include <QProgressBar>
#include <QStatusBar>
#include <QMenuBar>
#include <QToolBar>
#include <QAction>
#include <QStackedWidget>
#include <QTabWidget>
#include <QGridLayout>
#include <QFlowLayout>
#include <QTimer>

#include "CivitaiClient.h"
#include "CivitaiImageInfo.h"
#include "ImageManager.h"
#include "ConfigManager.h"
#include "ImageCardWidget.h"
#include "SettingsDialog.h"

// 如果ElaWidgetTools可用，使用它的组件
#ifdef ELAWIDGETTOOLS_AVAILABLE
#include "ElaWindow.h"
#include "ElaImageCard.h"
#include "ElaPlainTextEdit.h"
#include "ElaTreeView.h"
#include "ElaLineEdit.h"
#include "ElaComboBox.h"
#include "ElaPushButton.h"
#include "ElaScrollPageArea.h"
#include "ElaFlowLayout.h"
#include "ElaNavigationBar.h"
#include "ElaStatusBar.h"
#include "ElaTheme.h"
#include "ElaToggleButton.h"
#include "ElaMessageBar.h"
#endif

class QAbstractItemModel;
class ImageCardWidget;

/**
 * @brief 主窗口类
 *
 * 应用程序的主界面，负责整合所有UI组件和业务逻辑
 */
class MainWindow : public
#ifdef ELAWIDGETTOOLS_AVAILABLE
    ElaWindow
#else
    QMainWindow
#endif
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;
    void showEvent(QShowEvent *event) override;

private slots:
    // Civitai API 相关槽函数
    void onImagesReceived(const QList<CivitaiImageInfo>& images, const PaginationInfo& pagination);
    void onImageFetchFailed(const QString& errorMessage, int httpStatusCode, const QString& errorType);
    void onRateLimitWarning(int retryAfterSeconds);

    // UI 交互槽函数
    void onSearchRequested();
    void onImageCardClicked(const CivitaiImageInfo& imageInfo);
    void onMetaViewModeChanged();
    void onThemeToggled();
    void onSettingsRequested();
    void onAboutRequested();

    // 分页相关
    void onPreviousPageRequested();
    void onNextPageRequested();
    void onPageNumberChanged();

    // 图片加载相关
    void onImageLoaded(int imageId, const QPixmap& pixmap);
    void onImageLoadFailed(int imageId, const QString& error);

private:
    // 初始化方法
    void initializeUI();
    void initializeMenuBar();
    void initializeToolBar();
    void initializeStatusBar();
    void initializeCentralWidget();
    void initializeConnections();
    void loadSettings();
    void saveSettings();

    // UI 构建方法
    QWidget* createSearchPanel();
    QWidget* createImageGallery();
    QWidget* createMetaDataPanel();
    QWidget* createPaginationPanel();

    // 业务逻辑方法
    void performSearch();
    void updateImageGallery(const QList<CivitaiImageInfo>& images);
    void updateMetaDataDisplay(const CivitaiImageInfo& imageInfo);
    void updatePaginationControls(const PaginationInfo& pagination);
    void showErrorMessage(const QString& message, const QString& details = QString());
    void showStatusMessage(const QString& message, int timeout = 3000);

    // 工具方法
    void clearImageGallery();
    void setUIEnabled(bool enabled);
    ApiRequestParams getCurrentSearchParams() const;

private:
    // 核心组件
    CivitaiClient* m_civitaiClient;
    ImageManager* m_imageManager;
    ConfigManager* m_configManager;

    // UI 组件 - 搜索面板
#ifdef ELAWIDGETTOOLS_AVAILABLE
    ElaLineEdit* m_searchLineEdit;
    ElaComboBox* m_sortComboBox;
    ElaComboBox* m_periodComboBox;
    ElaComboBox* m_nsfwComboBox;
    ElaPushButton* m_searchButton;
#else
    QLineEdit* m_searchLineEdit;
    QComboBox* m_sortComboBox;
    QComboBox* m_periodComboBox;
    QComboBox* m_nsfwComboBox;
    QPushButton* m_searchButton;
#endif

    // UI 组件 - 图片画廊
    QScrollArea* m_imageScrollArea;
    QWidget* m_imageContainer;
#ifdef ELAWIDGETTOOLS_AVAILABLE
    ElaFlowLayout* m_imageLayout;
#else
    QGridLayout* m_imageLayout;
#endif
    QList<ImageCardWidget*> m_imageCards;

    // UI 组件 - 元数据面板
    QTabWidget* m_metaTabWidget;
#ifdef ELAWIDGETTOOLS_AVAILABLE
    ElaPlainTextEdit* m_metaTextEdit;
    ElaTreeView* m_metaTreeView;
    ElaToggleButton* m_metaViewToggle;
#else
    QTextEdit* m_metaTextEdit;
    QTreeView* m_metaTreeView;
    QPushButton* m_metaViewToggle;
#endif
    QAbstractItemModel* m_currentMetaModel;

    // UI 组件 - 分页
    QPushButton* m_prevPageButton;
    QPushButton* m_nextPageButton;
    QLineEdit* m_pageLineEdit;
    QLabel* m_pageInfoLabel;

    // UI 组件 - 状态和进度
    QProgressBar* m_progressBar;
    QLabel* m_statusLabel;
#ifdef ELAWIDGETTOOLS_AVAILABLE
    ElaMessageBar* m_messageBar;
#else
    QLabel* m_messageLabel;
#endif

    // 菜单和工具栏
    QAction* m_settingsAction;
    QAction* m_aboutAction;
    QAction* m_themeToggleAction;
    QAction* m_exitAction;

    // 状态变量
    PaginationInfo m_currentPagination;
    CivitaiImageInfo m_currentSelectedImage;
    bool m_isLoading;
    int m_currentGridColumns;

    // 常量
    static const int DEFAULT_GRID_COLUMNS;
    static const int MIN_CARD_WIDTH;
    static const int MAX_CARD_WIDTH;
    static const int CARD_HEIGHT;
};

#endif // MAINWINDOW_H
