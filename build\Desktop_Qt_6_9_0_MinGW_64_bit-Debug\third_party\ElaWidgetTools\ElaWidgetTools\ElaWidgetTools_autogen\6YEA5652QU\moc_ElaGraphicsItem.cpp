/****************************************************************************
** Meta object code from reading C++ file 'ElaGraphicsItem.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaGraphicsItem.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaGraphicsItem.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN15ElaGraphicsItemE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaGraphicsItem::qt_create_metaobjectdata<qt_meta_tag_ZN15ElaGraphicsItemE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaGraphicsItem",
        "pWidthChanged",
        "",
        "pHeightChanged",
        "pItemImageChanged",
        "pItemSelectedImageChanged",
        "pItemNameChanged",
        "pDataRoutesChanged",
        "pMaxLinkPortCountChanged",
        "pWidth",
        "pHeight",
        "pItemImage",
        "pItemSelectedImage",
        "pItemName",
        "pDataRoutes",
        "QVariantMap",
        "pMaxLinkPortCount"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pWidthChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pHeightChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pItemImageChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pItemSelectedImageChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pItemNameChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pDataRoutesChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pMaxLinkPortCountChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pWidth'
        QtMocHelpers::PropertyData<int>(9, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pHeight'
        QtMocHelpers::PropertyData<int>(10, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pItemImage'
        QtMocHelpers::PropertyData<QImage>(11, QMetaType::QImage, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pItemSelectedImage'
        QtMocHelpers::PropertyData<QImage>(12, QMetaType::QImage, QMC::DefaultPropertyFlags | QMC::Writable, 3),
        // property 'pItemName'
        QtMocHelpers::PropertyData<QString>(13, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 4),
        // property 'pDataRoutes'
        QtMocHelpers::PropertyData<QVariantMap>(14, 0x80000000 | 15, QMC::DefaultPropertyFlags | QMC::Writable | QMC::EnumOrFlag, 5),
        // property 'pMaxLinkPortCount'
        QtMocHelpers::PropertyData<int>(16, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 6),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaGraphicsItem, qt_meta_tag_ZN15ElaGraphicsItemE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaGraphicsItem::staticMetaObject = { {
    QMetaObject::SuperData::link<QGraphicsObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15ElaGraphicsItemE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15ElaGraphicsItemE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN15ElaGraphicsItemE_t>.metaTypes,
    nullptr
} };

void ElaGraphicsItem::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaGraphicsItem *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pWidthChanged(); break;
        case 1: _t->pHeightChanged(); break;
        case 2: _t->pItemImageChanged(); break;
        case 3: _t->pItemSelectedImageChanged(); break;
        case 4: _t->pItemNameChanged(); break;
        case 5: _t->pDataRoutesChanged(); break;
        case 6: _t->pMaxLinkPortCountChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaGraphicsItem::*)()>(_a, &ElaGraphicsItem::pWidthChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaGraphicsItem::*)()>(_a, &ElaGraphicsItem::pHeightChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaGraphicsItem::*)()>(_a, &ElaGraphicsItem::pItemImageChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaGraphicsItem::*)()>(_a, &ElaGraphicsItem::pItemSelectedImageChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaGraphicsItem::*)()>(_a, &ElaGraphicsItem::pItemNameChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaGraphicsItem::*)()>(_a, &ElaGraphicsItem::pDataRoutesChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaGraphicsItem::*)()>(_a, &ElaGraphicsItem::pMaxLinkPortCountChanged, 6))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = _t->getWidth(); break;
        case 1: *reinterpret_cast<int*>(_v) = _t->getHeight(); break;
        case 2: *reinterpret_cast<QImage*>(_v) = _t->getItemImage(); break;
        case 3: *reinterpret_cast<QImage*>(_v) = _t->getItemSelectedImage(); break;
        case 4: *reinterpret_cast<QString*>(_v) = _t->getItemName(); break;
        case 5: *reinterpret_cast<QVariantMap*>(_v) = _t->getDataRoutes(); break;
        case 6: *reinterpret_cast<int*>(_v) = _t->getMaxLinkPortCount(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setWidth(*reinterpret_cast<int*>(_v)); break;
        case 1: _t->setHeight(*reinterpret_cast<int*>(_v)); break;
        case 2: _t->setItemImage(*reinterpret_cast<QImage*>(_v)); break;
        case 3: _t->setItemSelectedImage(*reinterpret_cast<QImage*>(_v)); break;
        case 4: _t->setItemName(*reinterpret_cast<QString*>(_v)); break;
        case 5: _t->setDataRoutes(*reinterpret_cast<QVariantMap*>(_v)); break;
        case 6: _t->setMaxLinkPortCount(*reinterpret_cast<int*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaGraphicsItem::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaGraphicsItem::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15ElaGraphicsItemE_t>.strings))
        return static_cast<void*>(this);
    return QGraphicsObject::qt_metacast(_clname);
}

int ElaGraphicsItem::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QGraphicsObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 7;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    }
    return _id;
}

// SIGNAL 0
void ElaGraphicsItem::pWidthChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaGraphicsItem::pHeightChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaGraphicsItem::pItemImageChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaGraphicsItem::pItemSelectedImageChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaGraphicsItem::pItemNameChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ElaGraphicsItem::pDataRoutesChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void ElaGraphicsItem::pMaxLinkPortCountChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}
QT_WARNING_POP
