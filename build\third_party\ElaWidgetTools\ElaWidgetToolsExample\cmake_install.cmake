# Install script for directory: C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetToolsExample

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "C:/Users/<USER>/Desktop/Civitai_IMG/Install")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Release")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
     "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/ElaWidgetToolsExample.exe")
    if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/Debug/ElaWidgetToolsExample.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
     "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/ElaWidgetToolsExample.exe")
    if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/Release/ElaWidgetToolsExample.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
     "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/ElaWidgetToolsExample.exe")
    if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/MinSizeRel/ElaWidgetToolsExample.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
     "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/ElaWidgetToolsExample.exe")
    if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/RelWithDebInfo/ElaWidgetToolsExample.exe")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
     "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/ElaWidgetToolsExample.pdb")
    if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample" TYPE FILE OPTIONAL FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/Debug/ElaWidgetToolsExample.pdb")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
     "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/ElaWidgetToolsExample.pdb")
    if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample" TYPE FILE OPTIONAL FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/Release/ElaWidgetToolsExample.pdb")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
     "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/ElaWidgetToolsExample.pdb")
    if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample" TYPE FILE OPTIONAL FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/MinSizeRel/ElaWidgetToolsExample.pdb")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
     "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/ElaWidgetToolsExample.pdb")
    if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample" TYPE FILE OPTIONAL FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/RelWithDebInfo/ElaWidgetToolsExample.pdb")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/platforms/qwindows.dll")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/platforms" TYPE FILE FILES "D:/Qt/6.6.2/msvc2019_64/plugins/platforms/qwindows.dll")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/generic/qtuiotouchplugin.dll")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/generic" TYPE FILE FILES "D:/Qt/6.6.2/msvc2019_64/plugins/generic/qtuiotouchplugin.dll")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/imageformats/qgif.dll;C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/imageformats/qicns.dll;C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/imageformats/qico.dll;C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/imageformats/qjpeg.dll;C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/imageformats/qtga.dll;C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/imageformats/qtiff.dll;C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/imageformats/qwbmp.dll;C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/imageformats/qwebp.dll")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/imageformats" TYPE FILE FILES
    "D:/Qt/6.6.2/msvc2019_64/plugins/imageformats/qgif.dll"
    "D:/Qt/6.6.2/msvc2019_64/plugins/imageformats/qicns.dll"
    "D:/Qt/6.6.2/msvc2019_64/plugins/imageformats/qico.dll"
    "D:/Qt/6.6.2/msvc2019_64/plugins/imageformats/qjpeg.dll"
    "D:/Qt/6.6.2/msvc2019_64/plugins/imageformats/qtga.dll"
    "D:/Qt/6.6.2/msvc2019_64/plugins/imageformats/qtiff.dll"
    "D:/Qt/6.6.2/msvc2019_64/plugins/imageformats/qwbmp.dll"
    "D:/Qt/6.6.2/msvc2019_64/plugins/imageformats/qwebp.dll"
    )
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/styles/qwindowsvistastyle.dll")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/styles" TYPE FILE FILES "D:/Qt/6.6.2/msvc2019_64/plugins/styles/qwindowsvistastyle.dll")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/Qt6Widgets.dll;C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/Qt6Core.dll;C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/Qt6Gui.dll;C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/Qt6Network.dll;C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/Qt6Xml.dll")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample" TYPE FILE FILES
    "D:/Qt/6.6.2/msvc2019_64/bin/Qt6Widgets.dll"
    "D:/Qt/6.6.2/msvc2019_64/bin/Qt6Core.dll"
    "D:/Qt/6.6.2/msvc2019_64/bin/Qt6Gui.dll"
    "D:/Qt/6.6.2/msvc2019_64/bin/Qt6Network.dll"
    "D:/Qt/6.6.2/msvc2019_64/bin/Qt6Xml.dll"
    )
endif()

