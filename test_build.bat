@echo off
echo ========================================
echo Civitai 图片查看器 - 构建测试脚本
echo ========================================
echo.

REM 检查当前目录
echo [1/6] 检查项目目录...
if not exist "CMakeLists.txt" (
    echo 错误: 未找到 CMakeLists.txt 文件
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)
echo ✓ 项目目录检查通过

REM 检查源文件
echo.
echo [2/6] 检查源文件...
if not exist "src\main.cpp" (
    echo 错误: 未找到 src\main.cpp 文件
    pause
    exit /b 1
)
echo ✓ 源文件检查通过

REM 检查 Qt 安装
echo.
echo [3/6] 检查 Qt 安装...
where qmake >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo 警告: 未找到 qmake，请确保 Qt 已正确安装并添加到 PATH
    echo 继续尝试构建...
) else (
    echo ✓ Qt 环境检查通过
)

REM 检查 CMake
echo.
echo [4/6] 检查 CMake...
where cmake >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo 错误: 未找到 CMake，请安装 CMake 并添加到 PATH
    pause
    exit /b 1
)
echo ✓ CMake 检查通过

REM 创建构建目录
echo.
echo [5/6] 创建构建目录...
if not exist "build" mkdir build
cd build

REM 配置项目
echo.
echo [6/6] 配置和构建项目...
echo 正在配置 CMake...
cmake .. -DCMAKE_BUILD_TYPE=Release

if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ CMake 配置失败！
    echo.
    echo 可能的原因：
    echo 1. Qt 未正确安装或未添加到 PATH
    echo 2. ElaWidgetTools 未正确放置在 third_party/ElaWidgetTools
    echo 3. 编译器未正确配置
    echo.
    echo 解决建议：
    echo 1. 安装 Qt 6.2+ 并确保 qmake 在 PATH 中
    echo 2. 克隆 ElaWidgetTools: 
    echo    git clone https://github.com/Liniyous/ElaWidgetTools.git third_party/ElaWidgetTools
    echo 3. 安装 Visual Studio 2019+ 或 MinGW
    echo.
    pause
    exit /b 1
)

echo ✓ CMake 配置成功

echo.
echo 正在构建项目...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ 项目构建失败！
    echo.
    echo 请检查上面的错误信息，常见问题：
    echo 1. 缺少必要的头文件或库
    echo 2. 编译器版本不兼容
    echo 3. ElaWidgetTools 编译失败
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎉 构建成功！
echo ========================================
echo.
echo 可执行文件位置: build\bin\CivitaiImageViewer.exe
echo.
echo 下一步：
echo 1. 获取 Civitai API 密钥：https://civitai.com/user/account
echo 2. 运行应用程序：build\bin\CivitaiImageViewer.exe
echo 3. 在设置中配置 API 密钥
echo 4. 开始搜索和浏览图片！
echo.

REM 询问是否立即运行
set /p choice="是否立即运行应用程序？(y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 正在启动应用程序...
    start bin\CivitaiImageViewer.exe
)

echo.
echo 测试完成！
pause
