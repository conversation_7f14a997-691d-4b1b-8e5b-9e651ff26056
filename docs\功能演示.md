# Civitai 图片查看器功能演示指南

## 🎬 完整功能演示流程

### 📋 功能清单概览

#### ✅ 已实现的完整功能

1. **🚀 应用程序核心**
   - 现代化 Qt 6 + ElaWidgetTools 界面
   - 跨平台支持 (Windows/Linux/macOS)
   - 完整的错误处理和状态管理

2. **🔌 Civitai API 集成**
   - 完整的 REST API 客户端
   - 速率限制和重试机制
   - 异步请求处理
   - API 密钥管理和验证

3. **🖼️ 图片浏览系统**
   - 网格布局图片展示
   - 异步图片加载
   - 图片信息卡片
   - 缩略图预览

4. **🔍 搜索和筛选**
   - 关键词搜索
   - 多种排序方式
   - 时间范围筛选
   - NSFW 等级控制
   - 搜索历史记录

5. **📊 元数据管理**
   - JSON 格式显示
   - 树状结构视图
   - 关键参数提取
   - 元数据搜索和导出

6. **💾 智能缓存系统**
   - 内存缓存 (可配置大小)
   - 磁盘缓存 (可配置目录和大小)
   - 并发下载控制
   - 缓存清理和管理

7. **⚙️ 配置管理**
   - 完整的设置系统
   - 配置持久化
   - 导入/导出功能
   - 默认设置恢复

8. **🎨 用户界面**
   - 亮色/暗色主题
   - 响应式布局
   - 状态栏和进度显示
   - 现代化交互体验

## 🎯 测试方法

### 方法一：快速自动化测试

#### Windows 用户
```cmd
# 运行自动化测试脚本
test_build.bat
```

#### Linux/macOS 用户
```bash
# 运行自动化测试脚本
./test_build.sh
```

### 方法二：手动构建测试

#### 1. 环境准备
```bash
# 确保已安装必要工具
- Qt 6.2+ (推荐 6.5 LTS)
- CMake 3.16+
- C++17 编译器

# 获取 ElaWidgetTools
git clone https://github.com/Liniyous/ElaWidgetTools.git third_party/ElaWidgetTools
```

#### 2. 构建项目
```bash
# 创建构建目录
mkdir build && cd build

# 配置项目
cmake .. -DCMAKE_BUILD_TYPE=Release

# 构建项目
cmake --build . --config Release
```

#### 3. 运行应用程序
```bash
# Windows
build\bin\CivitaiImageViewer.exe

# Linux/macOS
./build/bin/CivitaiImageViewer
```

## 🎮 功能演示步骤

### 第一步：应用程序启动
1. **启动应用程序**
   - 双击可执行文件或命令行运行
   - 观察启动画面和主窗口加载

2. **界面检查**
   - 主窗口正确显示
   - 菜单栏和工具栏加载完成
   - 状态栏显示"就绪"状态

### 第二步：API 配置
1. **打开设置**
   - 点击菜单 "文件" → "设置"
   - 或使用快捷键 Ctrl+, (Windows/Linux) 或 Cmd+, (macOS)

2. **配置 API 密钥**
   - 切换到 "API" 选项卡
   - 输入从 Civitai 获取的 API 密钥
   - 点击 "测试连接" 验证

3. **调整 API 参数**
   - 设置速率限制 (推荐 1-2 请求/秒)
   - 设置重试次数 (推荐 3 次)

### 第三步：基础搜索演示
1. **简单搜索**
   - 在搜索框输入 "landscape"
   - 点击搜索按钮
   - 观察图片加载过程

2. **搜索参数调整**
   - 排序方式：选择 "Most Reactions"
   - 时间范围：选择 "Month"
   - NSFW 等级：选择 "None"

### 第四步：图片浏览演示
1. **图片卡片展示**
   - 观察图片网格布局
   - 检查图片信息显示
   - 测试图片异步加载

2. **图片交互**
   - 点击图片卡片
   - 观察选中状态
   - 查看图片详细信息

### 第五步：元数据查看演示
1. **元数据面板**
   - 点击任意图片卡片
   - 右侧面板显示元数据
   - 检查数据完整性

2. **视图切换**
   - 切换到 "JSON文本" 选项卡
   - 查看原始 JSON 格式
   - 切换到 "结构化视图" 选项卡
   - 展开树状结构

### 第六步：分页浏览演示
1. **分页控制**
   - 点击 "下一页" 按钮
   - 观察页面切换
   - 检查页码信息更新

2. **页面导航**
   - 在页码输入框输入特定页码
   - 按回车跳转到指定页面

### 第七步：缓存系统演示
1. **缓存配置**
   - 进入设置 → 缓存选项卡
   - 查看缓存使用情况
   - 调整缓存参数

2. **缓存测试**
   - 重复访问相同图片
   - 观察加载速度提升
   - 测试缓存清理功能

### 第八步：高级功能演示
1. **主题切换**
   - 菜单 "视图" → "切换主题"
   - 观察界面颜色变化
   - 验证主题保存

2. **设置管理**
   - 导出当前设置
   - 修改部分配置
   - 导入之前的设置
   - 测试恢复默认设置

## 📊 性能测试

### 内存使用测试
1. **长时间运行**
   - 连续运行 30 分钟以上
   - 监控内存使用情况
   - 检查是否有内存泄漏

2. **大量数据加载**
   - 浏览多页图片内容
   - 观察内存增长情况
   - 测试缓存清理效果

### 网络性能测试
1. **并发下载测试**
   - 调整并发下载数量
   - 测试不同网络环境
   - 观察下载速度和稳定性

2. **错误恢复测试**
   - 模拟网络中断
   - 测试重连机制
   - 验证错误处理

## 🐛 常见问题排查

### 构建问题
- **Qt 未找到**: 确保 Qt 6.2+ 已安装并在 PATH 中
- **ElaWidgetTools 错误**: 检查是否正确克隆到 third_party 目录
- **编译器错误**: 确保使用 C++17 兼容编译器

### 运行问题
- **应用程序崩溃**: 检查 Qt 运行时库是否完整
- **API 连接失败**: 验证 API 密钥和网络连接
- **图片加载失败**: 检查缓存设置和磁盘空间

### 功能问题
- **搜索无结果**: 检查搜索关键词和筛选条件
- **元数据不显示**: 确认图片包含元数据信息
- **设置不保存**: 检查配置文件写入权限

## 🎯 测试成功标准

### 基础功能 ✅
- [ ] 应用程序正常启动和关闭
- [ ] 主界面完整显示
- [ ] 设置对话框正常工作

### 核心功能 ✅
- [ ] API 连接和认证成功
- [ ] 图片搜索和加载正常
- [ ] 元数据正确显示

### 高级功能 ✅
- [ ] 缓存系统正常工作
- [ ] 主题切换功能正常
- [ ] 设置导入导出正常

### 性能指标 ✅
- [ ] 内存使用稳定
- [ ] 网络请求响应及时
- [ ] 界面操作流畅

## 🚀 下一步

完成功能测试后，您可以：

1. **日常使用**
   - 浏览 Civitai 上的精美 AI 艺术作品
   - 学习不同的生成参数和技巧
   - 收集灵感和创作素材

2. **功能扩展**
   - 根据需要修改源代码
   - 添加新的功能模块
   - 优化用户体验

3. **社区贡献**
   - 报告发现的问题
   - 提出改进建议
   - 分享使用经验

---

**恭喜！** 您已经成功完成了 Civitai 图片查看器的完整功能测试。这个应用程序现在可以为您提供优秀的 AI 艺术作品浏览体验！
