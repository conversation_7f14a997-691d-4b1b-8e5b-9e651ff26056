/****************************************************************************
** Meta object code from reading C++ file 'ElaPivot.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaPivot.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaPivot.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN8ElaPivotE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaPivot::qt_create_metaobjectdata<qt_meta_tag_ZN8ElaPivotE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaPivot",
        "pTextPixelSizeChanged",
        "",
        "pCurrentIndexChanged",
        "pPivotSpacingChanged",
        "pMarkWidthChanged",
        "pivotClicked",
        "index",
        "pivotDoubleClicked",
        "pTextPixelSize",
        "pCurrentIndex",
        "pPivotSpacing",
        "pMarkWidth"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pTextPixelSizeChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCurrentIndexChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pPivotSpacingChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pMarkWidthChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pivotClicked'
        QtMocHelpers::SignalData<void(int)>(6, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 7 },
        }}),
        // Signal 'pivotDoubleClicked'
        QtMocHelpers::SignalData<void(int)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 7 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pTextPixelSize'
        QtMocHelpers::PropertyData<int>(9, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pCurrentIndex'
        QtMocHelpers::PropertyData<int>(10, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pPivotSpacing'
        QtMocHelpers::PropertyData<int>(11, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pMarkWidth'
        QtMocHelpers::PropertyData<int>(12, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 3),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaPivot, qt_meta_tag_ZN8ElaPivotE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaPivot::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN8ElaPivotE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN8ElaPivotE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN8ElaPivotE_t>.metaTypes,
    nullptr
} };

void ElaPivot::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaPivot *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pTextPixelSizeChanged(); break;
        case 1: _t->pCurrentIndexChanged(); break;
        case 2: _t->pPivotSpacingChanged(); break;
        case 3: _t->pMarkWidthChanged(); break;
        case 4: _t->pivotClicked((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 5: _t->pivotDoubleClicked((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaPivot::*)()>(_a, &ElaPivot::pTextPixelSizeChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPivot::*)()>(_a, &ElaPivot::pCurrentIndexChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPivot::*)()>(_a, &ElaPivot::pPivotSpacingChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPivot::*)()>(_a, &ElaPivot::pMarkWidthChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPivot::*)(int )>(_a, &ElaPivot::pivotClicked, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPivot::*)(int )>(_a, &ElaPivot::pivotDoubleClicked, 5))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = _t->getTextPixelSize(); break;
        case 1: *reinterpret_cast<int*>(_v) = _t->getCurrentIndex(); break;
        case 2: *reinterpret_cast<int*>(_v) = _t->getPivotSpacing(); break;
        case 3: *reinterpret_cast<int*>(_v) = _t->getMarkWidth(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setTextPixelSize(*reinterpret_cast<int*>(_v)); break;
        case 1: _t->setCurrentIndex(*reinterpret_cast<int*>(_v)); break;
        case 2: _t->setPivotSpacing(*reinterpret_cast<int*>(_v)); break;
        case 3: _t->setMarkWidth(*reinterpret_cast<int*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaPivot::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaPivot::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN8ElaPivotE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ElaPivot::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 6;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void ElaPivot::pTextPixelSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaPivot::pCurrentIndexChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaPivot::pPivotSpacingChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaPivot::pMarkWidthChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaPivot::pivotClicked(int _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}

// SIGNAL 5
void ElaPivot::pivotDoubleClicked(int _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1);
}
QT_WARNING_POP
