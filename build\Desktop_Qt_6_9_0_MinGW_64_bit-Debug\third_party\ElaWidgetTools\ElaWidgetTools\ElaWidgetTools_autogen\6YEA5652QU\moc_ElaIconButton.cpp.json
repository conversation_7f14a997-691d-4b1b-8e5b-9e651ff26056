{"classes": [{"className": "ElaIconButton", "lineNumber": 9, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pBorderRadius", "notify": "pBorderRadiusChanged", "read": "getBorderRadius", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBorderRadius"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pOpacity", "notify": "pOpacityChanged", "read": "getOpacity", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setOpacity"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "pLightHoverColor", "notify": "pLightHoverColorChanged", "read": "getLightHoverColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setLightHoverColor"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "pDarkHoverColor", "notify": "pDarkHoverColorChanged", "read": "getDarkHoverColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setDarkHoverColor"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "pLightIconColor", "notify": "pLightIconColorChanged", "read": "getLightIconColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setLightIconColor"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "pDarkIconColor", "notify": "pDarkIconColorChanged", "read": "getDarkIconColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setDarkIconColor"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "pLightHoverIconColor", "notify": "pLightHoverIconColorChanged", "read": "getLightHoverIconColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setLightHoverIconColor"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "pDarkHoverIconColor", "notify": "pDarkHoverIconColorChanged", "read": "getDarkHoverIconColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setDarkHoverIconColor"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "pIsSelected", "notify": "pIsSelectedChanged", "read": "getIsSelected", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setIsSelected"}], "qualifiedClassName": "ElaIconButton", "signals": [{"access": "public", "index": 0, "name": "pBorderRadiusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pOpacityChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "pLightHoverColorChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "pDarkHoverColorChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "pLightIconColorChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "pDarkIconColorChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "pLightHoverIconColorChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "pDarkHoverIconColorChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "pIsSelectedChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPushButton"}]}], "inputFile": "ElaIconButton.h", "outputRevision": 69}