{"classes": [{"className": "ElaScrollBarPrivate", "lineNumber": 12, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "member": "_pTargetMaximum", "name": "pTargetMaximum", "notify": "pTargetMaximumChanged", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "ElaScrollBarPrivate", "signals": [{"access": "public", "index": 0, "name": "pTargetMaximumChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "min", "type": "int"}, {"name": "max", "type": "int"}], "index": 1, "name": "onRangeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "ElaScrollBarPrivate.h", "outputRevision": 69}