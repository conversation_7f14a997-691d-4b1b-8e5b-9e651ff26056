#!/bin/bash

echo "========================================"
echo "Civitai 图片查看器 - 构建测试脚本"
echo "========================================"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查当前目录
echo "[1/6] 检查项目目录..."
if [ ! -f "CMakeLists.txt" ]; then
    echo -e "${RED}错误: 未找到 CMakeLists.txt 文件${NC}"
    echo "请确保在项目根目录运行此脚本"
    exit 1
fi
echo -e "${GREEN}✓ 项目目录检查通过${NC}"

# 检查源文件
echo
echo "[2/6] 检查源文件..."
if [ ! -f "src/main.cpp" ]; then
    echo -e "${RED}错误: 未找到 src/main.cpp 文件${NC}"
    exit 1
fi
echo -e "${GREEN}✓ 源文件检查通过${NC}"

# 检查 Qt 安装
echo
echo "[3/6] 检查 Qt 安装..."
if ! command -v qmake &> /dev/null; then
    echo -e "${YELLOW}警告: 未找到 qmake，请确保 Qt 已正确安装并添加到 PATH${NC}"
    echo "继续尝试构建..."
else
    QT_VERSION=$(qmake -version | grep "Using Qt version" | cut -d' ' -f4)
    echo -e "${GREEN}✓ Qt 环境检查通过 (版本: $QT_VERSION)${NC}"
fi

# 检查 CMake
echo
echo "[4/6] 检查 CMake..."
if ! command -v cmake &> /dev/null; then
    echo -e "${RED}错误: 未找到 CMake，请安装 CMake${NC}"
    echo "Ubuntu/Debian: sudo apt install cmake"
    echo "macOS: brew install cmake"
    exit 1
fi
CMAKE_VERSION=$(cmake --version | head -n1 | cut -d' ' -f3)
echo -e "${GREEN}✓ CMake 检查通过 (版本: $CMAKE_VERSION)${NC}"

# 检查编译器
echo
echo "检查编译器..."
if command -v g++ &> /dev/null; then
    GCC_VERSION=$(g++ --version | head -n1)
    echo -e "${GREEN}✓ 找到 GCC: $GCC_VERSION${NC}"
elif command -v clang++ &> /dev/null; then
    CLANG_VERSION=$(clang++ --version | head -n1)
    echo -e "${GREEN}✓ 找到 Clang: $CLANG_VERSION${NC}"
else
    echo -e "${RED}错误: 未找到 C++ 编译器${NC}"
    echo "Ubuntu/Debian: sudo apt install build-essential"
    echo "macOS: xcode-select --install"
    exit 1
fi

# 创建构建目录
echo
echo "[5/6] 创建构建目录..."
mkdir -p build
cd build

# 配置项目
echo
echo "[6/6] 配置和构建项目..."
echo "正在配置 CMake..."
cmake .. -DCMAKE_BUILD_TYPE=Release

if [ $? -ne 0 ]; then
    echo
    echo -e "${RED}❌ CMake 配置失败！${NC}"
    echo
    echo "可能的原因："
    echo "1. Qt 未正确安装或未添加到 PATH"
    echo "2. ElaWidgetTools 未正确放置在 third_party/ElaWidgetTools"
    echo "3. 缺少必要的开发包"
    echo
    echo "解决建议："
    echo "1. 安装 Qt 6.2+ 开发包"
    echo "   Ubuntu/Debian: sudo apt install qt6-base-dev qt6-tools-dev"
    echo "   macOS: brew install qt6"
    echo "2. 克隆 ElaWidgetTools:"
    echo "   git clone https://github.com/Liniyous/ElaWidgetTools.git third_party/ElaWidgetTools"
    echo "3. 安装构建依赖"
    echo "   Ubuntu/Debian: sudo apt install build-essential cmake"
    echo
    exit 1
fi

echo -e "${GREEN}✓ CMake 配置成功${NC}"

echo
echo "正在构建项目..."
cmake --build . --config Release

if [ $? -ne 0 ]; then
    echo
    echo -e "${RED}❌ 项目构建失败！${NC}"
    echo
    echo "请检查上面的错误信息，常见问题："
    echo "1. 缺少必要的头文件或库"
    echo "2. 编译器版本不兼容"
    echo "3. ElaWidgetTools 编译失败"
    echo
    exit 1
fi

echo
echo "========================================"
echo -e "${GREEN}🎉 构建成功！${NC}"
echo "========================================"
echo
echo "可执行文件位置: build/bin/CivitaiImageViewer"
echo
echo "下一步："
echo "1. 获取 Civitai API 密钥：https://civitai.com/user/account"
echo "2. 运行应用程序：./build/bin/CivitaiImageViewer"
echo "3. 在设置中配置 API 密钥"
echo "4. 开始搜索和浏览图片！"
echo

# 询问是否立即运行
read -p "是否立即运行应用程序？(y/n): " choice
case "$choice" in 
  y|Y ) 
    echo
    echo "正在启动应用程序..."
    ./bin/CivitaiImageViewer &
    ;;
  * ) 
    echo "您可以稍后手动运行应用程序"
    ;;
esac

echo
echo "测试完成！"
