{"classes": [{"className": "ElaCalendarPrivate", "lineNumber": 14, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "member": "_pZoomRatio", "name": "pZoomRatio", "notify": "pZoomRatioChanged", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "member": "_pPixOpacity", "name": "pPixOpacity", "notify": "pPixOpacityChanged", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "ElaCalendarPrivate", "signals": [{"access": "public", "index": 0, "name": "pZoomRatioChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pPixOpacityChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 2, "name": "onSwitchButtonClicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "QModelIndex"}], "index": 3, "name": "onCalendarViewClicked", "returnType": "void"}, {"access": "public", "index": 4, "name": "onUpButtonClicked", "returnType": "void"}, {"access": "public", "index": 5, "name": "onDownButtonClicked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "ElaCalendarPrivate.h", "outputRevision": 69}