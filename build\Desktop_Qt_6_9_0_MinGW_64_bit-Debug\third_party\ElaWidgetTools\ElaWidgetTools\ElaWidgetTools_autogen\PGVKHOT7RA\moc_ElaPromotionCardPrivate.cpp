/****************************************************************************
** Meta object code from reading C++ file 'ElaPromotionCardPrivate.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/private/ElaPromotionCardPrivate.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaPromotionCardPrivate.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN23ElaPromotionCardPrivateE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaPromotionCardPrivate::qt_create_metaobjectdata<qt_meta_tag_ZN23ElaPromotionCardPrivateE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaPromotionCardPrivate",
        "pPressRadiusChanged",
        "",
        "pHoverOpacityChanged",
        "pPressOpacityChanged",
        "pPressRadius",
        "pHoverOpacity",
        "pPressOpacity"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pPressRadiusChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pHoverOpacityChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pPressOpacityChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pPressRadius'
        QtMocHelpers::PropertyData<qreal>(5, QMetaType::QReal, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pHoverOpacity'
        QtMocHelpers::PropertyData<qreal>(6, QMetaType::QReal, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pPressOpacity'
        QtMocHelpers::PropertyData<qreal>(7, QMetaType::QReal, QMC::DefaultPropertyFlags | QMC::Writable, 2),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaPromotionCardPrivate, qt_meta_tag_ZN23ElaPromotionCardPrivateE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaPromotionCardPrivate::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN23ElaPromotionCardPrivateE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN23ElaPromotionCardPrivateE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN23ElaPromotionCardPrivateE_t>.metaTypes,
    nullptr
} };

void ElaPromotionCardPrivate::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaPromotionCardPrivate *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pPressRadiusChanged(); break;
        case 1: _t->pHoverOpacityChanged(); break;
        case 2: _t->pPressOpacityChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCardPrivate::*)()>(_a, &ElaPromotionCardPrivate::pPressRadiusChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCardPrivate::*)()>(_a, &ElaPromotionCardPrivate::pHoverOpacityChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionCardPrivate::*)()>(_a, &ElaPromotionCardPrivate::pPressOpacityChanged, 2))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<qreal*>(_v) = _t->_pPressRadius; break;
        case 1: *reinterpret_cast<qreal*>(_v) = _t->_pHoverOpacity; break;
        case 2: *reinterpret_cast<qreal*>(_v) = _t->_pPressOpacity; break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0:
            if (QtMocHelpers::setProperty(_t->_pPressRadius, *reinterpret_cast<qreal*>(_v)))
                Q_EMIT _t->pPressRadiusChanged();
            break;
        case 1:
            if (QtMocHelpers::setProperty(_t->_pHoverOpacity, *reinterpret_cast<qreal*>(_v)))
                Q_EMIT _t->pHoverOpacityChanged();
            break;
        case 2:
            if (QtMocHelpers::setProperty(_t->_pPressOpacity, *reinterpret_cast<qreal*>(_v)))
                Q_EMIT _t->pPressOpacityChanged();
            break;
        default: break;
        }
    }
}

const QMetaObject *ElaPromotionCardPrivate::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaPromotionCardPrivate::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN23ElaPromotionCardPrivateE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ElaPromotionCardPrivate::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 3)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 3)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 3;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void ElaPromotionCardPrivate::pPressRadiusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaPromotionCardPrivate::pHoverOpacityChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaPromotionCardPrivate::pPressOpacityChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}
QT_WARNING_POP
