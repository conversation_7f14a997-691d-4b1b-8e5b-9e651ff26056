#include "ImageManager.h"
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QPixmap>
#include <QBuffer>
#include <QImageReader>
#include <QCryptographicHash>
#include <QFileInfo>
#include <QDebug>
#include <QDateTime>

ImageManager::ImageManager(QObject *parent)
    : QObject(parent)
    , m_networkManager(new QNetworkAccessManager(this))
    , m_queueTimer(new QTimer(this))
    , m_memoryCache(new QCache<QString, QPixmap>())
    , m_diskCacheEnabled(true)
    , m_diskCacheMaxSize(1024 * 1024 * 1024) // 1GB
    , m_maxConcurrentDownloads(4)
    , m_downloadTimeoutMs(30000) // 30秒
    , m_maxRetries(3)
{
    // 设置内存缓存大小 (默认50MB)
    m_memoryCache->setMaxCost(50 * 1024); // 50MB in KB

    // 初始化磁盘缓存
    initializeDiskCache();

    // 设置队列处理定时器
    m_queueTimer->setSingleShot(true);
    m_queueTimer->setInterval(100); // 100ms延迟
    connect(m_queueTimer, &QTimer::timeout, this, &ImageManager::processDownloadQueue);

    // 连接网络管理器信号
    connect(m_networkManager, &QNetworkAccessManager::finished,
            this, &ImageManager::onNetworkReply);
}

ImageManager::~ImageManager()
{
    // 取消所有活动下载
    for (auto it = m_activeDownloads.begin(); it != m_activeDownloads.end(); ++it) {
        it.key()->abort();
    }

    delete m_memoryCache;
}

void ImageManager::setMemoryCacheSize(int maxCostKB)
{
    QMutexLocker locker(&m_cacheMutex);
    m_memoryCache->setMaxCost(maxCostKB);
}

int ImageManager::memoryCacheSize() const
{
    QMutexLocker locker(&m_cacheMutex);
    return m_memoryCache->maxCost();
}

void ImageManager::setDiskCacheEnabled(bool enabled)
{
    m_diskCacheEnabled = enabled;
    if (enabled) {
        initializeDiskCache();
    }
}

bool ImageManager::isDiskCacheEnabled() const
{
    return m_diskCacheEnabled;
}

void ImageManager::setDiskCacheDirectory(const QString& directory)
{
    m_diskCacheDir = directory;
    if (m_diskCacheEnabled) {
        initializeDiskCache();
    }
}

QString ImageManager::diskCacheDirectory() const
{
    return m_diskCacheDir;
}

void ImageManager::setDiskCacheMaxSize(qint64 maxSizeBytes)
{
    m_diskCacheMaxSize = maxSizeBytes;
}

qint64 ImageManager::diskCacheMaxSize() const
{
    return m_diskCacheMaxSize;
}

void ImageManager::setMaxConcurrentDownloads(int maxDownloads)
{
    m_maxConcurrentDownloads = qMax(1, maxDownloads);
}

int ImageManager::maxConcurrentDownloads() const
{
    return m_maxConcurrentDownloads;
}

void ImageManager::setDownloadTimeout(int timeoutMs)
{
    m_downloadTimeoutMs = qMax(1000, timeoutMs);
}

int ImageManager::downloadTimeout() const
{
    return m_downloadTimeoutMs;
}

void ImageManager::loadImage(int imageId, const QString& url, const QSize& preferredSize)
{
    if (url.isEmpty()) {
        emit imageLoadFailed(imageId, url, "URL为空");
        return;
    }

    // 检查缓存
    QString cacheKey = generateCacheKey(url, preferredSize);
    QPixmap cachedPixmap = getCachedImage(url, preferredSize);

    if (!cachedPixmap.isNull()) {
        emit imageLoaded(imageId, url, cachedPixmap);
        return;
    }

    // 添加到下载队列
    DownloadRequest request(imageId, url, preferredSize);
    m_downloadQueue.enqueue(request);

    // 启动队列处理
    if (!m_queueTimer->isActive()) {
        m_queueTimer->start();
    }
}

void ImageManager::preloadImage(const QString& url, const QSize& preferredSize)
{
    loadImage(-1, url, preferredSize); // -1表示预加载
}

void ImageManager::clearMemoryCache()
{
    QMutexLocker locker(&m_cacheMutex);
    m_memoryCache->clear();
}

void ImageManager::clearDiskCache()
{
    if (!m_diskCacheEnabled || m_diskCacheDir.isEmpty()) {
        return;
    }

    QDir cacheDir(m_diskCacheDir);
    if (cacheDir.exists()) {
        cacheDir.removeRecursively();
        initializeDiskCache();
    }
}

void ImageManager::clearAllCache()
{
    clearMemoryCache();
    clearDiskCache();
}

bool ImageManager::isImageCached(const QString& url, const QSize& size) const
{
    QString cacheKey = generateCacheKey(url, size);

    // 检查内存缓存
    {
        QMutexLocker locker(&m_cacheMutex);
        if (m_memoryCache->contains(cacheKey)) {
            return true;
        }
    }

    // 检查磁盘缓存
    if (m_diskCacheEnabled) {
        QString filePath = getCacheFilePath(cacheKey);
        return QFileInfo::exists(filePath);
    }

    return false;
}

QPixmap ImageManager::getCachedImage(const QString& url, const QSize& size) const
{
    QString cacheKey = generateCacheKey(url, size);

    // 先检查内存缓存
    QPixmap pixmap = getImageFromMemoryCache(cacheKey);
    if (!pixmap.isNull()) {
        return pixmap;
    }

    // 再检查磁盘缓存
    if (m_diskCacheEnabled) {
        pixmap = getImageFromDiskCache(cacheKey);
        if (!pixmap.isNull()) {
            // 将从磁盘加载的图片放入内存缓存
            cacheImageInMemory(cacheKey, pixmap);
            return pixmap;
        }
    }

    return QPixmap();
}

int ImageManager::pendingDownloads() const
{
    return m_downloadQueue.size();
}

int ImageManager::activeDownloads() const
{
    return m_activeDownloads.size();
}

qint64 ImageManager::diskCacheUsage() const
{
    if (!m_diskCacheEnabled) {
        return 0;
    }

    return calculateDiskCacheSize();
}

int ImageManager::memoryCacheUsage() const
{
    QMutexLocker locker(&m_cacheMutex);
    return m_memoryCache->totalCost();
}

void ImageManager::onNetworkReply()
{
    QNetworkReply* reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) {
        return;
    }

    // 获取下载请求信息
    DownloadRequest request = m_activeDownloads.take(reply);

    if (reply->error() == QNetworkReply::NoError) {
        // 下载成功，处理图片数据
        QByteArray imageData = reply->readAll();

        if (isValidImageData(imageData)) {
            QPixmap pixmap = processDownloadedImage(imageData, request.preferredSize);

            if (!pixmap.isNull()) {
                // 缓存图片
                QString cacheKey = generateCacheKey(request.url, request.preferredSize);
                cacheImageInMemory(cacheKey, pixmap);

                if (m_diskCacheEnabled) {
                    cacheImageOnDisk(cacheKey, pixmap);
                }

                // 发送成功信号
                emit imageLoaded(request.imageId, request.url, pixmap);
            } else {
                emit imageLoadFailed(request.imageId, request.url, "图片处理失败");
            }
        } else {
            emit imageLoadFailed(request.imageId, request.url, "无效的图片数据");
        }
    } else {
        // 下载失败，检查是否需要重试
        QString errorMsg = getErrorMessage(reply);

        if (request.retryCount < m_maxRetries) {
            request.retryCount++;
            retryDownload(request);
        } else {
            emit imageLoadFailed(request.imageId, request.url, errorMsg);
        }
    }

    reply->deleteLater();

    // 处理下一个下载
    if (!m_downloadQueue.isEmpty() && !m_queueTimer->isActive()) {
        m_queueTimer->start();
    }
}

void ImageManager::onDownloadProgress(qint64 bytesReceived, qint64 bytesTotal)
{
    QNetworkReply* reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply || !m_activeDownloads.contains(reply)) {
        return;
    }

    DownloadRequest request = m_activeDownloads[reply];
    emit downloadProgress(request.url, bytesReceived, bytesTotal);
}

void ImageManager::processDownloadQueue()
{
    while (!m_downloadQueue.isEmpty() && canStartNewDownload()) {
        DownloadRequest request = m_downloadQueue.dequeue();
        startDownload(request);
    }
}

void ImageManager::initializeDiskCache()
{
    if (m_diskCacheDir.isEmpty()) {
        m_diskCacheDir = QStandardPaths::writableLocation(QStandardPaths::CacheLocation) + "/images";
    }

    QDir cacheDir;
    if (!cacheDir.exists(m_diskCacheDir)) {
        cacheDir.mkpath(m_diskCacheDir);
    }
}

QString ImageManager::generateCacheKey(const QString& url, const QSize& size) const
{
    QString key = url;
    if (size.isValid()) {
        key += QString("_%1x%2").arg(size.width()).arg(size.height());
    }

    // 使用MD5生成缓存键
    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(key.toUtf8());
    return hash.result().toHex();
}

QString ImageManager::getCacheFilePath(const QString& cacheKey) const
{
    return m_diskCacheDir + "/" + cacheKey + ".cache";
}

void ImageManager::cacheImageInMemory(const QString& cacheKey, const QPixmap& pixmap)
{
    QMutexLocker locker(&m_cacheMutex);

    // 计算图片大小（KB）
    int cost = pixmap.width() * pixmap.height() * pixmap.depth() / 8 / 1024;
    m_memoryCache->insert(cacheKey, new QPixmap(pixmap), cost);
}

QPixmap ImageManager::getImageFromMemoryCache(const QString& cacheKey) const
{
    QMutexLocker locker(&m_cacheMutex);

    QPixmap* pixmap = m_memoryCache->object(cacheKey);
    return pixmap ? *pixmap : QPixmap();
}

void ImageManager::cacheImageOnDisk(const QString& cacheKey, const QPixmap& pixmap)
{
    if (!m_diskCacheEnabled || m_diskCacheDir.isEmpty()) {
        return;
    }

    QString filePath = getCacheFilePath(cacheKey);
    pixmap.save(filePath, "PNG");

    // 检查磁盘缓存大小并清理
    if (calculateDiskCacheSize() > m_diskCacheMaxSize) {
        cleanupDiskCache();
    }
}

QPixmap ImageManager::getImageFromDiskCache(const QString& cacheKey) const
{
    if (!m_diskCacheEnabled) {
        return QPixmap();
    }

    QString filePath = getCacheFilePath(cacheKey);
    if (QFileInfo::exists(filePath)) {
        return QPixmap(filePath);
    }

    return QPixmap();
}

void ImageManager::cleanupDiskCache()
{
    if (!m_diskCacheEnabled || m_diskCacheDir.isEmpty()) {
        return;
    }

    QDir cacheDir(m_diskCacheDir);
    QFileInfoList files = cacheDir.entryInfoList(QStringList() << "*.cache", QDir::Files, QDir::Time);

    qint64 totalSize = 0;
    for (const QFileInfo& fileInfo : files) {
        totalSize += fileInfo.size();
    }

    // 删除最旧的文件直到大小在限制内
    for (const QFileInfo& fileInfo : files) {
        if (totalSize <= m_diskCacheMaxSize) {
            break;
        }

        totalSize -= fileInfo.size();
        QFile::remove(fileInfo.absoluteFilePath());
    }
}

qint64 ImageManager::calculateDiskCacheSize() const
{
    if (!m_diskCacheEnabled || m_diskCacheDir.isEmpty()) {
        return 0;
    }

    QDir cacheDir(m_diskCacheDir);
    QFileInfoList files = cacheDir.entryInfoList(QStringList() << "*.cache", QDir::Files);

    qint64 totalSize = 0;
    for (const QFileInfo& fileInfo : files) {
        totalSize += fileInfo.size();
    }

    return totalSize;
}

void ImageManager::startDownload(const DownloadRequest& request)
{
    QNetworkRequest netRequest(request.url);
    netRequest.setHeader(QNetworkRequest::UserAgentHeader, "CivitaiImageViewer/1.0.0");
    netRequest.setAttribute(QNetworkRequest::RedirectPolicyAttribute, QNetworkRequest::NoLessSafeRedirectPolicy);

    QNetworkReply* reply = m_networkManager->get(netRequest);
    reply->setProperty("timeout", m_downloadTimeoutMs);

    // 连接进度信号
    connect(reply, &QNetworkReply::downloadProgress,
            this, &ImageManager::onDownloadProgress);

    m_activeDownloads[reply] = request;
}

void ImageManager::retryDownload(const DownloadRequest& request)
{
    m_downloadQueue.enqueue(request);

    if (!m_queueTimer->isActive()) {
        m_queueTimer->start();
    }
}

bool ImageManager::canStartNewDownload() const
{
    return m_activeDownloads.size() < m_maxConcurrentDownloads;
}

QPixmap ImageManager::processDownloadedImage(const QByteArray& imageData, const QSize& preferredSize) const
{
    QBuffer buffer;
    buffer.setData(imageData);
    buffer.open(QIODevice::ReadOnly);

    QImageReader reader(&buffer);
    QImage image = reader.read();

    if (image.isNull()) {
        return QPixmap();
    }

    QPixmap pixmap = QPixmap::fromImage(image);

    // 如果指定了首选大小，进行缩放
    if (preferredSize.isValid() && pixmap.size() != preferredSize) {
        pixmap = scaleImage(pixmap, preferredSize);
    }

    return pixmap;
}

QPixmap ImageManager::scaleImage(const QPixmap& pixmap, const QSize& targetSize) const
{
    return pixmap.scaled(targetSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);
}

QString ImageManager::getErrorMessage(QNetworkReply* reply) const
{
    switch (reply->error()) {
    case QNetworkReply::NoError:
        return QString();
    case QNetworkReply::TimeoutError:
        return "下载超时";
    case QNetworkReply::HostNotFoundError:
        return "主机未找到";
    case QNetworkReply::ConnectionRefusedError:
        return "连接被拒绝";
    case QNetworkReply::ContentNotFoundError:
        return "内容未找到";
    default:
        return reply->errorString();
    }
}

bool ImageManager::isValidImageData(const QByteArray& data) const
{
    if (data.isEmpty()) {
        return false;
    }

    // 检查常见图片格式的文件头
    if (data.startsWith("\xFF\xD8\xFF")) return true; // JPEG
    if (data.startsWith("\x89PNG\r\n\x1A\n")) return true; // PNG
    if (data.startsWith("GIF87a") || data.startsWith("GIF89a")) return true; // GIF
    if (data.startsWith("RIFF") && data.mid(8, 4) == "WEBP") return true; // WebP
    if (data.startsWith("BM")) return true; // BMP

    return false;
}
