{"classes": [{"className": "ElaApplicationType", "enums": [{"isClass": false, "isFlag": false, "name": "WindowDisplayMode", "values": ["Normal", "ElaMica", "Mica", "MicaAlt", "Acrylic", "DWMBlur"]}], "lineNumber": 33, "namespace": true, "qualifiedClassName": "ElaApplicationType"}, {"className": "ElaThemeType", "enums": [{"isClass": false, "isFlag": false, "name": "ThemeMode", "values": ["Light", "Dark"]}, {"isClass": false, "isFlag": false, "name": "ThemeColor", "values": ["ScrollBarHandle", "ToggleSwitchNoToggledCenter", "WindowBase", "WindowCentralStackBase", "PrimaryNormal", "PrimaryHover", "PrimaryPress", "PopupBorder", "PopupBorderHover", "PopupBase", "PopupHover", "DialogBase", "DialogLayoutArea", "BasicText", "BasicTextInvert", "BasicDetailsText", "BasicTextNoFocus", "BasicTextDisable", "BasicTextPress", "BasicBorder", "BasicBorderDeep", "BasicBorderHover", "BasicBase", "BasicBaseDeep", "BasicDisable", "BasicHover", "BasicPress", "BasicSelectedHover", "BasicBaseLine", "BasicHemline", "BasicIndicator", "BasicChute", "BasicAlternating", "BasicBaseAlpha", "BasicBaseDeepAlpha", "BasicHoverAlpha", "BasicPressAlpha", "BasicSelectedAlpha", "BasicSelectedHoverAlpha", "StatusDanger"]}], "lineNumber": 48, "namespace": true, "qualifiedClassName": "ElaThemeType"}, {"className": "ElaAppBarType", "enums": [{"isClass": false, "isFlag": false, "name": "ButtonType", "values": ["RouteBackButtonHint", "NavigationButtonHint", "StayTopButtonHint", "ThemeChangeButtonHint", "MinimizeButtonHint", "MaximizeButtonHint", "CloseButtonHint"]}, {"isClass": false, "isFlag": false, "name": "CustomArea", "values": ["LeftArea", "MiddleArea", "RightArea"]}, {"isClass": false, "isFlag": false, "name": "WMMouseActionType", "values": ["WMLBUTTONDOWN", "WMLBUTTONUP", "WMLBUTTONDBLCLK", "WMNCLBUTTONDOWN"]}], "lineNumber": 102, "namespace": true, "qualifiedClassName": "ElaAppBarType"}, {"className": "ElaTextType", "enums": [{"isClass": false, "isFlag": false, "name": "TextStyle", "values": ["NoStyle", "Caption", "Body", "BodyStrong", "Subtitle", "Title", "TitleLarge", "Display"]}], "lineNumber": 137, "namespace": true, "qualifiedClassName": "ElaTextType"}, {"className": "ElaNavigationType", "enums": [{"isClass": false, "isFlag": false, "name": "NodeOperateReturnType", "values": ["Success", "TargetNodeInvalid", "TargetNodeTypeError", "TargetNodeDepthLimit", "PageInvalid", "FooterUpperLimit"]}, {"isClass": false, "isFlag": false, "name": "NavigationDisplayMode", "values": ["Auto", "Minimal", "Compact", "Maximal"]}, {"isClass": false, "isFlag": false, "name": "NavigationNodeType", "values": ["PageNode", "FooterNode"]}], "lineNumber": 152, "namespace": true, "qualifiedClassName": "ElaNavigationType"}, {"className": "ElaNavigationRouterType", "enums": [{"isClass": false, "isFlag": false, "name": "NavigationRouteType", "values": ["Success", "ObjectInvalid", "FunctionNameInvalid"]}], "lineNumber": 181, "namespace": true, "qualifiedClassName": "ElaNavigationRouterType"}, {"className": "ElaEventBusType", "enums": [{"isClass": false, "isFlag": false, "name": "EventBusReturnType", "values": ["Success", "EventInvalid", "EventNameInvalid"]}], "lineNumber": 191, "namespace": true, "qualifiedClassName": "ElaEventBusType"}, {"className": "ElaCardPixType", "enums": [{"isClass": false, "isFlag": false, "name": "PixMode", "values": ["<PERSON><PERSON><PERSON>", "RoundedRect", "Ellipse"]}], "lineNumber": 202, "namespace": true, "qualifiedClassName": "ElaCardPixType"}, {"className": "ElaGraphicsSceneType", "enums": [{"isClass": false, "isFlag": false, "name": "SceneMode", "values": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MultiSelect", "ItemLink"]}], "lineNumber": 212, "namespace": true, "qualifiedClassName": "ElaGraphicsSceneType"}, {"className": "ElaMessageBarType", "enums": [{"isClass": false, "isFlag": false, "name": "PositionPolicy", "values": ["Top", "Left", "Bottom", "Right", "TopRight", "TopLeft", "BottomRight", "BottomLeft"]}, {"isClass": false, "isFlag": false, "name": "MessageMode", "values": ["Success", "Warning", "Information", "Error"]}], "lineNumber": 223, "namespace": true, "qualifiedClassName": "ElaMessageBarType"}, {"className": "ElaProgressRingType", "enums": [{"isClass": false, "isFlag": false, "name": "ValueDisplayMode", "values": ["Actual", "Percent"]}], "lineNumber": 247, "namespace": true, "qualifiedClassName": "ElaProgressRingType"}, {"className": "ElaIconType", "enums": [{"isClass": false, "isFlag": false, "name": "IconName", "values": ["None", "Broom", "Number00", "Numbe0", "Numbe1", "Numbe2", "Numbe3", "Numbe4", "Numbe5", "Numbe6", "Numbe7", "Numbe8", "Numbe9", "Degrees360", "A", "Abacus", "AccentGrave", "Acorn", "AddressBook", "AddressCard", "AirConditioner", "Airplay", "AlarmClock", "AlarmExclamation", "AlarmPlus", "Album", "AlbumCirclePlus", "AlbumCircleUser", "AlbumCollection", "AlbumCollectionCirclePlus", "AlarmSnooze", "AlbumCollectionCircleUser", "Alicorn", "Alien8bit", "AlignCenter", "AlignLeft", "AlignRight", "Ampersand", "Alt", "AnchorCircleExclamation", "AlignSlash", "Apartment", "<PERSON><PERSON>", "AngleUp", "AnglesUpDown", "AnglesRight", "AnglesUp", "AnglesDown", "AnglesLeft", "AngleRight", "AngleDown", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Alien", "Angle90", "Angel", "<PERSON><PERSON>", "AnchorCircleXmark", "AnchorLock", "<PERSON><PERSON>", "AlignJustify", "AmpG<PERSON>ar", "Aperture", "Apostrophe", "AppleCore", "AngleLeft", "AppleWhole", "Archway", "ArrowDown", "ArrowDown19", "ArrowDownUpAcrossLine", "ArrowDown91", "ArrowDownArrowUp", "ArrowDownAZ", "ArrowDownBigSmall", "ArrowDownFromArc", "ArrowDownFromDottedLine", "ArrowDownFromLine", "ArrowDownLeft", "ArrowDownLeftAndArrowUpRightToCenter", "ArrowDownLong", "ArrowDownRight", "ArrowDownShortWide", "ArrowDownUpLock", "ArrowDownWideShort", "ArrowDownZA", "ArrowLeft", "ArrowLeftFromArc", "ArrowLeftFromLine", "ArrowLeftLong", "ArrowLeftLongToLine", "ArrowLeftToArc", "<PERSON><PERSON><PERSON><PERSON>", "ArrowDownToSquare", "ArrowLeftToLine", "ArrowRightToArc", "ArrowRightToBracket", "ArrowRightToCity", "ArrowRightToLine", "ArrowRotateLeft", "ArrowRotateRight", "ArrowsCross", "ArrowsDownToLine", "ArrowsDownToPeople", "ArrowsFromDottedLine", "ArrowsFromLine", "ArrowsLeftRight", "ArrowsSpin", "ArrowsSplitUpAndLeft", "ArrowsToCircle", "ArrowsToDot", "ArrowsToDottedLine", "ArrowsToEye", "ArrowsToLine", "ArrowsTurnRight", "ArrowsTurnToDots", "ArrowsUpDown", "ArrowsUpDownLeftRight", "ArrowsUpToLine", "ArrowTurnRight", "ArrowTurnUp", "ArrowUp", "ArrowUp19", "ArrowUp91", "ArrowUpArrowDown", "ArrowUpAZ", "ArrowUpBigSmall", "ArrowUpFromArc", "ArrowUpFromBracket", "ArrowUpFromDottedLine", "ArrowUpFromGroundWater", "ArrowUpRightDots", "ArrowUpRightFromSquare", "ArrowUpShortWide", "ArrowUpSmallBig", "ArrowUpSquareTriangle", "ArrowUpToArc", "ArrowUpToDottedLine", "ArrowUpToLine", "ArrowUpTriangleSquare", "ArrowUpWideShort", "ArrowUpZA", "Asterisk", "AwardSimple", "Axe", "AxeBattle", "B", "Baby", "BabyCarriage", "Backpack", "Backward", "BackwardFast", "BackwardStep", "<PERSON>", "Bacteria", "<PERSON><PERSON>", "BagSeedling", "BagShopping", "BagShoppingMinus", "BagShoppingPlus", "BagsShopping", "Baguette", "Bahai", "BahtSign", "Balloon", "Balloons", "<PERSON><PERSON>", "ArrowDownSquareTriangle", "ArrowDownSmallBig", "ArrowDownToArc", "ArrowDownToBracket", "ArrowDownToDottedLine", "ArrowDownToLine", "ArrowDownTriangleSquare", "ArrowProgress", "ArrowRight", "ArrowRightArrowLeft", "ArrowRightFromArc", "ArrowRightFromBracket", "ArrowRightFromLine", "ArrowRightLong", "ArrowRightLongToLine", "ArrowsLeftRightToLine", "ArrowsMaximize", "ArrowsMinimize", "ArrowsRepeat", "ArrowsRepeat1", "ArrowsRetweet", "ArrowsRotate", "ArrowsRotateReverse", "ArrowTrendDown", "ArrowTrendUp", "ArrowTurnDown", "ArrowTurnDownLeft", "ArrowTurnDownRight", "ArrowTurnLeft", "ArrowTurnLeftDown", "ArrowTurnLeftUp", "ArrowUpFromLine", "ArrowUpFromSquare", "ArrowUpFromWaterPump", "ArrowUpLeft", "ArrowUpLeftFromCircle", "ArrowUpLong", "ArrowUpRight", "ArrowUpRightAndArrowDownLeftFromCenter", "At", "Atom", "AtomSimple", "AudioDescription", "AudioDescriptionSlash", "AustralSign", "Avocado", "Award", "Badge", "Bacterium", "BadgeCheck", "BadgeDollar", "BadgePercent", "<PERSON><PERSON><PERSON><PERSON>", "BadgeSheriff", "Bad<PERSON>ton", "BallotCheck", "BallPile", "Ban", "Banana", "BanBug", "Bandage", "BangladeshiTakaSign", "<PERSON><PERSON>", "BanParking", "BanSmoking", "Barcode", "BarcodeRead", "BarcodeScan", "Bars", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BarsProgress", "BarsSort", "BarsStaggered", "Baseball", "BaseballBatBall", "Basketball", "BasketballHoop", "BasketShopping", "BasketShoppingMinus", "BasketShoppingPlus", "BasketShoppingSimple", "Bat", "Bath", "BatteryBolt", "BatteryEmpty", "BatteryExclamation", "BatteryFull", "BatteryHalf", "BatteryLow", "BatteryQuarter", "BatterySlash", "BatteryThreeQuarters", "Bed", "BedBunk", "BedEmpty", "BedFront", "BedPulse", "Bee", "BeerMug", "BeerMugEmpty", "Bell", "BellConcierge", "BellExclamation", "BellOn", "BellPlus", "BellRing", "Bells", "BellSchool", "BellSchoolSlash", "BellSlash", "BenchTree", "BezierCurve", "Bicycle", "Billboard", "Binary", "BinaryCircleCheck", "BinaryLock", "BinarySlash", "BinBottles", "BinBottlesRecycle", "Binoculars", "BinRecycle", "Biohazard", "<PERSON>", "BitcoinSign", "Blanket", "BlanketFire", "<PERSON><PERSON>der", "BlenderPhone", "Blinds", "BlindsOpen", "BlindsRaised", "Block", "BlockBrick", "BlockBrickFire", "BlockQuestion", "BlockQuote", "Blog", "Blueberries", "Bluetooth", "Bold", "<PERSON><PERSON>", "BoltAuto", "BoltLightning", "BoltSlash", "Bomb", "Bone", "BoneBreak", "<PERSON><PERSON>", "Book", "BookArrowRight", "BookArrowUp", "BookAtlas", "BookBible", "BookBlank", "BookBookmark", "BookCircleArrowRight", "BookCircleArrowUp", "BookCopy", "BookFont", "Book<PERSON>eart", "BookJournalWhills", "Bookmark", "BookmarkSlash", "BookMedical", "BookOpen", "BookOpenCover", "BookOpenReader", "BookQuran", "Books", "BookSection", "BookSkull", "BooksMedical", "BookSparkles", "<PERSON><PERSON><PERSON><PERSON>", "BookUser", "Boombox", "Boot", "<PERSON><PERSON><PERSON><PERSON>", "BootHeeled", "BorderAll", "BorderBottom", "BorderBottomRight", "BorderCenterH", "BorderCenterV", "BorderInner", "BorderLeft", "BorderNone", "BorderOuter", "BorderRight", "BorderTop", "BorderTopLeft", "BoreHole", "BottleDroplet", "BottleWater", "BowArrow", "BowlChopsticks", "BowlChopsticksNoodles", "BowlFood", "BowlHot", "BowlingBall", "BowlingBallPin", "BowlingPins", "BowlRice", "BowlScoop", "BowlScoops", "BowlSoftServe", "BowlSpoon", "Box", "BoxArchive", "BoxBallot", "BoxCheck", "BoxCircleCheck", "BoxDollar", "BoxesPacking", "BoxesStacked", "BoxHeart", "BoxingGlove", "BoxOpen", "BoxOpenFull", "BoxTaped", "BoxTissue", "BracketCurly", "BracketCurlyRight", "BracketRound", "BracketRoundRight", "BracketsCurly", "BracketSquare", "BracketSquareRight", "BracketsRound", "BracketsSquare", "Braille", "Brain", "BrainArrowCurvedRight", "BrainCircuit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BrazilianRealSign", "BreadLoaf", "BreadSlice", "BreadSliceButter", "Bridge", "BridgeCircleCheck", "BridgeCircleExclamation", "BridgeCircleXmark", "BridgeLock", "BridgeSuspension", "BridgeWater", "Briefcase", "BriefcaseArrowRight", "BriefcaseBlank", "BriefcaseMedical", "Brightness", "BrightnessLow", "BringForward", "BringFront", "<PERSON><PERSON><PERSON><PERSON>", "Clover", "BroomBall", "BroomWide", "Browser", "Browsers", "Brush", "Bucket", "Bug", "Bugs", "BugSlash", "Building", "BuildingCircleArrowRight", "BuildingCircleCheck", "BuildingCircleExclamation", "BuildingCircleXmark", "BuildingColumns", "BuildingFlag", "BuildingLock", "BuildingMagnifyingGlass", "BuildingMemo", "BuildingNgo", "Buildings", "BuildingShield", "BuildingUn", "BuildingUser", "BuildingWheat", "Bulldozer", "Bullhorn", "Bullseye", "BullseyeArrow", "BullseyePointer", "Buoy", "BuoyMooring", "Burger", "Burger<PERSON><PERSON><PERSON>", "BurgerFries", "BurgerGlass", "Burger<PERSON><PERSON><PERSON>", "BurgerSoda", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Bus", "BusinessTime", "BusSchool", "BusSimple", "Butter", "C", "Cabin", "CabinetFiling", "CableCar", "Cactus", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CakeSlice", "Calculator", "CalculatorSimple", "Calendar", "CalendarArrowDown", "CalendarArrowUp", "CalendarCheck", "CalendarCircleExclamation", "CalendarCircleMinus", "CalendarCirclePlus", "CalendarCircleUser", "CalendarClock", "CalendarDay", "CalendarDays", "CalendarExclamation", "CalendarImage", "CalendarLines", "CalendarMinus", "CalendarHeart", "CalendarLinesPen", "CalendarPlus", "Calendars", "CalendarPen", "CalendarStar", "CalendarWeek", "CalendarXmark", "CameraCctv", "CalendarUsers", "CameraMovie", "Camcorder", "CameraRetro", "CameraRotate", "CameraSecurity", "CameraPolaroid", "CameraSlash", "CalendarRange", "CameraViewfinder", "Camera", "CameraWeb", "Campground", "CandleHolder", "CameraWebSlash", "Campfire", "<PERSON>", "CandyCane", "CandyBar", "CanFood", "CandyCorn", "Cannabis", "<PERSON>", "Capsules", "Car", "Caravan", "CaravanSimple", "CarBattery", "CarBolt", "CarBuilding", "CarBump", "CarBurst", "CarBus", "CarCircleBolt", "CardClub", "<PERSON><PERSON><PERSON><PERSON>", "Card<PERSON>eart", "Cards", "CardsBlank", "CardSpade", "CaretDown", "CaretLeft", "CaretRight", "CaretUp", "CarGarage", "CarMirrors", "CarOn", "CarRear", "Carrot", "Cars", "CarSide", "CarSideBolt", "CartArrowDown", "CartArrowUp", "CartCircleArrowDown", "CartCircleArrowUp", "CartCircleCheck", "CartCircleExclamation", "CartCirclePlus", "CartCircleXmark", "CartFlatbed", "CartFlatbedBoxes", "CartFlatbedEmpty", "CartFlatbedSuitcase", "CarTilt", "CartMinus", "CartShopping", "CartPlus", "CartShoppingFast", "CarTunnel", "CartXmark", "CarWash", "CarWrench", "CashRegister", "CassetteBetamax", "CassetteTape", "CassetteVhs", "Castle", "Cat", "CatSpace", "<PERSON><PERSON><PERSON>", "CediSign", "CentSign", "Certificate", "Chair", "ChairOffice", "Chalkboard", "ChalkboardUser", "ChampagneGlass", "ChampagneGlasses", "ChargingStation", "ChartArea", "ChartBar", "ChartBullet", "ChartCandlestick", "ChartColumn", "ChartGantt", "ChartKanban", "ChartLine", "ChartLineDown", "ChartLineUp", "ChartLineUpDown", "ChartMixed", "ChartMixedUpCircleCurrency", "ChartMixedUpCircleDollar", "ChartNetwork", "ChartPie", "Chart<PERSON>ie<PERSON><PERSON>ple", "ChartPieSimpleCircleCurrency", "ChartPieSimpleCircleDollar", "ChartPyramid", "ChartRadar", "ChartScatter", "ChartScatter3d", "ChartScatterBubble", "ChartSimple", "ChartSimpleHorizontal", "ChartTreeMap", "ChartUser", "ChartWaterfall", "Check", "CheckDouble", "CheckToSlot", "Cheese", "CheeseSwiss", "Cherries", "Chess", "ChessBishop", "ChessBishopPiece", "ChessBoard", "ChessClock", "ChessClockFlip", "ChessKing", "ChessKingPiece", "ChessKnight", "ChessKnightPiece", "ChessPawn", "ChessPawn<PERSON><PERSON>ce", "<PERSON><PERSON><PERSON><PERSON>", "Chess<PERSON>ueen<PERSON><PERSON>", "ChessRook", "ChessRook<PERSON><PERSON>ce", "Chestnut", "ChevronDown", "ChevronLeft", "ChevronRight", "ChevronsDown", "ChevronsLeft", "ChevronsRight", "ChevronsUp", "ChevronUp", "ChfSign", "Child", "ChildCombatant", "ChildDress", "ChildReaching", "Children", "<PERSON><PERSON><PERSON>", "Chopsticks", "Church", "Circle", "Circle0", "Circle1", "Circle2", "Circle3", "Circle4", "Circle5", "Circle6", "Circle7", "Circle8", "Circle9", "CircleA", "CircleAmpersand", "CircleArrowDown", "CircleArrowDownLeft", "CircleArrowDownRight", "CircleArrowLeft", "CircleArrowRight", "CircleArrowUp", "CircleArrowUpLeft", "CircleArrowUpRight", "CircleB", "CircleBolt", "CircleBookmark", "CircleBookOpen", "CircleC", "CircleCalendar", "CircleCamera", "CircleCaretDown", "CircleCaretLeft", "CircleCaretRight", "CircleCaretUp", "CircleCheck", "CircleChevronDown", "CircleChevronLeft", "CircleChevronRight", "CircleChevronUp", "CircleD", "CircleDashed", "CircleDivide", "CircleDollar", "CircleDollarToSlot", "CircleDot", "CircleDown", "CircleDownLeft", "CircleDownRight", "CircleE", "CircleEllipsis", "CircleEllipsisVertical", "CircleEnvelope", "CircleEuro", "CircleExclamation", "CircleExclamationCheck", "CircleF", "CircleG", "CircleH", "CircleHalf", "CircleHalfStroke", "CircleHeart", "CircleI", "CircleInfo", "CircleJ", "CircleK", "CircleL", "CircleLeft", "CircleLocationArrow", "CircleM", "CircleMicrophone", "CircleMicrophoneLines", "CircleMinus", "CircleN", "CircleNodes", "CircleNotch", "CircleO", "CircleP", "CircleParking", "CirclePause", "CirclePhone", "CirclePhoneFlip", "CirclePhoneHangup", "CirclePlay", "CirclePlus", "CircleQ", "CircleQuarter", "CircleQuarters", "CircleQuarterStroke", "CircleQuestion", "CircleR", "CircleRadiation", "CircleRight", "CircleS", "CircleSmall", "CircleSort", "CircleSortDown", "CircleSortUp", "CirclesOverlap", "CircleStar", "CircleSterling", "CircleStop", "CircleT", "CircleThreeQuarters", "CircleThreeQuartersStroke", "CircleTrash", "CircleU", "CircleUp", "CircleUpLeft", "CircleUpRight", "CircleUser", "CircleV", "CircleVideo", "CircleW", "CircleWaveformLines", "CircleX", "CircleXmark", "CircleY", "CircleYen", "CircleZ", "Citrus", "CitrusSlice", "City", "Clapperboard", "ClapperboardPlay", "Clarinet", "ClawMarks", "Clipboard", "ClipboardCheck", "ClipboardList", "ClipboardListCheck", "ClipboardMedical", "ClipboardPrescription", "ClipboardQuestion", "ClipboardUser", "Clock", "ClockDesk", "ClockEight", "ClockEightThirty", "ClockEleven", "ClockElevenThirty", "ClockFive", "ClockFiveThirty", "ClockFourThirty", "ClockNine", "Clock<PERSON>ineThirty", "ClockOne", "ClockOneThirty", "ClockRotateLeft", "ClockSeven", "ClockSevenThirty", "ClockSix", "ClockSixThirty", "ClockTen", "ClockTenThirty", "ClockThree", "ClockThreeThirty", "ClockTwelve", "ClockTwelveThirty", "ClockTwo", "ClockTwoThirty", "<PERSON><PERSON>", "ClosedCaptioning", "ClosedCaptioningSlash", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cloud", "CloudArrowDown", "CloudArrowUp", "CloudBinary", "CloudBolt", "CloudBoltMoon", "CloudBoltSun", "CloudCheck", "CloudDrizzle", "CloudExclamation", "CloudFog", "CloudHail", "CloudHailMixed", "CloudMeatball", "CloudMinus", "CloudMoon", "CloudMoonRain", "CloudMusic", "CloudPlus", "CloudQuestion", "CloudRain", "CloudRainbow", "Clouds", "CloudShowers", "CloudShowersHeavy", "CloudShowersWater", "CloudSlash", "CloudSleet", "CloudsMoon", "CloudSnow", "CloudsSun", "CloudSun", "CloudSunRain", "CloudWord", "CloudXmark", "FaceSaluting", "Club", "Coconut", "Code", "CodeBranch", "CodeCommit", "CodeCompare", "CodeFork", "CodePullRequest", "CodePullRequestClosed", "CodeMerge", "CodePullRequestDraft", "CodeSimple", "CoffeeBean", "CoffeeBeans", "CoffeePot", "CoffinCross", "<PERSON><PERSON>", "Coin", "CoinBlank", "CoinVertical", "CoinFront", "Colon", "Coins", "ColonSign", "Columns3", "Comet", "Comma", "Comment", "CommentArrowDown", "CommentArrowUp", "CommentArrowUpRight", "CommentCaptions", "CommentCheck", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CommentDots", "CommentCode", "Command", "CommentImage", "CommentHeart", "CommentExclamation", "CommentLines", "CommentMedical", "CommentMiddle", "CommentMiddleTop", "CommentMinus", "CommentMusic", "CommentPen", "CommentPlus", "CommentQuestion", "CommentQuote", "Comments", "Comments<PERSON>ollar", "CommentSlash", "CommentSmile", "CommentSms", "CommentsQuestion", "CommentsQuestionCheck", "CommentText", "CommentXmark", "CompactDisc", "<PERSON>mp<PERSON>", "CompassDrafting", "CompassSlash", "Compress", "CompressWide", "ComputerClassic", "ComputerMouse", "ComputerMouseScrollwheel", "ComputerSpeaker", "ContainerStorage", "ConveyorBelt", "ConveyorBeltBoxes", "ConveyorBeltArm", "CookieBite", "Computer", "Copyright", "<PERSON><PERSON>", "Corn", "Corner", "Copy", "CourtSport", "Cow", "<PERSON><PERSON>", "CowbellCirclePlus", "<PERSON><PERSON><PERSON>", "CrateEmpty", "CreditCardFront", "<PERSON><PERSON>", "CreditCardBlank", "CreditCard", "CrateApple", "ConveyorBeltEmpty", "Crop", "Crosshairs", "CropSimple", "Cross", "Croissant", "CricketBatBall", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Crow", "Crown", "<PERSON><PERSON><PERSON>", "Crutches", "CruzeiroSign", "CrystalBall", "C<PERSON>", "Cubes", "CubesStacked", "<PERSON><PERSON><PERSON>ber", "Cupcake", "CupStraw", "CupStrawSwoosh", "CupTogo", "CurlingStone", "<PERSON><PERSON><PERSON>", "D", "<PERSON>gger", "Dash", "Database", "Deer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DeleteLeft", "DeleteRight", "Desktop", "Democrat", "DesktopArrowDown", "Dharmachakra", "DiagramCells", "DiagramLeanC<PERSON>vas", "DiagramNested", "DiagramNext", "DiagramPredecessor", "DiagramPrevious", "DiagramProject", "DiagramSankey", "DiagramSubtask", "DiagramSuccessor", "DiagramVenn", "<PERSON><PERSON>", "DialHigh", "Dial<PERSON>ow", "DialMax", "DialMed", "DialMedLow", "<PERSON>al<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Diamond", "DiamondExclamation", "DiamondHalf", "DiamondHalfStroke", "DiamondTurnRight", "<PERSON><PERSON>", "DiceD4", "DiceD6", "DiceD8", "DiceD10", "DiceD12", "DiceD20", "DiceFive", "DiceFour", "DiceOne", "DiceSix", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DiceTwo", "Dinosaur", "Diploma", "DiscDrive", "Disease", "Display", "DisplayArrowDown", "DisplayChartUp", "DisplayChartUpCircleCurrency", "DisplayChartUpCircleDollar", "DisplayCode", "DisplayMedical", "DisplaySlash", "DistributeSpacingHorizontal", "DistributeSpacingVertical", "<PERSON><PERSON>", "Divide", "Dna", "Dog", "DogLeashed", "DollarSign", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Dolphin", "DongSign", "DoNotEnter", "Donut", "DoorClosed", "DoorOpen", "Dove", "Down", "DownFromDottedLine", "DownFromLine", "DownLeft", "DownLeftAndUpRightToCenter", "Download", "DownLong", "DownRight", "DownToBracket", "DownToDottedLine", "DownToLine", "Dragon", "DrawCircle", "DrawPolygon", "DrawSquare", "<PERSON><PERSON><PERSON>", "Drone", "DroneFront", "Droplet", "DropletDegree", "DropletPercent", "DropletSlash", "Drum", "DrumSteelpan", "Drumstick", "DrumstickBite", "Dryer", "DryerHeat", "<PERSON>", "<PERSON><PERSON><PERSON>", "Dumpster", "DumpsterFire", "Dungeon", "E", "Ear", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EarListen", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EarthAfrica", "EarthAmericas", "EarthAsia", "EarthEurope", "EarthOceania", "Eclipse", "Egg", "EggFried", "Eggplant", "Eject", "Elephant", "Elevator", "El<PERSON><PERSON>", "EllipsisStroke", "EllipsisStrokeVertical", "EllipsisVertical", "EmptySet", "Engine", "EngineWarning", "Envelope", "EnvelopeCircleCheck", "EnvelopeDot", "EnvelopeOpen", "EnvelopeOpenDollar", "EnvelopeOpenText", "Envelopes", "EnvelopesBulk", "Equals", "Eraser", "Escalator", "Ethernet", "EuroSign", "Excavator", "Exclamation", "Expand", "ExpandWide", "Explosion", "Eye", "EyeDropper", "EyeDropperFull", "EyeDropperHalf", "EyeEvil", "EyeLowVision", "Eyes", "EyeSlash", "F", "FaceAngry", "FaceAngryHorns", "FaceAnguished", "FaceAnxiousSweat", "FaceAstonished", "FaceAwesome", "FaceBeamHandOverMouth", "FaceClouds", "FaceConfounded", "FaceConfused", "FaceCowboyHat", "FaceDiagonalMouth", "FaceDisappointed", "FaceDisguise", "FaceDizzy", "FaceDotted", "FaceDowncastSweat", "FaceDrooling", "FaceExhaling", "FaceExplode", "FaceExpressionless", "FaceEyesXmarks", "FaceFearful", "FaceFlushed", "FaceFrown", "FaceFrownOpen", "FaceFrownSlight", "FaceGlasses", "Face<PERSON><PERSON><PERSON>", "Face<PERSON>rin", "FaceGrinBeam", "FaceGrinBeamSweat", "Face<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaceGrinSquint", "FaceGrinSquintTears", "FaceGrinStars", "FaceGrinTears", "FaceGrinTongue", "FaceGrinTongueSquint", "FaceGrinTongueWink", "FaceGrinWide", "FaceGrinWink", "FaceHandOverMouth", "FaceHandPeeking", "FaceHandYawn", "FaceHeadBandage", "FaceHoldingBackTears", "FaceHushed", "FaceIcicles", "<PERSON><PERSON><PERSON>", "FaceKissBeam", "FaceKissClosedEyes", "FaceKissWinkHeart", "<PERSON><PERSON><PERSON>", "FaceLaughBeam", "FaceLaughSquint", "FaceLaughWink", "FaceLying", "FaceMask", "<PERSON><PERSON>eh", "FaceMehBlank", "FaceMelting", "FaceMonocle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaceNoseSteam", "Face<PERSON>arty", "FacePensive", "FacePersevering", "FacePleading", "FacePouting", "FaceRaisedEyebrow", "Face<PERSON>elieved", "FaceRollingEyes", "FaceSadCry", "FaceSadSweat", "FaceSadTear", "Hotdog", "FaceScream", "<PERSON><PERSON><PERSON><PERSON>", "FaceSleeping", "FaceS<PERSON>py", "FaceSmileBeam", "FaceSmile", "FaceSmileHalo", "FaceSmileHearts", "FaceSmileHorns", "FaceSmilePlus", "FaceSmileRelaxed", "FaceSmileTear", "FaceSmileTongue", "FaceSmileUpsideDown", "FaceSmileWink", "FaceSmilingHands", "FaceSpiralEyes", "FaceSmirking", "FaceSunglasses", "FaceSurprise", "FaceSwear", "FaceThermometer", "FaceThinking", "FaceTired", "FaceTissue", "FaceTongueMoney", "FaceTongueSweat", "FaceUnamused", "FaceViewfinder", "FaceVomit", "<PERSON><PERSON><PERSON><PERSON>", "FaceWoozy", "FaceWorried", "FaceZany", "FaceZipper", "Falafel", "Family", "FamilyDress", "FamilyPants", "Fan", "FanTable", "Farm", "Faucet", "FaucetDrip", "Fax", "<PERSON><PERSON>", "FeatherPointed", "<PERSON><PERSON>", "FerrisWheel", "Ferry", "FieldHockeyStickBall", "File", "FileArrowDown", "FileArrowUp", "FileAudio", "FileBinary", "FileCertificate", "FileChartColumn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FileCheck", "FileCircleCheck", "FileCircleExclamation", "FileCircleInfo", "FileCircleMinus", "FileCirclePlus", "FileCircleQuestion", "FileCircleXmark", "FileCode", "FileContract", "FileCsv", "FileDashedLine", "FileDoc", "FileEps", "FileExclamation", "FileGif", "FileExport", "FileImport", "FileExcel", "FileHeart", "FileInvoiceDollar", "FileImage", "FileInvoice", "FileJpg", "FileLines", "FileMagnifyingGlass", "FileLock", "FileMedical", "FileMinus", "<PERSON><PERSON><PERSON>", "FileMp4", "FileMp3", "FilePdf", "FileMusic", "FilePen", "FilePlusMinus", "FilePrescription", "FilePlus", "FilePowerpoint", "FilePpt", "Files", "FileShield", "FileSignature", "FileSlash", "FilesMedical", "FileSpreadsheet", "FileSvg", "FileUser", "FileVector", "FileVideo", "FileWaveform", "FileWord", "FileXls", "FileXmark", "FileXml", "FileZip", "FileZipper", "Fill", "FillDrip", "Film", "FilmCanister", "Films", "FilmSimple", "FilmSlash", "Filter", "<PERSON>lter<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FilterCircleXmark", "FilterList", "Filters", "FilterSlash", "Fingerprint", "Fire", "FireBurner", "FireExtinguisher", "FireFlame", "FireFlameCurved", "FireFlameSimple", "FireHydrant", "Fireplace", "FireSmoke", "Fish", "FishBones", "FishCooked", "FishFins", "FishingRod", "Flag", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Flag<PERSON><PERSON><PERSON>", "FlagSwallowtail", "FlagUsa", "Flashlight", "Flask", "FlaskGear", "FlaskRoundPoison", "FlaskRoundPotion", "FlaskVial", "Flatbread", "FlatbreadStuffed", "FloppyDisk", "FloppyDiskCircleArrowRight", "FloppyDiskCircleXmark", "FloppyDiskPen", "FloppyDisks", "FlorinSign", "Flower", "FlowerDaffodil", "FlowerTulip", "Flute", "FluxCapacitor", "FlyingDisc", "Folder", "FolderArrowDown", "FolderArrowUp", "FolderBookmark", "FolderCheck", "FolderClosed", "FolderGear", "FolderGrid", "FolderHeart", "FolderImage", "FolderMagnifyingGlass", "FolderMedical", "FolderMinus", "FolderMusic", "FolderOpen", "FolderPlus", "Folders", "FolderTree", "FolderUser", "FolderXmark", "FonduePot", "Font", "FontAwesome", "FontCase", "Football", "FootballHelmet", "Fork", "ForkKnife", "Forklift", "Fort", "Forward", "ForwardFast", "ForwardStep", "<PERSON>ame", "FrancSign", "FrenchFries", "<PERSON>", "Function", "Futbol", "G", "Galaxy", "GalleryThumbnails", "GameBoard", "GameBoardSimple", "GameConsoleHandheld", "GameConsoleHandheldCrank", "Gamepad", "GamepadModern", "Garage", "GarageCar", "GarageOpen", "<PERSON><PERSON><PERSON>", "GasPump", "GasPumpSlash", "Gauge", "GaugeCircleBolt", "GaugeCircleMinus", "GaugeCirclePlus", "GaugeHigh", "GaugeLow", "GaugeMax", "GaugeMin", "GaugeSimple", "GaugeSimpleHigh", "GaugeSimpleLow", "GaugeSimpleMax", "GaugeSimpleMin", "Gavel", "Gear", "GearCode", "GearComplex", "GearComplexCode", "Gears", "Gem", "Genderless", "Ghost", "Gif", "Gift", "GiftCard", "Gifts", "GingerbreadMan", "Glass", "GlassCitrus", "GlassEmpty", "Glasses", "GlassesRound", "GlassHalf", "GlassWater", "GlassWaterDroplet", "Globe", "GlobePointer", "GlobeSnow", "GlobeStand", "GoalNet", "GolfBallTee", "GolfClub", "GolfFlagHole", "Gopuram", "GraduationCap", "Gramophone", "Grapes", "Grate", "GrateDroplet", "GreaterThan", "GreaterThanEqual", "Grid", "Grid2", "Grid2Plus", "Grid4", "Grid5", "GridDividers", "GridHorizontal", "GridRound", "GridRound2", "GridRound2Plus", "GridRound4", "GridRound5", "Grill", "GrillFire", "GrillHot", "<PERSON><PERSON>", "GripDots", "GripDotsVertical", "GripLines", "GripVertical", "GripLinesVertical", "GroupArrowsRotate", "Gun", "GuitarElectric", "Guitars", "Guitar", "GuaraniSign", "GunSlash", "GunSquirt", "H", "H1", "H2", "H3", "H4", "H5", "H6", "Hammer", "HammerBrush", "HammerCrash", "<PERSON><PERSON><PERSON>", "Ham<PERSON>", "Hand", "HandBackFist", "HandBackPointDown", "HandBackPointLeft", "HandBackPointRibbon", "HandBackPointRight", "HandBackPointUp", "Handcuffs", "HandDots", "HandFingers<PERSON>rossed", "HandFist", "HandHeart", "HandHolding", "HandHoldingBox", "HandHoldingCircleDollar", "HandHoldingDollar", "HandHoldingDroplet", "HandHoldingHand", "HandHoldingHeart", "HandHoldingMagic", "HandHoldingMedical", "HandHoldingSeedling", "HandHoldingSkull", "HandHorns", "HandLizard", "HandLove", "HandMiddle<PERSON>inger", "Hand<PERSON><PERSON><PERSON>", "HandPointDown", "HandPointer", "HandPointLeft", "HandPointRibbon", "HandPointRight", "HandPointUp", "Hands", "HandsAslInterpreting", "HandsBound", "HandsBubbles", "HandScissors", "HandsClapping", "Handshake", "Handshake<PERSON>ngle", "Handshake<PERSON><PERSON><PERSON>", "HandshakeSimpleSlash", "HandshakeSlash", "HandsHolding", "HandsHoldingChild", "HandsHoldingCircle", "HandsHoldingDiamond", "HandsHoldingDollar", "HandsHoldingHeart", "HandSparkles", "HandSpock", "HandsPraying", "HandWave", "<PERSON><PERSON><PERSON>", "HardDrive", "Hashtag", "HashtagLock", "HatBeach", "<PERSON><PERSON><PERSON><PERSON>", "HatCowboy", "HatCowboySide", "HatSanta", "HatWinter", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Heading", "Headphones", "HeadphonesSimple", "Headset", "HeadSide", "HeadSideBrain", "HeadSideCough", "HeadSideCoughSlash", "HeadSideGear", "HeadSideGoggles", "HeadSideHeadphones", "HeadSideHeart", "HeadSideMask", "HeadSideMedical", "HeadSideVirus", "Heart", "HeartCircleBolt", "HeartCircleCheck", "HeartCircleExclamation", "HeartCircleMinus", "HeartCirclePlus", "HeartCircleXmark", "HeartCrack", "HeartHalf", "HeartHalfStroke", "HeartPulse", "Heat", "Helicopter", "HelicopterSymbol", "He<PERSON>etBattle", "HelmetSafety", "HelmetUn", "Hexagon", "HexagonCheck", "HexagonDivide", "HexagonExclamation", "HexagonImage", "HexagonMinus", "HexagonPlus", "HexagonVerticalNft", "HexagonVerticalNftSlanted", "HexagonXmark", "HighDefinition", "Highlighter", "HighlighterLine", "HillAvalanche", "HillRockslide", "<PERSON><PERSON>", "HockeyMask", "HockeyPuck", "HockeyStickPuck", "HockeySticks", "<PERSON><PERSON><PERSON>", "HoneyPot", "HoodCloak", "HorizontalRule", "Horse", "HorseHead", "HorseSaddle", "Hose", "<PERSON><PERSON><PERSON><PERSON>", "Hospital", "Hospitals", "HospitalUser", "P", "Hotel", "HotTub<PERSON>erson", "Hourglass", "HourglassClock", "HourglassEnd", "HourglassHalf", "HourglassStart", "House", "HouseBlank", "HouseBuilding", "House<PERSON><PERSON><PERSON>", "HouseChimneyBlank", "HouseChimneyCrack", "HouseC<PERSON>ney<PERSON>eart", "HouseChimneyUser", "HouseChimneyWindow", "HouseChimneyMedical", "HouseCircleCheck", "HouseCircleExclamation", "HouseCircleXmark", "HouseDay", "HouseCrack", "HouseFire", "HouseFlag", "HouseFloodWater", "HouseFloodWaterCircleArrowRight", "House<PERSON>ear<PERSON>", "HouseLaptop", "HouseLock", "HouseMedical", "HouseMedicalCircleCheck", "HouseMedicalCircleExclamation", "HouseMedicalCircleXmark", "HouseMedicalFlag", "HouseNight", "HousePersonLeave", "HousePersonReturn", "HouseSignal", "HouseTree", "HouseTsunami", "<PERSON><PERSON><PERSON><PERSON>", "HouseUser", "HouseWater", "HouseWindow", "HryvniaSign", "HundredPoints", "Hurricane", "Hyphen", "I", "IceCream", "IceSkate", "Icicles", "Icons", "ICursor", "IdBadge", "IdCard", "IdCardClip", "Igloo", "Image", "ImageLandscape", "ImagePolaroid", "ImagePolaroidUser", "ImagePortrait", "Images", "ImageSlash", "ImagesUser", "ImageUser", "Inboxes", "InboxFull", "InboxIn", "Inbox", "IndianRupeeSign", "Industry", "InboxOut", "IndustryWindows", "Infinity", "Indent", "Info", "<PERSON><PERSON><PERSON>", "InputNumeric", "InputPipe", "InputText", "Integral", "Interrobang", "Intersection", "J", "JackOLantern", "<PERSON><PERSON>", "Italic", "IslandTropical", "JarWheat", "<PERSON><PERSON>", "<PERSON><PERSON>ighter", "Joystick", "<PERSON><PERSON>", "JetFighterUp", "Joint", "JugDetergent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "K", "<PERSON><PERSON>", "Ka<PERSON><PERSON>", "Kerning", "Key", "Keyboard", "KeyboardBrightness", "KeyboardBrightnessLow", "KeyboardDown", "KeyboardLeft", "Keynote", "KeySkeleton", "KeySkeletonLeftRight", "Khanda", "Kidneys", "KipSign", "KitchenSet", "<PERSON>e", "KitMedical", "KiwiBird", "KiwiFruit", "Knife", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "L", "LacrosseStick", "LacrosseStickBall", "Lambda", "<PERSON><PERSON>", "LampDesk", "LampFloor", "LampStreet", "Landmark", "LandmarkDome", "LandmarkFlag", "LandmarkMagnifyingGlass", "LandMineOn", "Language", "Laptop", "LaptopArrowDown", "LaptopBinary", "LaptopCode", "LaptopFile", "LaptopMedical", "LaptopMobile", "LaptopSlash", "LariSign", "<PERSON><PERSON>", "LassoSparkles", "LayerGroup", "LayerMinus", "LayerPlus", "Leaf", "Leaf<PERSON>ear<PERSON>", "LeafMaple", "LeafOak", "LeafyGreen", "Left", "LeftFromLine", "LeftLong", "LeftLongToLine", "LeftRight", "LeftToLine", "Lemon", "<PERSON><PERSON><PERSON>", "LessThanEqual", "LifeRing", "Lightbulb", "LightbulbCfl", "LightbulbCflOn", "LightbulbDollar", "LightbulbExclamation", "LightbulbExclamationOn", "LightbulbGear", "LightbulbOn", "LightbulbSlash", "LightCeiling", "LightEmergency", "LightEmergencyOn", "Lighthouse", "LightsHoliday", "LightSwitch", "LightSwitchOff", "LightSwitchOn", "LineColumns", "LineHeight", "LinesLeaning", "Link", "LinkHorizontal", "LinkHorizontalSlash", "LinkSimple", "LinkSimpleSlash", "LinkSlash", "Lips", "LiraSign", "List", "ListCheck", "ListDropdown", "ListMusic", "ListOl", "ListRadio", "ListTimeline", "ListTree", "ListUl", "LitecoinSign", "Loader", "Lobster", "LocationArrow", "LocationArrowUp", "LocationCheck", "LocationCrosshairs", "LocationCrosshairsSlash", "LocationDot", "LocationDotSlash", "LocationExclamation", "LocationMinus", "LocationPen", "LocationPin", "LocationPinLock", "LocationPinSlash", "LocationPlus", "LocationQuestion", "LocationSmile", "LocationXmark", "Lock", "LockA", "LockHashtag", "LockKeyhole", "LockKeyholeOpen", "LockOpen", "Locust", "Lollipop", "<PERSON><PERSON>", "LuchadorMask", "<PERSON><PERSON><PERSON>", "LungsVirus", "M", "Mace", "<PERSON><PERSON><PERSON>", "MagnifyingGlass", "MagnifyingGlassArrowRight", "MagnifyingGlassArrowsRotate", "Magnifying<PERSON><PERSON><PERSON>hart", "Magnifying<PERSON><PERSON><PERSON><PERSON><PERSON>", "MagnifyingGlassLocation", "MagnifyingGlassMinus", "MagnifyingGlassMusic", "MagnifyingGlassPlay", "MagnifyingGlassPlus", "MagnifyingGlassWaveform", "Mailbox", "MailboxFlagUp", "ManatSign", "Mandolin", "Mango", "Manhole", "Map", "MapLocation", "MapLocationDot", "MapPin", "<PERSON><PERSON>", "Mars", "MarsAndVenus", "MarsAndVenusBurst", "MarsDouble", "MarsStroke", "MarsStrokeRight", "MarsStrokeUp", "MartiniGlass", "MartiniGlassCitrus", "MartiniGlassEmpty", "Mask", "MaskFace", "MaskSnorkel", "MasksTheater", "MaskVentilator", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Maximize", "Meat", "Medal", "Megaphone", "Melon", "MelonSlice", "Memo", "MemoCircleCheck", "MemoCircleInfo", "MemoPad", "Memory", "Menorah", "Mercury", "<PERSON><PERSON>", "Message", "MessageArrowDown", "MessageArrowUp", "MessageArrowUpRight", "MessageBot", "MessageCaptions", "MessageCheck", "MessageCode", "Message<PERSON>ollar", "MessageDots", "MessageExclamation", "MessageHeart", "MessageImage", "MessageLines", "MessageMedical", "MessageMiddle", "MessageMiddleTop", "MessageMinus", "MessageMusic", "MessagePen", "MessagePlus", "MessageQuestion", "MessageQuote", "Messages", "MessagesDollar", "MessageSlash", "MessageSmile", "MessageSms", "MessagesQuestion", "MessageText", "MessageXmark", "Meteor", "<PERSON>er", "MeterBolt", "MeterDroplet", "MeterFire", "Microchip", "MicrochipAi", "Microphone", "MicrophoneLines", "MicrophoneLinesSlash", "MicrophoneSlash", "MicrophoneStand", "Microscope", "Microwave", "MillSign", "Minimize", "Minus", "Mistletoe", "<PERSON><PERSON>", "Mobile", "MobileButton", "MobileNotch", "MobileRetro", "MobileScreen", "MobileScreenButton", "MobileSignal", "MobileSignalOut", "MoneyBill", "MoneyBill1", "MoneyBill1Wave", "MoneyBills", "MoneyBillSimple", "MoneyBillSimpleWave", "MoneyBillsSimple", "MoneyBillTransfer", "MoneyBillTrendUp", "MoneyBillWave", "MoneyBillWheat", "MoneyCheck", "MoneyCheckDollar", "MoneyCheckDollarPen", "MoneyCheckPen", "MoneyFromBracket", "MoneySimpleFromBracket", "MonitorWaveform", "Monkey", "Monument", "Moon", "MoonCloud", "MoonOverSun", "MoonStars", "Mo<PERSON>", "MortarPestle", "Mosque", "Mosquito", "MosquitoNet", "Motorcycle", "Mound", "Mountain", "MountainCity", "Mountains", "MountainSun", "Mouse<PERSON><PERSON>", "Mp3Player", "<PERSON>g", "MugHot", "MugMarshmallows", "<PERSON>g<PERSON><PERSON><PERSON>", "MugTea", "Mug<PERSON>ea<PERSON><PERSON><PERSON>", "Mushroom", "Music", "MusicMagnifyingGlass", "MusicNote", "MusicNoteSlash", "MusicSlash", "Mustache", "N", "NairaSign", "<PERSON><PERSON><PERSON><PERSON>", "NestingDolls", "NetworkWired", "Neuter", "Newspaper", "Nfc", "NfcLock", "NfcMagnifyingGlass", "NfcPen", "NfcSignal", "NfcSlash", "NfcSymbol", "NfcTrash", "Nose", "Notdef", "Note", "Notebook", "NoteMedical", "NotEqual", "Notes", "NotesMedical", "NoteSticky", "O", "ObjectExclude", "ObjectGroup", "ObjectIntersect", "ObjectsAlignBottom", "ObjectsAlignCenterHorizontal", "ObjectsAlignCenterVertical", "ObjectsAlignLeft", "ObjectsAlignRight", "ObjectsAlignTop", "ObjectsColumn", "ObjectSubtract", "ObjectUngroup", "ObjectUnion", "Octagon", "OctagonCheck", "OctagonDivide", "OctagonExclamation", "OctagonMinus", "OctagonPlus", "OctagonXmark", "OilCan", "OilCanDrip", "OilTemperature", "OilWell", "<PERSON>", "OliveBranch", "Om", "Omega", "Onion", "Option", "Ornament", "Otter", "Outdent", "Outlet", "Oven", "Overline", "SignalSlash", "PageCaretDown", "Page", "PageCaretUp", "Pager", "Paintbrush", "PaintbrushFine", "PaintbrushPencil", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Palette", "PalletBox", "PalletBoxes", "Pancakes", "PanelEws", "PanelFire", "PanFood", "PanFrying", "Panorama", "Paperclip", "PaperclipVertical", "PaperPlane", "PaperPlaneTop", "ParachuteBox", "Paragraph", "PartyBell", "ParagraphLeft", "PartyHorn", "Passport", "Paste", "Pause", "<PERSON>w", "PawClaws", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Peace", "Peach", "Peanut", "Peanuts", "Peapod", "Pear", "Pedestal", "Pegasus", "Pen", "Pencil", "PencilMechanical", "PencilSlash", "PenCircle", "Pen<PERSON>lip", "PenClipSlash", "PenFancy", "PenFancySlash", "PenField", "PenNib", "PenLine", "PenNibSlash", "PenPaintbrush", "PenRuler", "PenSlash", "PenSwirl", "PenToSquare", "People", "PeopleArrows", "PeopleCarryBox", "PeopleDress", "PeopleDressSimple", "PeopleGroup", "PeopleLine", "PeoplePants", "PeoplePantsSimple", "PeoplePulling", "PeopleRobbery", "PeopleRoof", "PeopleSimple", "Pepper", "PepperHot", "Person", "Percent", "Period", "PersonArrowDownToLine", "PersonBiking", "PersonArrowUpFromLine", "PersonBikingMountain", "<PERSON><PERSON><PERSON><PERSON>", "PersonBreastfeeding", "<PERSON><PERSON><PERSON><PERSON>", "PersonCane", "PersonCarryBox", "PersonChalkboard", "PersonCircle<PERSON>heck", "PersonCircleExclamation", "PersonCircleMinus", "PersonCirclePlus", "PersonCircleQuestion", "PersonCircleXmark", "PersonDigging", "Person<PERSON><PERSON><PERSON><PERSON><PERSON>", "PersonDotsFromLine", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PersonDress", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PersonDressSimple", "PersonDrowning", "<PERSON><PERSON><PERSON><PERSON>", "PersonFalling", "Person<PERSON><PERSON>ing<PERSON><PERSON><PERSON>", "PersonFromPortal", "PersonHalfDress", "PersonHarassing", "PersonHiking", "PersonMilitaryPointing", "PersonMilitaryRifle", "PersonMilitaryTo<PERSON>erson", "PersonPinball", "PersonPraying", "PersonPregnant", "PersonRays", "PersonRifle", "PersonRunning", "PersonRunningFast", "PersonSeat", "PersonSeatReclined", "PersonShelter", "PersonSign", "PersonSimple", "PersonSkating", "PersonSkiing", "PersonSkiing<PERSON>ordic", "PersonSkiJumping", "PersonSkiLift", "PersonSledding", "PersonSnowboarding", "PersonSnowmobiling", "PersonSwimming", "PersonThroughWindow", "PersonToDoor", "PersonToPortal", "PersonWalking", "PersonWalkingArrowLoopLeft", "PersonWalkingArrowRight", "PersonWalkingDashedLineArrowRight", "PersonWalkingLuggage", "PersonWalkingWithCane", "PesetaSign", "PesoSign", "Phone", "PhoneArrowDownLeft", "PhoneArrowRight", "PhoneArrowUpRight", "PhoneFlip", "PhoneHangup", "PhoneIntercom", "PhoneMissed", "PhoneOffice", "PhonePlus", "PhoneRotary", "PhoneSlash", "PhoneVolume", "PhoneXmark", "PhotoFilm", "PhotoFilmMusic", "Pi", "Piano", "PiggyBank", "Pig", "PianoKeyboard", "Pickaxe", "<PERSON><PERSON><PERSON>", "Pinball", "Pie", "Pills", "Pickleball", "Pineapple", "<PERSON><PERSON>", "PipeCircleCheck", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PipeSection", "PipeSmoking", "PipeValve", "Pizza", "PizzaSlice", "PlaceOfWorship", "Plane", "PlaneArrival", "PlaneCircleCheck", "PlaneCircleExclamation", "PlaneCircleXmark", "PlaneDeparture", "PlaneEngines", "PlaneLock", "PlaneProp", "PlaneSlash", "PlaneTail", "PlanetMoon", "PlanetRinged", "PlaneUp", "PlaneUpSlash", "PlantWilt", "PlateUtensils", "PlateWheat", "Play", "PlayPause", "Plug", "PlugCircleBolt", "PlugCircleCheck", "PlugCircleExclamation", "PlugCircleMinus", "PlugCirclePlus", "PlugCircleXmark", "Plus", "PlusLarge", "PlusMinus", "Podcast", "Podium", "PodiumStar", "PoliceBox", "PollPeople", "Pompebled", "Poo", "Pool8Ball", "<PERSON><PERSON>", "PooStorm", "Popcorn", "Popsicle", "Potato", "PotFood", "PowerOff", "Prescription", "PrescriptionBottle", "PrescriptionBottleMedical", "PrescriptionBottlePill", "PresentationScreen", "<PERSON>tzel", "Print", "PrintMagnifyingGlass", "PrintSlash", "Projector", "Pump", "<PERSON><PERSON><PERSON>", "PumpMedical", "PumpSoap", "Puzzle", "PuzzleP<PERSON>ce", "PuzzlePieceSimple", "Q", "Qrcode", "Question", "QuoteLeft", "QuoteRight", "Quotes", "R", "Rabbit", "<PERSON><PERSON>unning", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Radar", "Radiation", "Radio", "RadioTuner", "Rainbow", "Raindrops", "Ram", "RampLoading", "RankingStar", "<PERSON><PERSON>", "Receipt", "RecordVinyl", "Rectangle", "RectangleAd", "RectangleBarcode", "RectangleCode", "RectangleHistory", "RectangleHistoryCirclePlus", "RectangleHistoryCircleUser", "RectangleList", "RectanglePro", "RectanglesMixed", "RectangleTerminal", "RectangleVertical", "RectangleVerticalHistory", "RectangleWide", "RectangleXmark", "Recycle", "<PERSON><PERSON>", "ReflectHorizontal", "ReflectVertical", "Refrigerator", "Registered", "Repeat", "Repeat1", "Reply", "ReplyAll", "<PERSON><PERSON><PERSON><PERSON>", "Republican", "Restroom", "RestroomSimple", "Retweet", "Rhombus", "Ribbon", "Right", "RightFromBracket", "RightFromLine", "RightLeft", "RightLeftLarge", "RightLong", "RightLongToLine", "RightToBracket", "RightToLine", "Ring", "<PERSON><PERSON><PERSON><PERSON>", "RingsWedding", "Road", "RoadBarrier", "RoadBridge", "RoadCircleCheck", "RoadCircleExclamation", "RoadCircleXmark", "RoadLock", "RoadSpikes", "Robot", "RobotAstromech", "Rocket", "RocketLaunch", "RollerCoaster", "Rotate", "RotateExclamation", "RotateLeft", "RotateReverse", "RotateRight", "Route", "RouteHighway", "RouteInterstate", "Router", "Rss", "RubleSign", "Rug", "RugbyBall", "Ruler", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RulerHorizontal", "Ruler<PERSON><PERSON><PERSON>", "RulerVertical", "RupeeSign", "RupiahSign", "Rv", "S", "Sack", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SackXmark", "Sailboat", "Salad", "<PERSON><PERSON><PERSON><PERSON>", "Sandwich", "Satellite", "SatelliteDish", "Sausage", "Saxophone", "SaxophoneFire", "ScaleBalanced", "ScaleUnbalanced", "ScaleUnbalancedFlip", "<PERSON><PERSON><PERSON>", "ScalpelLineDashed", "ScannerGun", "ScannerImage", "ScannerKeyboard", "ScannerTouchscreen", "Scarecrow", "<PERSON><PERSON><PERSON>", "School", "SchoolCircleCheck", "SchoolCircleExclamation", "SchoolCircleXmark", "SchoolFlag", "SchoolLock", "Scissors", "Screencast", "ScreenUsers", "<PERSON><PERSON><PERSON><PERSON>", "ScrewdriverWrench", "Scribble", "<PERSON><PERSON>", "ScrollOld", "<PERSON><PERSON>Tora<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "SdCard", "SdCards", "Seal", "SealExclamation", "SealQuestion", "SeatAirline", "Section", "Seedling", "Semicolon", "SendBack", "SendBackward", "Sensor", "SensorCloud", "SensorFire", "SensorOn", "SensorTriangleExclamation", "Server", "<PERSON><PERSON><PERSON>", "Share", "ShareAll", "ShareFromSquare", "ShareNodes", "Sheep", "SheetPlastic", "ShekelSign", "<PERSON><PERSON>", "<PERSON><PERSON>E<PERSON><PERSON>", "Shield", "ShieldCat", "ShieldCheck", "ShieldCross", "ShieldDog", "ShieldExclamation", "ShieldHalved", "ShieldHeart", "ShieldKeyhole", "ShieldMinus", "ShieldPlus", "ShieldQuartered", "ShieldSlash", "ShieldVirus", "ShieldXmark", "Ship", "Shirt", "ShirtLongSleeve", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ShirtTankTop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ShoePrints", "Shop", "ShopLock", "ShopSlash", "<PERSON><PERSON><PERSON>", "ShovelSnow", "Shower", "ShowerDown", "Shredder", "<PERSON>mp", "Shuffle", "Shutters", "<PERSON><PERSON>", "ShuttleSpace", "<PERSON><PERSON>", "Sidebar", "SidebarFlip", "Sigma", "Signal", "SignalBars", "SignalBarsFair", "SignalBarsGood", "SignalBarsSlash", "SignalBarsWeak", "SignalFair", "SignalGood", "UserHairBuns", "SignalStream", "SignalStreamSlash", "SignalStrong", "SignalWeak", "Signature", "SignatureLock", "SignatureSlash", "SignHanging", "SignPost", "SignPosts", "SignPostsWrench", "SignsPost", "SimCard", "SimCards", "Sink", "<PERSON><PERSON>", "SirenOn", "Sitemap", "Skeleton", "SkeletonRibs", "SkiBoot", "SkiBootSki", "Skull", "SkullCow", "SkullCrossbones", "Slash", "SlashBack", "SlashForward", "Sleigh", "Slide<PERSON>", "Sliders", "SlidersSimple", "SlidersUp", "SlotMachine", "Smog", "Smoke", "Smoking", "Snake", "Snooze", "SnowBlowing", "Snowflake", "SnowflakeDroplets", "Snowflakes", "Snowman", "SnowmanHead", "Snowplow", "Soap", "Socks", "SoftServe", "SolarPanel", "SolarSystem", "Sort", "SortDown", "SortUp", "Spa", "SpaceStationMoon", "SpaceStationMoonConstruction", "Spade", "SpaghettiMonsterFlying", "<PERSON><PERSON><PERSON>", "Speaker", "Speakers", "SpiderBlackWidow", "SpiderWeb", "Sparkle", "Spinner", "Spider", "SpinnerScale", "SpinnerThird", "Splotch", "Split", "SprayCan", "SprayCanSparkles", "Sportsball", "Sprinkler", "SprinklerCeiling", "Square", "Square3", "Square0", "Square1", "SpellCheck", "Square4", "Square5", "<PERSON>poon", "Square2", "Square7", "Square8", "Square6", "Square9", "SquareALock", "SquareA", "SquareArrowDown", "SquareAmpersand", "SquareArrowDownRight", "SquareArrowDownLeft", "SquareArrowLeft", "SquareArrowRight", "SquareArrowUp", "SquareArrowUpLeft", "SquareArrowUpRight", "SquareB", "SquareBolt", "SquareC", "SquareCaretDown", "SquareCaretLeft", "SquareCaretRight", "SquareCaretUp", "SquareCheck", "SquareChevronDown", "SquareChevronLeft", "SquareChevronRight", "SquareChevronUp", "SquareCode", "SquareD", "SquareDashed", "SquareDashedCirclePlus", "SquareDivide", "SquareDollar", "SquareDown", "SquareDownLeft", "SquareDownRight", "SquareE", "SquareEllipsis", "SquareEllipsisVertical", "SquareEnvelope", "SquareExclamation", "SquareF", "SquareFragile", "SquareFull", "SquareG", "SquareH", "SquareHeart", "SquareI", "SquareInfo", "SquareJ", "SquareK", "SquareKanban", "SquareL", "SquareLeft", "SquareList", "SquareM", "SquareMinus", "SquareN", "SquareNfi", "SquareO", "SquareP", "SquareParking", "SquareParkingSlash", "SquarePen", "SquarePersonConfined", "SquarePhone", "SquarePhoneFlip", "SquarePhoneHangup", "SquarePlus", "SquarePollHorizontal", "SquarePollVertical", "SquareQ", "SquareQuarters", "SquareQuestion", "SquareQuote", "SquareR", "SquareRight", "SquareRing", "SquareRoot", "SquareRootVariable", "SquareRss", "SquareS", "SquareShareNodes", "SquareSliders", "SquareSlidersVertical", "SquareSmall", "SquareStar", "SquareT", "SquareTerminal", "SquareThisWayUp", "SquareU", "SquareUp", "SquareUpLeft", "SquareUpRight", "SquareUser", "SquareV", "SquareVirus", "SquareW", "SquareX", "SquareXmark", "SquareY", "SquareZ", "Squid", "Squirrel", "Staff", "StaffSnake", "Stairs", "Stamp", "StandardDefinition", "<PERSON><PERSON><PERSON>", "Star", "StarAndCrescent", "StarChristmas", "StarExclamation", "Starfighter", "StarfighterTwinIonEngine", "StarfighterTwinIonEngineAdvanced", "StarHalf", "StarHalfStroke", "StarOfDavid", "StarOfLife", "Stars", "StarSharp", "StarSharpHalf", "StarSharpHalfStroke", "Starship", "Starship<PERSON><PERSON>ighter", "StarShooting", "Steak", "SteeringWheel", "SterlingSign", "Stethoscope", "Stocking", "<PERSON><PERSON><PERSON>", "Stop", "Stopwatch", "Stopwatch20", "Store", "StoreLock", "StoreSlash", "<PERSON><PERSON>berry", "StreetView", "<PERSON><PERSON><PERSON>", "Strikethrough", "Stroopwafel", "Subscript", "Subtitles", "SubtitlesSlash", "Suitcase", "SuitcaseMedical", "SuitcaseRolling", "Sun", "SunBright", "SunCloud", "SunDust", "Sunglasses", "SunHaze", "SunPlantWilt", "Sunrise", "Sunset", "Superscript", "<PERSON><PERSON>", "SushiRoll", "<PERSON><PERSON><PERSON>", "SwapArrows", "Swatchbook", "Sword", "SwordLaser", "SwordLaserAlt", "Swords", "SwordsLaser", "Symbols", "Synagogue", "Syringe", "T", "Table", "TableCells", "TableCellsLarge", "TableColumns", "TableLayout", "TableList", "TablePicnic", "TablePivot", "TableRows", "Tablet", "TabletButton", "TableTennisPaddleBall", "TableTree", "TabletRugged", "Tablets", "TabletScreen", "TabletScreenButton", "TachographDigital", "Taco", "Tag", "Tags", "<PERSON><PERSON>", "Tally1", "Tally2", "Tally3", "Tally4", "Tamale", "TankWater", "Ta<PERSON>", "Tarp", "TarpDroplet", "Taxi", "TaxiBus", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "TeethOpen", "Telescope", "TemperatureArrowDown", "TemperatureArrowUp", "TemperatureEmpty", "TemperatureFull", "TemperatureHalf", "TemperatureHigh", "TemperatureList", "TemperatureLow", "TemperatureQuarter", "TemperatureSnow", "TemperatureSun", "TemperatureThreeQuarters", "TengeSign", "TennisBall", "Tent", "TentArrowDownToLine", "TentArrowLeftRight", "TentArrowsDown", "TentArrowTurnLeft", "TentDoublePeak", "Tents", "Terminal", "Text", "TextHeight", "TextSize", "TextSlash", "TextWidth", "Thermometer", "Theta", "ThoughtBubble", "ThumbsDown", "ThumbsUp", "Thumbtack", "Tick", "Ticket", "TicketAirline", "TicketPerforated", "Tickets", "TicketsAirline", "TicketSimple", "TicketsPerforated", "TicketsSimple", "<PERSON><PERSON>", "Timeline", "TimelineArrow", "Timer", "Tire", "<PERSON><PERSON><PERSON><PERSON>", "TirePressureWarning", "TireRugged", "ToggleLar<PERSON><PERSON>ff", "ToggleLargeOn", "<PERSON><PERSON><PERSON><PERSON>", "ToggleOn", "<PERSON><PERSON><PERSON>", "ToiletPaper", "ToiletPaperBlank", "ToiletPaperBlankUnder", "ToiletPaperCheck", "ToiletPaperSlash", "ToiletPaperUnder", "ToiletPaperUnderSlash", "ToiletPaperXmark", "ToiletPortable", "ToiletsPortable", "Tomato", "Tombstone", "TombstoneBlank", "Toolbox", "Tooth", "Toothbrush", "ToriiGate", "Tornado", "TowerBroadcast", "TowerCell", "TowerControl", "TowerObservation", "Tractor", "Trademark", "TrafficCone", "TrafficLight", "TrafficLightGo", "TrafficLightSlow", "TrafficLightStop", "Trailer", "Train", "TrainSubway", "TrainSubwayTunnel", "TrainTrack", "TrainTram", "TrainTunnel", "TransformerBolt", "Transgender", "Transporter", "Transporter1", "Transporter2", "Transporter3", "Transporter4", "Transporter5", "Transporter6", "Transporter7", "TransporterEmpty", "Trash", "TrashArrowUp", "TrashCan", "TrashCanArrowUp", "TrashCanCheck", "TrashCanClock", "TrashCanList", "TrashCanPlus", "TrashCanSlash", "TrashCanUndo", "TrashCanXmark", "TrashCheck", "TrashClock", "TrashList", "TrashPlus", "TrashSlash", "TrashUndo", "TrashXmark", "TreasureChest", "Tree", "TreeChristmas", "TreeCity", "TreeDeciduous", "TreeDecorated", "Tree<PERSON>arge", "TreePalm", "Trees", "TRex", "Triangle", "TriangleExclamation", "TriangleInstrument", "TrianglePersonDigging", "Tricycle", "TricycleAdult", "Trillium", "Trophy", "TrophyStar", "Trowel", "T<PERSON>elBricks", "Truck", "TruckArrowRight", "TruckBolt", "TruckClock", "TruckContainer", "TruckContainerEmpty", "TruckDroplet", "TruckFast", "TruckField", "TruckFieldUn", "TruckFire", "TruckFlatbed", "TruckFront", "TruckLadder", "TruckMedical", "TruckMonster", "TruckMoving", "TruckPickup", "TruckPlane", "TruckPlow", "TruckRamp", "TruckRampBox", "TruckRampCouch", "TruckTow", "TruckUtensils", "Trumpet", "<PERSON><PERSON>", "TtyAnswer", "TugrikSign", "Turkey", "TurkishLiraSign", "TurnDown", "TurnDownLeft", "TurnDownRight", "TurnLeft", "TurnLeftDown", "TurnLeftUp", "TurnRight", "Turntable", "TurnUp", "Turtle", "Tv", "TvMusic", "TvRetro", "Typewriter", "U", "Ufo", "UfoBeam", "Um<PERSON>lla", "UmbrellaBeach", "UmbrellaSimple", "Underline", "Unicorn", "UniformMartialArts", "Union", "UniversalAccess", "Unlock", "UnlockKeyhole", "Up", "UpDown", "UpDownLeftRight", "UpFromBracket", "UpFromDottedLine", "UpFromLine", "UpLeft", "Upload", "UpLong", "UpRight", "UpRightAndDownLeftFromCenter", "UpRightFromSquare", "UpToDottedLine", "UpToLine", "UsbDrive", "User", "UserAlien", "UserAstronaut", "UserBountyHunter", "UserCheck", "UserChef", "UserClock", "UserCowboy", "UserCrown", "UserDoctor", "UserDoctorHair", "UserDoctorHairLong", "UserDoctorMessage", "UserGear", "UserGraduate", "UserGroup", "UserGroupCrown", "UserGroupSimple", "UserHair", "XmarksLines", "XmarkToSlot", "XRay", "Y", "YenSign", "<PERSON><PERSON><PERSON>", "Z", "UserHairLong", "UserHairMullet", "UserHeadset", "UserHelmetSafety", "UserInjured", "UserLarge", "UserLargeSlash", "UserLock", "UserMagnifyingGlass", "UserMinus", "UserMusic", "User<PERSON><PERSON>ja", "UserNurse", "UserNurseHair", "UserNurseHairLong", "UserPen", "UserPilot", "UserPilotTie", "UserPlus", "UserPolice", "UserPoliceTie", "UserRobot", "UserRobotXmarks", "Users", "UsersBetweenLines", "UserSecret", "UsersGear", "User<PERSON><PERSON><PERSON><PERSON><PERSON>", "UserShield", "UserSlash", "UsersLine", "UsersMedical", "UsersRectangle", "UsersSlash", "UsersViewfinder", "UserTag", "User<PERSON>ie", "UserUnlock", "UserTieHairLong", "UserVisor", "UserVneck", "UserTieHair", "UserVneckHair", "UserVneckHairLong", "UsersRays", "UserXmark", "Utensils", "UtensilsSlash", "UtilityPole", "UtilityPoleDouble", "V", "Vacuum", "VacuumRobot", "ValueAbsolute", "<PERSON><PERSON><PERSON><PERSON>", "VectorCircle", "<PERSON><PERSON>", "VectorPolygon", "VectorSquare", "Venus", "VenusDouble", "VenusMars", "VestPatches", "Vial", "VialCircleCheck", "Vials", "Vest", "Video", "VideoArrowDownLeft", "VideoSlash", "VideoPlus", "Violin", "VirusCovidSlash", "Viruses", "VirusSlash", "Virus", "<PERSON><PERSON><PERSON>", "VirusCovid", "Volcano", "VentDamper", "VolumeSlash", "VolumeXmark", "VrCardboard", "Volume", "VolumeLow", "Volleyball", "VolumeOff", "Waffle", "VialVirus", "VolumeHigh", "Voicemail", "W", "VideoArrowUpRight", "WagonCovered", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Wallet", "<PERSON>d", "WandMagic", "WandMagicSparkles", "WandSparkles", "Warehouse", "WarehouseFull", "WashingMachine", "Watch", "WatchApple", "WatchCalculator", "WatchFitness", "WatchSmart", "Water", "WaterArrowDown", "WaterArrowUp", "WaterLadder", "WatermelonSlice", "Wave", "Waveform", "WaveformLines", "WavePulse", "WaveSine", "WaveSquare", "WavesSine", "WaveTriangle", "Webhook", "WeightHanging", "WeightScale", "Whale", "Wheat", "WheatAwn", "WheatAwnCircleExclamation", "WheatAwnSlash", "WheatSlash", "Wheelchair", "WheelchairMove", "WhiskeyGlass", "WhiskeyGlassIce", "Whistle", "Wifi", "WifiExclamation", "WifiFair", "WifiSlash", "WifiWeak", "Wind", "Window", "WindowFlip", "WindowFrame", "WindowFrameOpen", "WindowMaximize", "WindowMinimize", "WindowRestore", "Windsock", "WindTurbine", "WindWarning", "WineBottle", "WineGlass", "WineGlassCrack", "WineGlassEmpty", "WonSign", "<PERSON><PERSON>", "Wreath", "WreathLaurel", "<PERSON><PERSON>", "WrenchSimple", "X", "Xmark", "XmarkLarge"]}], "lineNumber": 256, "namespace": true, "qualifiedClassName": "ElaIconType"}], "inputFile": "Def.h", "outputRevision": 69}