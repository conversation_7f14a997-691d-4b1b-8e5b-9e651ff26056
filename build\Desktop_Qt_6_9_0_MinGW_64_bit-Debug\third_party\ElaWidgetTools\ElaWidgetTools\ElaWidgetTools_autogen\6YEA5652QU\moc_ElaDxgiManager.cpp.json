{"classes": [{"className": "ElaDxgiManager", "lineNumber": 10, "object": true, "qualifiedClassName": "ElaDxgiManager", "signals": [{"access": "public", "arguments": [{"name": "img", "type": "QImage"}], "index": 0, "name": "grabImageUpdate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "ElaDxgiScreen", "lineNumber": 43, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pBorderRadius", "notify": "pBorderRadiusChanged", "read": "getBorderRadius", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBorderRadius"}], "qualifiedClassName": "ElaDxgiScreen", "signals": [{"access": "public", "index": 0, "name": "pBorderRadiusChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "ElaDxgiManager.h", "outputRevision": 69}