{"classes": [{"className": "ElaPromotionCard", "lineNumber": 7, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pBorderRadius", "notify": "pBorderRadiusChanged", "read": "getBorderRadius", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBorderRadius"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pCardPixmap", "notify": "pCardPixmapChanged", "read": "getCardPixmap", "required": false, "scriptable": true, "stored": true, "type": "QPixmap", "user": false, "write": "setCardPixmap"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "pCardTitle", "notify": "pCardTitleChanged", "read": "getCardTitle", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setCardTitle"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "pPromotionTitle", "notify": "pPromotionTitleChanged", "read": "getPromotionTitle", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setPromotionTitle"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "p<PERSON>itle", "notify": "pTitleChanged", "read": "getTitle", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTitle"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "pSubTitle", "notify": "pSubTitleChanged", "read": "getSubTitle", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setSubTitle"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "pCardTitleColor", "notify": "pCardTitleColorChanged", "read": "getCardTitleColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setCardTitleColor"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "pPromotionTitleColor", "notify": "pPromotionTitleColorChanged", "read": "getPromotionTitleColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setPromotionTitleColor"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "pPromotionTitleBaseColor", "notify": "pPromotionTitleBaseColorChanged", "read": "getPromotionTitleBaseColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setPromotionTitleBaseColor"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "pTitleColor", "notify": "pTitleColorChanged", "read": "getTitleColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setTitleColor"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "pSubTitleColor", "notify": "pSubTitleColorChanged", "read": "getSubTitleColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSubTitleColor"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "pCardTitlePixelSize", "notify": "pCardTitlePixelSizeChanged", "read": "getCardTitlePixelSize", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setCardTitlePixelSize"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "pPromotionTitlePixelSize", "notify": "pPromotionTitlePixelSizeChanged", "read": "getPromotionTitlePixelSize", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setPromotionTitlePixelSize"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "pTitlePixelSize", "notify": "pTitlePixelSizeChanged", "read": "getTitlePixelSize", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTitlePixelSize"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "pSubTitlePixelSize", "notify": "pSubTitlePixelSizeChanged", "read": "getSubTitlePixelSize", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSubTitlePixelSize"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "pHorizontalCardPixmapRatio", "notify": "pHorizontalCardPixmapRatioChanged", "read": "getHorizontalCardPixmapRatio", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setHorizontalCardPixmapRatio"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "pVerticalCardPixmapRatio", "notify": "pVerticalCardPixmapRatioChanged", "read": "getVerticalCardPixmapRatio", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setVerticalCardPixmapRatio"}], "qualifiedClassName": "ElaPromotionCard", "signals": [{"access": "public", "index": 0, "name": "pBorderRadiusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pCardPixmapChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "pCardTitleChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "pPromotionTitleChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "pTitleChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "pSubTitleChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "pCardTitleColorChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "pPromotionTitleColorChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "pPromotionTitleBaseColorChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "pTitleColorChanged", "returnType": "void"}, {"access": "public", "index": 10, "name": "pSubTitleColorChanged", "returnType": "void"}, {"access": "public", "index": 11, "name": "pCardTitlePixelSizeChanged", "returnType": "void"}, {"access": "public", "index": 12, "name": "pPromotionTitlePixelSizeChanged", "returnType": "void"}, {"access": "public", "index": 13, "name": "pTitlePixelSizeChanged", "returnType": "void"}, {"access": "public", "index": 14, "name": "pSubTitlePixelSizeChanged", "returnType": "void"}, {"access": "public", "index": 15, "name": "pHorizontalCardPixmapRatioChanged", "returnType": "void"}, {"access": "public", "index": 16, "name": "pVerticalCardPixmapRatioChanged", "returnType": "void"}, {"access": "public", "index": 17, "name": "promotionCardClicked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "ElaPromotionCard.h", "outputRevision": 69}