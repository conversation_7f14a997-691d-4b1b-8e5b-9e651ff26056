{"classes": [{"className": "ElaGraphicsItem", "lineNumber": 12, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pWidth", "notify": "pW<PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "getWidth", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pHeight", "notify": "pHeightChanged", "read": "getHeight", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setHeight"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "pItemImage", "notify": "pItemImageChanged", "read": "getItemImage", "required": false, "scriptable": true, "stored": true, "type": "QImage", "user": false, "write": "setItemImage"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "pItemSelectedImage", "notify": "pItemSelectedImageChanged", "read": "getItemSelectedImage", "required": false, "scriptable": true, "stored": true, "type": "QImage", "user": false, "write": "setItemSelectedImage"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "pItemName", "notify": "pItemNameChanged", "read": "getItemName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setItemName"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "pDataRoutes", "notify": "pDataRoutesChanged", "read": "getDataRoutes", "required": false, "scriptable": true, "stored": true, "type": "QVariantMap", "user": false, "write": "setDataRoutes"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "pMaxLinkPortCount", "notify": "pMaxLinkPortCountChanged", "read": "getMaxLinkPortCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMaxLinkPortCount"}], "qualifiedClassName": "ElaGraphicsItem", "signals": [{"access": "public", "index": 0, "name": "pW<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 1, "name": "pHeightChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "pItemImageChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "pItemSelectedImageChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "pItemNameChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "pDataRoutesChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "pMaxLinkPortCountChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QGraphicsObject"}]}], "inputFile": "ElaGraphicsItem.h", "outputRevision": 69}