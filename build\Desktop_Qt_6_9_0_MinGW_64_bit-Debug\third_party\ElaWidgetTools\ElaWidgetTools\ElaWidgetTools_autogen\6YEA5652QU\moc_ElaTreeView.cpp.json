{"classes": [{"className": "ElaTreeView", "lineNumber": 9, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pItemHeight", "notify": "pItemHeightChanged", "read": "getItemHeight", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setItemHeight"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "<PERSON><PERSON>erMargin", "notify": "pHeaderMarginChanged", "read": "getHeaderMargin", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "set<PERSON><PERSON>erMargin"}], "qualifiedClassName": "ElaTreeView", "signals": [{"access": "public", "index": 0, "name": "pItemHeightChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pHeaderMarginChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QTreeView"}]}], "inputFile": "ElaTreeView.h", "outputRevision": 69}