#include "SettingsDialog.h"
#include <QStandardPaths>
#include <QDir>
#include <QDebug>

SettingsDialog::SettingsDialog(ConfigManager* configManager, QWidget *parent)
    :
#ifdef ELAWIDGETTOOLS_AVAILABLE
      ElaDialog(parent)
#else
      QDialog(parent)
#endif
    , m_config<PERSON>anager(configManager)
    , m_tabWidget(nullptr)
    , m_settingsChanged(false)
{
    setWindowTitle("设置");
    setModal(true);
    resize(600, 500);

    initializeUI();
    loadSettings();
}

SettingsDialog::~SettingsDialog()
{
}

void SettingsDialog::accept()
{
    try {
        validateSettings();
        saveSettings();

#ifdef ELAWIDGETTOOLS_AVAILABLE
        ElaDialog::accept();
#else
        QDialog::accept();
#endif
    } catch (const std::exception& e) {
        QMessageBox::warning(this, "设置错误", QString("保存设置时出错：%1").arg(e.what()));
    }
}

void SettingsDialog::reject()
{
    if (m_settingsChanged) {
        int ret = QMessageBox::question(this, "确认",
            "设置已修改但未保存，确定要放弃更改吗？",
            QMessageBox::Yes | QMessageBox::No,
            QMessageBox::No);

        if (ret == QMessageBox::No) {
            return;
        }
    }

#ifdef ELAWIDGETTOOLS_AVAILABLE
    ElaDialog::reject();
#else
    QDialog::reject();
#endif
}

void SettingsDialog::initializeUI()
{
    QVBoxLayout* mainLayout = new QVBoxLayout(this);

    // 创建选项卡组件
#ifdef ELAWIDGETTOOLS_AVAILABLE
    m_tabWidget = new ElaTabWidget();
#else
    m_tabWidget = new QTabWidget();
#endif

    // 创建各个选项卡
    createGeneralTab();
    createApiTab();
    createCacheTab();
    createNetworkTab();
    createAdvancedTab();

    mainLayout->addWidget(m_tabWidget);

    // 创建按钮区域
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    buttonLayout->addStretch();

#ifdef ELAWIDGETTOOLS_AVAILABLE
    m_okButton = new ElaPushButton("确定");
    m_cancelButton = new ElaPushButton("取消");
    m_applyButton = new ElaPushButton("应用");
#else
    m_okButton = new QPushButton("确定");
    m_cancelButton = new QPushButton("取消");
    m_applyButton = new QPushButton("应用");
#endif

    buttonLayout->addWidget(m_okButton);
    buttonLayout->addWidget(m_cancelButton);
    buttonLayout->addWidget(m_applyButton);

    mainLayout->addLayout(buttonLayout);

    // 连接信号
    connect(m_okButton, &QPushButton::clicked, this, &SettingsDialog::accept);
    connect(m_cancelButton, &QPushButton::clicked, this, &SettingsDialog::reject);
    connect(m_applyButton, &QPushButton::clicked, this, [this]() {
        try {
            validateSettings();
            saveSettings();
            m_settingsChanged = false;
        } catch (const std::exception& e) {
            QMessageBox::warning(this, "设置错误", QString("应用设置时出错：%1").arg(e.what()));
        }
    });
}

void SettingsDialog::createGeneralTab()
{
    QWidget* generalTab = new QWidget();
    QFormLayout* layout = new QFormLayout(generalTab);

    // 主题设置
    m_themeComboBox = new QComboBox();
    m_themeComboBox->addItems({"Light", "Dark", "Auto"});
    layout->addRow("主题:", m_themeComboBox);

    // 语言设置
    m_languageComboBox = new QComboBox();
    m_languageComboBox->addItems({"简体中文", "English"});
    layout->addRow("语言:", m_languageComboBox);

    // 自动检查更新
    m_autoCheckUpdatesCheckBox = new QCheckBox("启动时检查更新");
    layout->addRow(m_autoCheckUpdatesCheckBox);

    // 调试模式
    m_debugModeCheckBox = new QCheckBox("启用调试模式");
    layout->addRow(m_debugModeCheckBox);

    m_tabWidget->addTab(generalTab, "通用");

    // 连接信号
    connect(m_themeComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, [this]() { m_settingsChanged = true; });
    connect(m_languageComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, [this]() { m_settingsChanged = true; });
    connect(m_autoCheckUpdatesCheckBox, &QCheckBox::toggled,
            this, [this]() { m_settingsChanged = true; });
    connect(m_debugModeCheckBox, &QCheckBox::toggled,
            this, [this]() { m_settingsChanged = true; });
}

void SettingsDialog::createApiTab()
{
    QWidget* apiTab = new QWidget();
    QVBoxLayout* mainLayout = new QVBoxLayout(apiTab);

    // API 密钥组
    QGroupBox* apiKeyGroup = new QGroupBox("API 密钥");
    QFormLayout* apiKeyLayout = new QFormLayout(apiKeyGroup);

#ifdef ELAWIDGETTOOLS_AVAILABLE
    m_apiKeyLineEdit = new ElaLineEdit();
#else
    m_apiKeyLineEdit = new QLineEdit();
#endif
    m_apiKeyLineEdit->setEchoMode(QLineEdit::Password);
    m_apiKeyLineEdit->setPlaceholderText("输入您的 Civitai API 密钥");
    apiKeyLayout->addRow("API 密钥:", m_apiKeyLineEdit);

    m_testApiButton = new QPushButton("测试连接");
    m_apiStatusLabel = new QLabel("未测试");

    QHBoxLayout* apiTestLayout = new QHBoxLayout();
    apiTestLayout->addWidget(m_testApiButton);
    apiTestLayout->addWidget(m_apiStatusLabel);
    apiTestLayout->addStretch();

    apiKeyLayout->addRow("状态:", apiTestLayout);
    mainLayout->addWidget(apiKeyGroup);

    // API 限制组
    QGroupBox* apiLimitsGroup = new QGroupBox("API 限制");
    QFormLayout* limitsLayout = new QFormLayout(apiLimitsGroup);

#ifdef ELAWIDGETTOOLS_AVAILABLE
    m_rateLimitSpinBox = new ElaSpinBox();
    m_retryCountSpinBox = new ElaSpinBox();
#else
    m_rateLimitSpinBox = new QSpinBox();
    m_retryCountSpinBox = new QSpinBox();
#endif

    m_rateLimitSpinBox->setRange(1, 10);
    m_rateLimitSpinBox->setSuffix(" 请求/秒");
    limitsLayout->addRow("速率限制:", m_rateLimitSpinBox);

    m_retryCountSpinBox->setRange(0, 10);
    m_retryCountSpinBox->setSuffix(" 次");
    limitsLayout->addRow("重试次数:", m_retryCountSpinBox);

    mainLayout->addWidget(apiLimitsGroup);
    mainLayout->addStretch();

    m_tabWidget->addTab(apiTab, "API");

    // 连接信号
    connect(m_apiKeyLineEdit, &QLineEdit::textChanged,
            this, &SettingsDialog::onApiKeyChanged);
    connect(m_testApiButton, &QPushButton::clicked,
            this, &SettingsDialog::onTestApiKey);
    connect(m_rateLimitSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, [this]() { m_settingsChanged = true; });
    connect(m_retryCountSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, [this]() { m_settingsChanged = true; });
}

void SettingsDialog::createCacheTab()
{
    QWidget* cacheTab = new QWidget();
    QVBoxLayout* mainLayout = new QVBoxLayout(cacheTab);

    // 缓存启用
    m_cacheEnabledCheckBox = new QCheckBox("启用缓存");
    mainLayout->addWidget(m_cacheEnabledCheckBox);

    // 内存缓存组
    QGroupBox* memoryCacheGroup = new QGroupBox("内存缓存");
    QFormLayout* memoryLayout = new QFormLayout(memoryCacheGroup);

    m_memoryCacheSizeSpinBox = new QSpinBox();
    m_memoryCacheSizeSpinBox->setRange(10, 1000);
    m_memoryCacheSizeSpinBox->setSuffix(" MB");
    memoryLayout->addRow("内存缓存大小:", m_memoryCacheSizeSpinBox);

    mainLayout->addWidget(memoryCacheGroup);

    // 磁盘缓存组
    QGroupBox* diskCacheGroup = new QGroupBox("磁盘缓存");
    QVBoxLayout* diskLayout = new QVBoxLayout(diskCacheGroup);

    m_diskCacheEnabledCheckBox = new QCheckBox("启用磁盘缓存");
    diskLayout->addWidget(m_diskCacheEnabledCheckBox);

    QFormLayout* diskFormLayout = new QFormLayout();

    QHBoxLayout* dirLayout = new QHBoxLayout();
    m_diskCacheDirectoryLineEdit = new QLineEdit();
    m_browseCacheDirectoryButton = new QPushButton("浏览...");
    dirLayout->addWidget(m_diskCacheDirectoryLineEdit);
    dirLayout->addWidget(m_browseCacheDirectoryButton);
    diskFormLayout->addRow("缓存目录:", dirLayout);

    m_diskCacheMaxSizeSpinBox = new QSpinBox();
    m_diskCacheMaxSizeSpinBox->setRange(100, 10000);
    m_diskCacheMaxSizeSpinBox->setSuffix(" MB");
    diskFormLayout->addRow("最大大小:", m_diskCacheMaxSizeSpinBox);

    diskLayout->addLayout(diskFormLayout);
    mainLayout->addWidget(diskCacheGroup);

    // 下载设置组
    QGroupBox* downloadGroup = new QGroupBox("下载设置");
    QFormLayout* downloadLayout = new QFormLayout(downloadGroup);

    m_maxConcurrentDownloadsSpinBox = new QSpinBox();
    m_maxConcurrentDownloadsSpinBox->setRange(1, 10);
    downloadLayout->addRow("并发下载数:", m_maxConcurrentDownloadsSpinBox);

    mainLayout->addWidget(downloadGroup);

    // 缓存管理
    QGroupBox* managementGroup = new QGroupBox("缓存管理");
    QVBoxLayout* managementLayout = new QVBoxLayout(managementGroup);

    m_cacheUsageLabel = new QLabel("缓存使用情况: 计算中...");
    managementLayout->addWidget(m_cacheUsageLabel);

    m_cacheUsageProgressBar = new QProgressBar();
    managementLayout->addWidget(m_cacheUsageProgressBar);

    m_clearCacheButton = new QPushButton("清空缓存");
    managementLayout->addWidget(m_clearCacheButton);

    mainLayout->addWidget(managementGroup);
    mainLayout->addStretch();

    m_tabWidget->addTab(cacheTab, "缓存");

    // 连接信号
    connect(m_browseCacheDirectoryButton, &QPushButton::clicked,
            this, &SettingsDialog::onCacheDirectoryBrowse);
    connect(m_clearCacheButton, &QPushButton::clicked,
            this, &SettingsDialog::onCacheClear);
    connect(m_cacheEnabledCheckBox, &QCheckBox::toggled,
            this, [this]() { m_settingsChanged = true; });
}

void SettingsDialog::createNetworkTab()
{
    QWidget* networkTab = new QWidget();
    QVBoxLayout* mainLayout = new QVBoxLayout(networkTab);

    // 下载设置组
    QGroupBox* downloadGroup = new QGroupBox("下载设置");
    QFormLayout* downloadLayout = new QFormLayout(downloadGroup);

    m_downloadTimeoutSpinBox = new QSpinBox();
    m_downloadTimeoutSpinBox->setRange(5, 300);
    m_downloadTimeoutSpinBox->setSuffix(" 秒");
    downloadLayout->addRow("下载超时:", m_downloadTimeoutSpinBox);

    mainLayout->addWidget(downloadGroup);

    // 代理设置组
    QGroupBox* proxyGroup = new QGroupBox("代理设置");
    QVBoxLayout* proxyLayout = new QVBoxLayout(proxyGroup);

    m_proxyEnabledCheckBox = new QCheckBox("启用代理");
    proxyLayout->addWidget(m_proxyEnabledCheckBox);

    QFormLayout* proxyFormLayout = new QFormLayout();

    m_proxyHostLineEdit = new QLineEdit();
    m_proxyHostLineEdit->setPlaceholderText("代理服务器地址");
    proxyFormLayout->addRow("主机:", m_proxyHostLineEdit);

    m_proxyPortSpinBox = new QSpinBox();
    m_proxyPortSpinBox->setRange(1, 65535);
    proxyFormLayout->addRow("端口:", m_proxyPortSpinBox);

    m_proxyUsernameLineEdit = new QLineEdit();
    m_proxyUsernameLineEdit->setPlaceholderText("用户名（可选）");
    proxyFormLayout->addRow("用户名:", m_proxyUsernameLineEdit);

    m_proxyPasswordLineEdit = new QLineEdit();
    m_proxyPasswordLineEdit->setEchoMode(QLineEdit::Password);
    m_proxyPasswordLineEdit->setPlaceholderText("密码（可选）");
    proxyFormLayout->addRow("密码:", m_proxyPasswordLineEdit);

    proxyLayout->addLayout(proxyFormLayout);
    mainLayout->addWidget(proxyGroup);
    mainLayout->addStretch();

    m_tabWidget->addTab(networkTab, "网络");

    // 连接信号
    connect(m_proxyEnabledCheckBox, &QCheckBox::toggled,
            this, [this](bool enabled) {
                m_proxyHostLineEdit->setEnabled(enabled);
                m_proxyPortSpinBox->setEnabled(enabled);
                m_proxyUsernameLineEdit->setEnabled(enabled);
                m_proxyPasswordLineEdit->setEnabled(enabled);
                m_settingsChanged = true;
            });
}

void SettingsDialog::createAdvancedTab()
{
    QWidget* advancedTab = new QWidget();
    QVBoxLayout* mainLayout = new QVBoxLayout(advancedTab);

    // 日志设置组
    QGroupBox* logGroup = new QGroupBox("日志设置");
    QFormLayout* logLayout = new QFormLayout(logGroup);

    m_logLevelComboBox = new QComboBox();
    m_logLevelComboBox->addItems({"Debug", "Info", "Warning", "Error"});
    logLayout->addRow("日志级别:", m_logLevelComboBox);

    mainLayout->addWidget(logGroup);

    // 图片设置组
    QGroupBox* imageGroup = new QGroupBox("图片设置");
    QFormLayout* imageLayout = new QFormLayout(imageGroup);

    m_autoLoadImagesCheckBox = new QCheckBox("自动加载图片");
    imageLayout->addRow(m_autoLoadImagesCheckBox);

    QHBoxLayout* qualityLayout = new QHBoxLayout();
    m_imageQualitySpinBox = new QSpinBox();
    m_imageQualitySpinBox->setRange(1, 100);
    m_imageQualitySpinBox->setSuffix("%");

#ifdef ELAWIDGETTOOLS_AVAILABLE
    m_imageQualitySlider = new ElaSlider(Qt::Horizontal);
#else
    m_imageQualitySlider = new QSlider(Qt::Horizontal);
#endif
    m_imageQualitySlider->setRange(1, 100);

    qualityLayout->addWidget(m_imageQualitySpinBox);
    qualityLayout->addWidget(m_imageQualitySlider);

    imageLayout->addRow("图片质量:", qualityLayout);
    mainLayout->addWidget(imageGroup);

    // 设置管理组
    QGroupBox* settingsGroup = new QGroupBox("设置管理");
    QVBoxLayout* settingsLayout = new QVBoxLayout(settingsGroup);

    QHBoxLayout* importExportLayout = new QHBoxLayout();
    m_importSettingsButton = new QPushButton("导入设置");
    m_exportSettingsButton = new QPushButton("导出设置");
    importExportLayout->addWidget(m_importSettingsButton);
    importExportLayout->addWidget(m_exportSettingsButton);
    importExportLayout->addStretch();

    settingsLayout->addLayout(importExportLayout);

    m_resetToDefaultsButton = new QPushButton("恢复默认设置");
    settingsLayout->addWidget(m_resetToDefaultsButton);

    mainLayout->addWidget(settingsGroup);
    mainLayout->addStretch();

    m_tabWidget->addTab(advancedTab, "高级");

    // 连接信号
    connect(m_imageQualitySpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            m_imageQualitySlider, &QSlider::setValue);
    connect(m_imageQualitySlider, &QSlider::valueChanged,
            m_imageQualitySpinBox, &QSpinBox::setValue);
    connect(m_resetToDefaultsButton, &QPushButton::clicked,
            this, &SettingsDialog::onResetToDefaults);
    connect(m_importSettingsButton, &QPushButton::clicked,
            this, &SettingsDialog::onImportSettings);
    connect(m_exportSettingsButton, &QPushButton::clicked,
            this, &SettingsDialog::onExportSettings);
}

void SettingsDialog::loadSettings()
{
    // 加载通用设置
    m_themeComboBox->setCurrentText(m_configManager->theme());
    m_languageComboBox->setCurrentText(m_configManager->language());
    m_autoCheckUpdatesCheckBox->setChecked(m_configManager->autoCheckUpdates());
    m_debugModeCheckBox->setChecked(m_configManager->isDebugMode());

    // 加载API设置
    m_apiKeyLineEdit->setText(m_configManager->apiKey());
    m_rateLimitSpinBox->setValue(m_configManager->apiRateLimit());
    m_retryCountSpinBox->setValue(m_configManager->apiRetryCount());

    // 加载缓存设置
    m_cacheEnabledCheckBox->setChecked(m_configManager->isCacheEnabled());
    m_memoryCacheSizeSpinBox->setValue(m_configManager->memoryCacheSize() / 1024); // 转换为MB
    m_diskCacheEnabledCheckBox->setChecked(m_configManager->isDiskCacheEnabled());
    m_diskCacheDirectoryLineEdit->setText(m_configManager->diskCacheDirectory());
    m_diskCacheMaxSizeSpinBox->setValue(m_configManager->diskCacheMaxSize() / (1024 * 1024)); // 转换为MB
    m_maxConcurrentDownloadsSpinBox->setValue(m_configManager->maxConcurrentDownloads());

    // 加载网络设置
    m_downloadTimeoutSpinBox->setValue(m_configManager->downloadTimeout() / 1000); // 转换为秒
    m_proxyEnabledCheckBox->setChecked(m_configManager->isProxyEnabled());
    m_proxyHostLineEdit->setText(m_configManager->proxyHost());
    m_proxyPortSpinBox->setValue(m_configManager->proxyPort());
    m_proxyUsernameLineEdit->setText(m_configManager->proxyUsername());
    m_proxyPasswordLineEdit->setText(m_configManager->proxyPassword());

    // 加载高级设置
    m_logLevelComboBox->setCurrentIndex(m_configManager->logLevel());
    m_autoLoadImagesCheckBox->setChecked(m_configManager->autoLoadImages());
    m_imageQualitySpinBox->setValue(m_configManager->imageQuality());
    m_imageQualitySlider->setValue(m_configManager->imageQuality());

    // 更新缓存使用情况
    updateCacheUsageDisplay();

    // 启用/禁用代理控件
    bool proxyEnabled = m_proxyEnabledCheckBox->isChecked();
    m_proxyHostLineEdit->setEnabled(proxyEnabled);
    m_proxyPortSpinBox->setEnabled(proxyEnabled);
    m_proxyUsernameLineEdit->setEnabled(proxyEnabled);
    m_proxyPasswordLineEdit->setEnabled(proxyEnabled);

    m_settingsChanged = false;
}

void SettingsDialog::saveSettings()
{
    // 保存通用设置
    m_configManager->setTheme(m_themeComboBox->currentText());
    m_configManager->setLanguage(m_languageComboBox->currentText());
    m_configManager->setAutoCheckUpdates(m_autoCheckUpdatesCheckBox->isChecked());
    m_configManager->setDebugMode(m_debugModeCheckBox->isChecked());

    // 保存API设置
    m_configManager->setApiKey(m_apiKeyLineEdit->text());
    m_configManager->setApiRateLimit(m_rateLimitSpinBox->value());
    m_configManager->setApiRetryCount(m_retryCountSpinBox->value());

    // 保存缓存设置
    m_configManager->setCacheEnabled(m_cacheEnabledCheckBox->isChecked());
    m_configManager->setMemoryCacheSize(m_memoryCacheSizeSpinBox->value() * 1024); // 转换为KB
    m_configManager->setDiskCacheEnabled(m_diskCacheEnabledCheckBox->isChecked());
    m_configManager->setDiskCacheDirectory(m_diskCacheDirectoryLineEdit->text());
    m_configManager->setDiskCacheMaxSize(static_cast<qint64>(m_diskCacheMaxSizeSpinBox->value()) * 1024 * 1024); // 转换为字节
    m_configManager->setMaxConcurrentDownloads(m_maxConcurrentDownloadsSpinBox->value());

    // 保存网络设置
    m_configManager->setDownloadTimeout(m_downloadTimeoutSpinBox->value() * 1000); // 转换为毫秒
    m_configManager->setProxyEnabled(m_proxyEnabledCheckBox->isChecked());
    m_configManager->setProxyHost(m_proxyHostLineEdit->text());
    m_configManager->setProxyPort(m_proxyPortSpinBox->value());
    m_configManager->setProxyUsername(m_proxyUsernameLineEdit->text());
    m_configManager->setProxyPassword(m_proxyPasswordLineEdit->text());

    // 保存高级设置
    m_configManager->setLogLevel(m_logLevelComboBox->currentIndex());
    m_configManager->setAutoLoadImages(m_autoLoadImagesCheckBox->isChecked());
    m_configManager->setImageQuality(m_imageQualitySpinBox->value());

    m_settingsChanged = false;
}

void SettingsDialog::validateSettings()
{
    // 验证API密钥
    QString apiKey = m_apiKeyLineEdit->text().trimmed();
    if (!apiKey.isEmpty() && !m_configManager->validateApiKey(apiKey)) {
        throw std::runtime_error("API密钥格式无效");
    }

    // 验证缓存目录
    QString cacheDir = m_diskCacheDirectoryLineEdit->text().trimmed();
    if (m_diskCacheEnabledCheckBox->isChecked() && !cacheDir.isEmpty()) {
        if (!m_configManager->validateCacheDirectory(cacheDir)) {
            throw std::runtime_error("缓存目录无效或无法创建");
        }
    }

    // 验证代理设置
    if (m_proxyEnabledCheckBox->isChecked()) {
        QString proxyHost = m_proxyHostLineEdit->text().trimmed();
        if (proxyHost.isEmpty()) {
            throw std::runtime_error("启用代理时必须指定代理主机");
        }
    }
}

// 槽函数实现
void SettingsDialog::onApiKeyChanged()
{
    m_settingsChanged = true;
    m_apiStatusLabel->setText("未测试");
    m_apiStatusLabel->setStyleSheet("");
}

void SettingsDialog::onCacheDirectoryBrowse()
{
    QString currentDir = m_diskCacheDirectoryLineEdit->text();
    if (currentDir.isEmpty()) {
        currentDir = QStandardPaths::writableLocation(QStandardPaths::CacheLocation);
    }

    QString selectedDir = QFileDialog::getExistingDirectory(this, "选择缓存目录", currentDir);
    if (!selectedDir.isEmpty()) {
        m_diskCacheDirectoryLineEdit->setText(selectedDir);
        m_settingsChanged = true;
    }
}

void SettingsDialog::onCacheClear()
{
    int ret = QMessageBox::question(this, "确认",
        "确定要清空所有缓存吗？这将删除所有已下载的图片缓存。",
        QMessageBox::Yes | QMessageBox::No,
        QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        // 这里应该调用ImageManager的清空缓存方法
        // 由于我们在对话框中没有直接访问ImageManager，
        // 可以通过信号或者回调来实现
        QMessageBox::information(this, "完成", "缓存已清空。");
        updateCacheUsageDisplay();
    }
}

void SettingsDialog::onResetToDefaults()
{
    int ret = QMessageBox::question(this, "确认",
        "确定要恢复所有设置到默认值吗？这将覆盖当前的所有配置。",
        QMessageBox::Yes | QMessageBox::No,
        QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        m_configManager->resetToDefaults();
        loadSettings();
        QMessageBox::information(this, "完成", "设置已恢复到默认值。");
    }
}

void SettingsDialog::onTestApiKey()
{
    QString apiKey = m_apiKeyLineEdit->text().trimmed();
    if (apiKey.isEmpty()) {
        m_apiStatusLabel->setText("请输入API密钥");
        m_apiStatusLabel->setStyleSheet("color: red;");
        return;
    }

    m_testApiButton->setEnabled(false);
    m_testApiButton->setText("测试中...");
    m_apiStatusLabel->setText("正在测试连接...");
    m_apiStatusLabel->setStyleSheet("color: blue;");

    // 这里应该实际测试API连接
    // 由于这是一个简化的实现，我们模拟测试结果
    QTimer::singleShot(2000, this, [this]() {
        m_testApiButton->setEnabled(true);
        m_testApiButton->setText("测试连接");

        // 模拟测试结果
        if (m_configManager->validateApiKey(m_apiKeyLineEdit->text())) {
            m_apiStatusLabel->setText("连接成功");
            m_apiStatusLabel->setStyleSheet("color: green;");
        } else {
            m_apiStatusLabel->setText("连接失败");
            m_apiStatusLabel->setStyleSheet("color: red;");
        }
    });
}

void SettingsDialog::onImportSettings()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        "导入设置",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation),
        "配置文件 (*.ini)");

    if (!fileName.isEmpty()) {
        if (m_configManager->importSettings(fileName)) {
            loadSettings();
            QMessageBox::information(this, "成功", "设置导入成功。");
        } else {
            QMessageBox::warning(this, "错误", "设置导入失败。");
        }
    }
}

void SettingsDialog::onExportSettings()
{
    QString fileName = QFileDialog::getSaveFileName(this,
        "导出设置",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/civitai_settings.ini",
        "配置文件 (*.ini)");

    if (!fileName.isEmpty()) {
        try {
            m_configManager->exportSettings(fileName);
            QMessageBox::information(this, "成功", "设置导出成功。");
        } catch (const std::exception& e) {
            QMessageBox::warning(this, "错误", QString("设置导出失败：%1").arg(e.what()));
        }
    }
}

// 工具方法
void SettingsDialog::updateCacheUsageDisplay()
{
    // 这里应该计算实际的缓存使用情况
    // 由于我们在对话框中没有直接访问ImageManager，
    // 这里提供一个简化的实现

    qint64 maxSize = static_cast<qint64>(m_diskCacheMaxSizeSpinBox->value()) * 1024 * 1024;
    qint64 currentSize = 0; // 这里应该从ImageManager获取实际使用量

    if (maxSize > 0) {
        int percentage = static_cast<int>((currentSize * 100) / maxSize);
        m_cacheUsageProgressBar->setValue(percentage);
    } else {
        m_cacheUsageProgressBar->setValue(0);
    }

    m_cacheUsageLabel->setText(QString("缓存使用情况: %1 / %2")
        .arg(formatFileSize(currentSize))
        .arg(formatFileSize(maxSize)));
}

void SettingsDialog::enableApiControls(bool enabled)
{
    m_rateLimitSpinBox->setEnabled(enabled);
    m_retryCountSpinBox->setEnabled(enabled);
    m_testApiButton->setEnabled(enabled);
}

QString SettingsDialog::formatFileSize(qint64 bytes) const
{
    const qint64 KB = 1024;
    const qint64 MB = KB * 1024;
    const qint64 GB = MB * 1024;

    if (bytes >= GB) {
        return QString("%1 GB").arg(static_cast<double>(bytes) / GB, 0, 'f', 2);
    } else if (bytes >= MB) {
        return QString("%1 MB").arg(static_cast<double>(bytes) / MB, 0, 'f', 1);
    } else if (bytes >= KB) {
        return QString("%1 KB").arg(static_cast<double>(bytes) / KB, 0, 'f', 1);
    } else {
        return QString("%1 B").arg(bytes);
    }
}
