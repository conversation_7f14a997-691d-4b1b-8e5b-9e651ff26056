{"classes": [{"className": "ElaLineEditPrivate", "lineNumber": 10, "methods": [{"access": "public", "arguments": [{"name": "data", "type": "QVariantMap"}], "index": 2, "name": "onWMWindowClickedEvent", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "member": "_pExpandMarkWidth", "name": "pExpandMarkWidth", "notify": "pExpandMarkWidthChanged", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "ElaLineEditPrivate", "signals": [{"access": "public", "index": 0, "name": "pExpandMarkWidthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "themeMode", "type": "ElaThemeType::ThemeMode"}], "index": 1, "name": "onThemeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "ElaLineEditPrivate.h", "outputRevision": 69}