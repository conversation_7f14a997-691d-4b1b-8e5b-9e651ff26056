/****************************************************************************
** Meta object code from reading C++ file 'ElaThemeAnimationWidget.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/DeveloperComponents/ElaThemeAnimationWidget.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaThemeAnimationWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN23ElaThemeAnimationWidgetE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaThemeAnimationWidget::qt_create_metaobjectdata<qt_meta_tag_ZN23ElaThemeAnimationWidgetE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaThemeAnimationWidget",
        "pRadiusChanged",
        "",
        "pEndRadiusChanged",
        "pCenterChanged",
        "pOldWindowBackgroundChanged",
        "pNewWindowBackgroundChanged",
        "animationFinished",
        "pRadius",
        "pEndRadius",
        "pCenter",
        "pOldWindowBackground",
        "pNewWindowBackground"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pRadiusChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pEndRadiusChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCenterChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pOldWindowBackgroundChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pNewWindowBackgroundChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'animationFinished'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pRadius'
        QtMocHelpers::PropertyData<qreal>(8, QMetaType::QReal, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pEndRadius'
        QtMocHelpers::PropertyData<qreal>(9, QMetaType::QReal, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pCenter'
        QtMocHelpers::PropertyData<QPoint>(10, QMetaType::QPoint, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pOldWindowBackground'
        QtMocHelpers::PropertyData<QImage>(11, QMetaType::QImage, QMC::DefaultPropertyFlags | QMC::Writable, 3),
        // property 'pNewWindowBackground'
        QtMocHelpers::PropertyData<QImage>(12, QMetaType::QImage, QMC::DefaultPropertyFlags | QMC::Writable, 4),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaThemeAnimationWidget, qt_meta_tag_ZN23ElaThemeAnimationWidgetE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaThemeAnimationWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN23ElaThemeAnimationWidgetE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN23ElaThemeAnimationWidgetE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN23ElaThemeAnimationWidgetE_t>.metaTypes,
    nullptr
} };

void ElaThemeAnimationWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaThemeAnimationWidget *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pRadiusChanged(); break;
        case 1: _t->pEndRadiusChanged(); break;
        case 2: _t->pCenterChanged(); break;
        case 3: _t->pOldWindowBackgroundChanged(); break;
        case 4: _t->pNewWindowBackgroundChanged(); break;
        case 5: _t->animationFinished(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaThemeAnimationWidget::*)()>(_a, &ElaThemeAnimationWidget::pRadiusChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaThemeAnimationWidget::*)()>(_a, &ElaThemeAnimationWidget::pEndRadiusChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaThemeAnimationWidget::*)()>(_a, &ElaThemeAnimationWidget::pCenterChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaThemeAnimationWidget::*)()>(_a, &ElaThemeAnimationWidget::pOldWindowBackgroundChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaThemeAnimationWidget::*)()>(_a, &ElaThemeAnimationWidget::pNewWindowBackgroundChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaThemeAnimationWidget::*)()>(_a, &ElaThemeAnimationWidget::animationFinished, 5))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<qreal*>(_v) = _t->_pRadius; break;
        case 1: *reinterpret_cast<qreal*>(_v) = _t->_pEndRadius; break;
        case 2: *reinterpret_cast<QPoint*>(_v) = _t->_pCenter; break;
        case 3: *reinterpret_cast<QImage*>(_v) = _t->_pOldWindowBackground; break;
        case 4: *reinterpret_cast<QImage*>(_v) = _t->_pNewWindowBackground; break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0:
            if (QtMocHelpers::setProperty(_t->_pRadius, *reinterpret_cast<qreal*>(_v)))
                Q_EMIT _t->pRadiusChanged();
            break;
        case 1:
            if (QtMocHelpers::setProperty(_t->_pEndRadius, *reinterpret_cast<qreal*>(_v)))
                Q_EMIT _t->pEndRadiusChanged();
            break;
        case 2:
            if (QtMocHelpers::setProperty(_t->_pCenter, *reinterpret_cast<QPoint*>(_v)))
                Q_EMIT _t->pCenterChanged();
            break;
        case 3:
            if (QtMocHelpers::setProperty(_t->_pOldWindowBackground, *reinterpret_cast<QImage*>(_v)))
                Q_EMIT _t->pOldWindowBackgroundChanged();
            break;
        case 4:
            if (QtMocHelpers::setProperty(_t->_pNewWindowBackground, *reinterpret_cast<QImage*>(_v)))
                Q_EMIT _t->pNewWindowBackgroundChanged();
            break;
        default: break;
        }
    }
}

const QMetaObject *ElaThemeAnimationWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaThemeAnimationWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN23ElaThemeAnimationWidgetE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ElaThemeAnimationWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 6;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    }
    return _id;
}

// SIGNAL 0
void ElaThemeAnimationWidget::pRadiusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaThemeAnimationWidget::pEndRadiusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaThemeAnimationWidget::pCenterChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaThemeAnimationWidget::pOldWindowBackgroundChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaThemeAnimationWidget::pNewWindowBackgroundChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ElaThemeAnimationWidget::animationFinished()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}
QT_WARNING_POP
