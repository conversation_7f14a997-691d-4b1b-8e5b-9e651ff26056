@echo off
echo ========================================
echo Civitai 图片查看器 - 快速构建脚本
echo ========================================
echo.

REM 设置颜色
color 0A

echo [1/8] 检查当前目录...
if not exist "CMakeLists.txt" (
    echo ❌ 错误: 未找到 CMakeLists.txt
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)
echo ✅ 项目目录检查通过

echo.
echo [2/8] 检查源文件...
if not exist "src\main.cpp" (
    echo ❌ 错误: 未找到源文件
    pause
    exit /b 1
)
echo ✅ 源文件检查通过

echo.
echo [3/8] 检查 ElaWidgetTools...
if not exist "third_party\ElaWidgetTools\CMakeLists.txt" (
    echo ❌ 错误: ElaWidgetTools 未找到
    echo 正在尝试下载...
    git clone https://github.com/Liniyous/ElaWidgetTools.git third_party/ElaWidgetTools
    if %ERRORLEVEL% neq 0 (
        echo ❌ 下载失败，请手动下载
        pause
        exit /b 1
    )
)
echo ✅ ElaWidgetTools 检查通过

echo.
echo [4/8] 检查 CMake...
cmake --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ 错误: CMake 未找到
    echo 请确保 CMake 已安装并添加到 PATH
    pause
    exit /b 1
)
echo ✅ CMake 检查通过

echo.
echo [5/8] 检查 Qt...
qmake -version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ⚠️  警告: qmake 未找到，但将继续尝试构建
) else (
    echo ✅ Qt 检查通过
)

echo.
echo [6/8] 创建构建目录...
if exist "build" (
    echo 清理旧的构建目录...
    rmdir /s /q build
)
mkdir build
cd build

echo.
echo [7/8] 配置项目...
echo 正在运行 CMake 配置...
cmake .. -DCMAKE_BUILD_TYPE=Release -G "Visual Studio 16 2019" -A x64

if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ CMake 配置失败！
    echo.
    echo 尝试其他生成器...
    cmake .. -DCMAKE_BUILD_TYPE=Release -G "Visual Studio 17 2022" -A x64
    
    if %ERRORLEVEL% neq 0 (
        echo.
        echo 尝试 MinGW...
        cmake .. -DCMAKE_BUILD_TYPE=Release -G "MinGW Makefiles"
        
        if %ERRORLEVEL% neq 0 (
            echo.
            echo ❌ 所有配置方法都失败了！
            echo.
            echo 可能的解决方案：
            echo 1. 确保 Visual Studio 2019/2022 已正确安装
            echo 2. 确保 Qt 6.2+ 已安装并在 PATH 中
            echo 3. 尝试在 Qt Creator 中打开项目
            echo 4. 检查 CMake 错误信息
            echo.
            pause
            exit /b 1
        )
    )
)

echo ✅ CMake 配置成功

echo.
echo [8/8] 构建项目...
echo 正在编译，请稍候...
cmake --build . --config Release --parallel

if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ 构建失败！
    echo.
    echo 请检查上面的错误信息
    echo 常见问题：
    echo 1. 缺少 Qt 开发库
    echo 2. ElaWidgetTools 编译错误
    echo 3. 编译器版本不兼容
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎉 构建成功！
echo ========================================
echo.

REM 查找可执行文件
if exist "bin\Release\CivitaiImageViewer.exe" (
    set EXECUTABLE=bin\Release\CivitaiImageViewer.exe
) else if exist "bin\CivitaiImageViewer.exe" (
    set EXECUTABLE=bin\CivitaiImageViewer.exe
) else if exist "Release\CivitaiImageViewer.exe" (
    set EXECUTABLE=Release\CivitaiImageViewer.exe
) else (
    echo ⚠️  可执行文件位置未知，请在 build 目录中查找 CivitaiImageViewer.exe
    set EXECUTABLE=
)

if defined EXECUTABLE (
    echo 可执行文件位置: build\%EXECUTABLE%
    echo.
    echo 下一步：
    echo 1. 获取 Civitai API 密钥：https://civitai.com/user/account
    echo 2. 运行程序并配置 API 密钥
    echo 3. 开始浏览 AI 艺术作品！
    echo.
    
    set /p choice="是否立即运行程序？(y/n): "
    if /i "%choice%"=="y" (
        echo.
        echo 正在启动程序...
        start %EXECUTABLE%
        echo.
        echo 程序已启动！
        echo 请在程序中配置您的 Civitai API 密钥。
    )
) else (
    echo.
    echo 请手动查找并运行 CivitaiImageViewer.exe
)

echo.
echo 构建完成！
pause
