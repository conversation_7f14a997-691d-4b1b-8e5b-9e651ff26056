{"classes": [{"className": "ElaMultiSelectComboBoxPrivate", "lineNumber": 11, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "member": "_pExpandIconRotate", "name": "pExpandIconRotate", "notify": "pExpandIconRotateChanged", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "member": "_pExpandMarkWidth", "name": "pExpandMarkWidth", "notify": "pExpandMarkWidthChanged", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "ElaMultiSelectComboBoxPrivate", "signals": [{"access": "public", "index": 0, "name": "pExpandIconRotateChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pExpandMarkWidthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "index", "type": "QModelIndex"}], "index": 2, "name": "onItemPressed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "ElaMultiSelectComboBoxPrivate.h", "outputRevision": 69}