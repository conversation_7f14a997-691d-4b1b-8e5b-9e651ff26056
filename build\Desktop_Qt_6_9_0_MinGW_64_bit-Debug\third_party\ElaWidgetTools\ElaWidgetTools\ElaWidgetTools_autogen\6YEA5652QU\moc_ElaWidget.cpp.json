{"classes": [{"className": "ElaW<PERSON>t", "lineNumber": 9, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pIsStayTop", "notify": "pIsStayTopChanged", "read": "getIsStayTop", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setIsStayTop"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pIsFixedSize", "notify": "pIsFixedSizeChanged", "read": "getIsFixedSize", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setIsFixedSize"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "pIsDefaultClosed", "notify": "pIsDefaultClosedChanged", "read": "getIsDefaultClosed", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setIsDefaultClosed"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "pAppBarHeight", "notify": "pAppBarHeightChanged", "read": "getAppBarHeight", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setAppBarHeight"}], "qualifiedClassName": "ElaW<PERSON>t", "signals": [{"access": "public", "index": 0, "name": "pIsStayTopChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pIsFixedSizeChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "pIsDefaultClosedChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "pAppBarHeightChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "routeBackButtonClicked", "returnType": "void"}, {"access": "public", "index": 5, "name": "navigationButtonClicked", "returnType": "void"}, {"access": "public", "index": 6, "name": "themeChangeButtonClicked", "returnType": "void"}, {"access": "public", "index": 7, "name": "closeButtonClicked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "ElaWidget.h", "outputRevision": 69}