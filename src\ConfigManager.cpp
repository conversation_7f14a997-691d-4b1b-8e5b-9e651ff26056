#include "ConfigManager.h"
#include <QStandardPaths>
#include <QDir>
#include <QDebug>
#include <QDateTime>

// 配置键常量定义
const QString ConfigManager::KEY_API_KEY = "api/key";
const QString ConfigManager::KEY_API_RATE_LIMIT = "api/rateLimit";
const QString ConfigManager::KEY_API_RETRY_COUNT = "api/retryCount";

const QString ConfigManager::KEY_WINDOW_GEOMETRY = "ui/windowGeometry";
const QString ConfigManager::KEY_WINDOW_STATE = "ui/windowState";
const QString ConfigManager::KEY_SPLITTER_STATE = "ui/splitterState";
const QString ConfigManager::KEY_THEME = "ui/theme";
const QString ConfigManager::KEY_LANGUAGE = "ui/language";

const QString ConfigManager::KEY_DEFAULT_SORT = "search/defaultSort";
const QString ConfigManager::KEY_DEFAULT_PERIOD = "search/defaultPeriod";
const QString ConfigManager::KEY_DEFAULT_NSFW_LEVEL = "search/defaultNsfwLevel";
const QString ConfigManager::KEY_SEARCH_HISTORY = "search/history";

const QString ConfigManager::KEY_IMAGE_CARD_SIZE = "display/imageCardSize";
const QString ConfigManager::KEY_GRID_COLUMNS = "display/gridColumns";
const QString ConfigManager::KEY_AUTO_LOAD_IMAGES = "display/autoLoadImages";
const QString ConfigManager::KEY_IMAGE_QUALITY = "display/imageQuality";

const QString ConfigManager::KEY_CACHE_ENABLED = "cache/enabled";
const QString ConfigManager::KEY_MEMORY_CACHE_SIZE = "cache/memorySizeKB";
const QString ConfigManager::KEY_DISK_CACHE_ENABLED = "cache/diskEnabled";
const QString ConfigManager::KEY_DISK_CACHE_DIRECTORY = "cache/diskDirectory";
const QString ConfigManager::KEY_DISK_CACHE_MAX_SIZE = "cache/diskMaxSizeBytes";
const QString ConfigManager::KEY_MAX_CONCURRENT_DOWNLOADS = "cache/maxConcurrentDownloads";

const QString ConfigManager::KEY_DOWNLOAD_TIMEOUT = "network/downloadTimeoutMs";
const QString ConfigManager::KEY_PROXY_ENABLED = "network/proxyEnabled";
const QString ConfigManager::KEY_PROXY_HOST = "network/proxyHost";
const QString ConfigManager::KEY_PROXY_PORT = "network/proxyPort";
const QString ConfigManager::KEY_PROXY_USERNAME = "network/proxyUsername";
const QString ConfigManager::KEY_PROXY_PASSWORD = "network/proxyPassword";

const QString ConfigManager::KEY_DEBUG_MODE = "advanced/debugMode";
const QString ConfigManager::KEY_LOG_LEVEL = "advanced/logLevel";
const QString ConfigManager::KEY_AUTO_CHECK_UPDATES = "advanced/autoCheckUpdates";
const QString ConfigManager::KEY_LAST_UPDATE_CHECK = "advanced/lastUpdateCheck";

// 默认值常量定义
const int ConfigManager::DEFAULT_API_RATE_LIMIT = 1;
const int ConfigManager::DEFAULT_API_RETRY_COUNT = 3;
const QString ConfigManager::DEFAULT_THEME = "Light";
const QString ConfigManager::DEFAULT_LANGUAGE = "zh_CN";
const QString ConfigManager::DEFAULT_SORT = "Most Reactions";
const QString ConfigManager::DEFAULT_PERIOD = "AllTime";
const QString ConfigManager::DEFAULT_NSFW_LEVEL = "None";
const QSize ConfigManager::DEFAULT_IMAGE_CARD_SIZE = QSize(280, 360);
const int ConfigManager::DEFAULT_GRID_COLUMNS = 4;
const int ConfigManager::DEFAULT_IMAGE_QUALITY = 85;
const int ConfigManager::DEFAULT_MEMORY_CACHE_SIZE = 50 * 1024; // 50MB
const qint64 ConfigManager::DEFAULT_DISK_CACHE_MAX_SIZE = 1024LL * 1024 * 1024; // 1GB
const int ConfigManager::DEFAULT_MAX_CONCURRENT_DOWNLOADS = 4;
const int ConfigManager::DEFAULT_DOWNLOAD_TIMEOUT = 30000; // 30秒
const int ConfigManager::DEFAULT_LOG_LEVEL = 2; // Info level

ConfigManager::ConfigManager(QObject *parent)
    : QObject(parent)
    , m_settings(nullptr)
{
    // 创建配置目录
    QString configDir = QStandardPaths::writableLocation(QStandardPaths::ConfigLocation);
    QDir().mkpath(configDir);

    // 初始化QSettings
    QString configFile = configDir + "/CivitaiImageViewer.ini";
    m_settings = new QSettings(configFile, QSettings::IniFormat, this);

    // 设置编码
    m_settings->setIniCodec("UTF-8");

    // 初始化默认值
    initializeDefaults();

    // 迁移旧设置（如果需要）
    migrateOldSettings();

    qDebug() << "Config file:" << configFile;
}

ConfigManager::~ConfigManager()
{
    if (m_settings) {
        m_settings->sync();
    }
}

void ConfigManager::setValue(const QString& key, const QVariant& value)
{
    QVariant oldValue = m_settings->value(key);
    m_settings->setValue(key, value);

    if (oldValue != value) {
        emit configChanged(key, value);

        // 发送特定信号
        if (key == KEY_API_KEY) {
            emit apiKeyChanged(value.toString());
        } else if (key == KEY_THEME) {
            emit themeChanged(value.toString());
        }
    }
}

QVariant ConfigManager::getValue(const QString& key, const QVariant& defaultValue) const
{
    return m_settings->value(key, defaultValue);
}

bool ConfigManager::contains(const QString& key) const
{
    return m_settings->contains(key);
}

void ConfigManager::removeKey(const QString& key)
{
    m_settings->remove(key);
}

// API 配置
void ConfigManager::setApiKey(const QString& apiKey)
{
    setValue(KEY_API_KEY, apiKey);
}

QString ConfigManager::apiKey() const
{
    return getValue(KEY_API_KEY).toString();
}

void ConfigManager::setApiRateLimit(int requestsPerSecond)
{
    setValue(KEY_API_RATE_LIMIT, requestsPerSecond);
}

int ConfigManager::apiRateLimit() const
{
    return getValue(KEY_API_RATE_LIMIT, DEFAULT_API_RATE_LIMIT).toInt();
}

void ConfigManager::setApiRetryCount(int maxRetries)
{
    setValue(KEY_API_RETRY_COUNT, maxRetries);
}

int ConfigManager::apiRetryCount() const
{
    return getValue(KEY_API_RETRY_COUNT, DEFAULT_API_RETRY_COUNT).toInt();
}

// UI 配置
void ConfigManager::setWindowGeometry(const QByteArray& geometry)
{
    setValue(KEY_WINDOW_GEOMETRY, geometry);
}

QByteArray ConfigManager::windowGeometry() const
{
    return getValue(KEY_WINDOW_GEOMETRY).toByteArray();
}

void ConfigManager::setWindowState(const QByteArray& state)
{
    setValue(KEY_WINDOW_STATE, state);
}

QByteArray ConfigManager::windowState() const
{
    return getValue(KEY_WINDOW_STATE).toByteArray();
}

void ConfigManager::setSplitterState(const QByteArray& state)
{
    setValue(KEY_SPLITTER_STATE, state);
}

QByteArray ConfigManager::splitterState() const
{
    return getValue(KEY_SPLITTER_STATE).toByteArray();
}

void ConfigManager::setTheme(const QString& theme)
{
    setValue(KEY_THEME, theme);
}

QString ConfigManager::theme() const
{
    return getValue(KEY_THEME, DEFAULT_THEME).toString();
}

void ConfigManager::setLanguage(const QString& language)
{
    setValue(KEY_LANGUAGE, language);
}

QString ConfigManager::language() const
{
    return getValue(KEY_LANGUAGE, DEFAULT_LANGUAGE).toString();
}

// 搜索配置
void ConfigManager::setDefaultSort(const QString& sort)
{
    setValue(KEY_DEFAULT_SORT, sort);
}

QString ConfigManager::defaultSort() const
{
    return getValue(KEY_DEFAULT_SORT, DEFAULT_SORT).toString();
}

void ConfigManager::setDefaultPeriod(const QString& period)
{
    setValue(KEY_DEFAULT_PERIOD, period);
}

QString ConfigManager::defaultPeriod() const
{
    return getValue(KEY_DEFAULT_PERIOD, DEFAULT_PERIOD).toString();
}

void ConfigManager::setDefaultNsfwLevel(const QString& nsfwLevel)
{
    setValue(KEY_DEFAULT_NSFW_LEVEL, nsfwLevel);
}

QString ConfigManager::defaultNsfwLevel() const
{
    return getValue(KEY_DEFAULT_NSFW_LEVEL, DEFAULT_NSFW_LEVEL).toString();
}

void ConfigManager::setSearchHistory(const QStringList& history)
{
    setValue(KEY_SEARCH_HISTORY, history);
}

QStringList ConfigManager::searchHistory() const
{
    return getValue(KEY_SEARCH_HISTORY).toStringList();
}

void ConfigManager::addToSearchHistory(const QString& query)
{
    if (query.trimmed().isEmpty()) {
        return;
    }

    QStringList history = searchHistory();
    history.removeAll(query); // 移除重复项
    history.prepend(query);   // 添加到开头

    // 限制历史记录数量
    const int maxHistorySize = 50;
    if (history.size() > maxHistorySize) {
        history = history.mid(0, maxHistorySize);
    }

    setSearchHistory(history);
}

void ConfigManager::clearSearchHistory()
{
    setValue(KEY_SEARCH_HISTORY, QStringList());
}

// 图片显示配置
void ConfigManager::setImageCardSize(const QSize& size)
{
    setValue(KEY_IMAGE_CARD_SIZE, size);
}

QSize ConfigManager::imageCardSize() const
{
    return getValue(KEY_IMAGE_CARD_SIZE, DEFAULT_IMAGE_CARD_SIZE).toSize();
}

void ConfigManager::setGridColumns(int columns)
{
    setValue(KEY_GRID_COLUMNS, columns);
}

int ConfigManager::gridColumns() const
{
    return getValue(KEY_GRID_COLUMNS, DEFAULT_GRID_COLUMNS).toInt();
}

void ConfigManager::setAutoLoadImages(bool autoLoad)
{
    setValue(KEY_AUTO_LOAD_IMAGES, autoLoad);
}

bool ConfigManager::autoLoadImages() const
{
    return getValue(KEY_AUTO_LOAD_IMAGES, true).toBool();
}

void ConfigManager::setImageQuality(int quality)
{
    setValue(KEY_IMAGE_QUALITY, qBound(0, quality, 100));
}

int ConfigManager::imageQuality() const
{
    return getValue(KEY_IMAGE_QUALITY, DEFAULT_IMAGE_QUALITY).toInt();
}

// 缓存配置
void ConfigManager::setCacheEnabled(bool enabled)
{
    setValue(KEY_CACHE_ENABLED, enabled);
}

bool ConfigManager::isCacheEnabled() const
{
    return getValue(KEY_CACHE_ENABLED, true).toBool();
}

void ConfigManager::setMemoryCacheSize(int sizeKB)
{
    setValue(KEY_MEMORY_CACHE_SIZE, sizeKB);
}

int ConfigManager::memoryCacheSize() const
{
    return getValue(KEY_MEMORY_CACHE_SIZE, DEFAULT_MEMORY_CACHE_SIZE).toInt();
}

void ConfigManager::setDiskCacheEnabled(bool enabled)
{
    setValue(KEY_DISK_CACHE_ENABLED, enabled);
}

bool ConfigManager::isDiskCacheEnabled() const
{
    return getValue(KEY_DISK_CACHE_ENABLED, true).toBool();
}

void ConfigManager::setDiskCacheDirectory(const QString& directory)
{
    setValue(KEY_DISK_CACHE_DIRECTORY, directory);
}

QString ConfigManager::diskCacheDirectory() const
{
    QString defaultDir = QStandardPaths::writableLocation(QStandardPaths::CacheLocation) + "/images";
    return getValue(KEY_DISK_CACHE_DIRECTORY, defaultDir).toString();
}

void ConfigManager::setDiskCacheMaxSize(qint64 sizeBytes)
{
    setValue(KEY_DISK_CACHE_MAX_SIZE, sizeBytes);
}

qint64 ConfigManager::diskCacheMaxSize() const
{
    return getValue(KEY_DISK_CACHE_MAX_SIZE, DEFAULT_DISK_CACHE_MAX_SIZE).toLongLong();
}

void ConfigManager::setMaxConcurrentDownloads(int maxDownloads)
{
    setValue(KEY_MAX_CONCURRENT_DOWNLOADS, maxDownloads);
}

int ConfigManager::maxConcurrentDownloads() const
{
    return getValue(KEY_MAX_CONCURRENT_DOWNLOADS, DEFAULT_MAX_CONCURRENT_DOWNLOADS).toInt();
}

// 网络配置
void ConfigManager::setDownloadTimeout(int timeoutMs)
{
    setValue(KEY_DOWNLOAD_TIMEOUT, timeoutMs);
}

int ConfigManager::downloadTimeout() const
{
    return getValue(KEY_DOWNLOAD_TIMEOUT, DEFAULT_DOWNLOAD_TIMEOUT).toInt();
}

void ConfigManager::setProxyEnabled(bool enabled)
{
    setValue(KEY_PROXY_ENABLED, enabled);
}

bool ConfigManager::isProxyEnabled() const
{
    return getValue(KEY_PROXY_ENABLED, false).toBool();
}

void ConfigManager::setProxyHost(const QString& host)
{
    setValue(KEY_PROXY_HOST, host);
}

QString ConfigManager::proxyHost() const
{
    return getValue(KEY_PROXY_HOST).toString();
}

void ConfigManager::setProxyPort(int port)
{
    setValue(KEY_PROXY_PORT, port);
}

int ConfigManager::proxyPort() const
{
    return getValue(KEY_PROXY_PORT, 8080).toInt();
}

void ConfigManager::setProxyUsername(const QString& username)
{
    setValue(KEY_PROXY_USERNAME, username);
}

QString ConfigManager::proxyUsername() const
{
    return getValue(KEY_PROXY_USERNAME).toString();
}

void ConfigManager::setProxyPassword(const QString& password)
{
    setValue(KEY_PROXY_PASSWORD, encryptPassword(password));
}

QString ConfigManager::proxyPassword() const
{
    QString encrypted = getValue(KEY_PROXY_PASSWORD).toString();
    return decryptPassword(encrypted);
}

// 高级配置
void ConfigManager::setDebugMode(bool enabled)
{
    setValue(KEY_DEBUG_MODE, enabled);
}

bool ConfigManager::isDebugMode() const
{
    return getValue(KEY_DEBUG_MODE, false).toBool();
}

void ConfigManager::setLogLevel(int level)
{
    setValue(KEY_LOG_LEVEL, level);
}

int ConfigManager::logLevel() const
{
    return getValue(KEY_LOG_LEVEL, DEFAULT_LOG_LEVEL).toInt();
}

void ConfigManager::setAutoCheckUpdates(bool enabled)
{
    setValue(KEY_AUTO_CHECK_UPDATES, enabled);
}

bool ConfigManager::autoCheckUpdates() const
{
    return getValue(KEY_AUTO_CHECK_UPDATES, true).toBool();
}

void ConfigManager::setLastUpdateCheck(const QDateTime& dateTime)
{
    setValue(KEY_LAST_UPDATE_CHECK, dateTime);
}

QDateTime ConfigManager::lastUpdateCheck() const
{
    return getValue(KEY_LAST_UPDATE_CHECK).toDateTime();
}

// 配置管理
void ConfigManager::resetToDefaults()
{
    m_settings->clear();
    initializeDefaults();

    // 发送重置信号
    emit configChanged("*", QVariant());
}

void ConfigManager::exportSettings(const QString& filePath) const
{
    QSettings exportSettings(filePath, QSettings::IniFormat);
    exportSettings.setIniCodec("UTF-8");

    // 复制所有设置
    for (const QString& key : m_settings->allKeys()) {
        exportSettings.setValue(key, m_settings->value(key));
    }

    exportSettings.sync();
}

bool ConfigManager::importSettings(const QString& filePath)
{
    if (!QFileInfo::exists(filePath)) {
        return false;
    }

    QSettings importSettings(filePath, QSettings::IniFormat);
    importSettings.setIniCodec("UTF-8");

    // 导入所有设置
    for (const QString& key : importSettings.allKeys()) {
        setValue(key, importSettings.value(key));
    }

    return true;
}

// 配置验证
bool ConfigManager::validateApiKey(const QString& apiKey) const
{
    // 简单验证：非空且长度合理
    return !apiKey.trimmed().isEmpty() && apiKey.length() >= 10;
}

bool ConfigManager::validateCacheDirectory(const QString& directory) const
{
    QDir dir(directory);
    return dir.exists() || dir.mkpath(directory);
}

// 私有方法
void ConfigManager::initializeDefaults()
{
    // 只设置不存在的默认值
    if (!contains(KEY_API_RATE_LIMIT)) {
        setValue(KEY_API_RATE_LIMIT, DEFAULT_API_RATE_LIMIT);
    }
    if (!contains(KEY_API_RETRY_COUNT)) {
        setValue(KEY_API_RETRY_COUNT, DEFAULT_API_RETRY_COUNT);
    }
    if (!contains(KEY_THEME)) {
        setValue(KEY_THEME, DEFAULT_THEME);
    }
    if (!contains(KEY_LANGUAGE)) {
        setValue(KEY_LANGUAGE, DEFAULT_LANGUAGE);
    }
    if (!contains(KEY_DEFAULT_SORT)) {
        setValue(KEY_DEFAULT_SORT, DEFAULT_SORT);
    }
    if (!contains(KEY_DEFAULT_PERIOD)) {
        setValue(KEY_DEFAULT_PERIOD, DEFAULT_PERIOD);
    }
    if (!contains(KEY_DEFAULT_NSFW_LEVEL)) {
        setValue(KEY_DEFAULT_NSFW_LEVEL, DEFAULT_NSFW_LEVEL);
    }
    if (!contains(KEY_IMAGE_CARD_SIZE)) {
        setValue(KEY_IMAGE_CARD_SIZE, DEFAULT_IMAGE_CARD_SIZE);
    }
    if (!contains(KEY_GRID_COLUMNS)) {
        setValue(KEY_GRID_COLUMNS, DEFAULT_GRID_COLUMNS);
    }
    if (!contains(KEY_AUTO_LOAD_IMAGES)) {
        setValue(KEY_AUTO_LOAD_IMAGES, true);
    }
    if (!contains(KEY_IMAGE_QUALITY)) {
        setValue(KEY_IMAGE_QUALITY, DEFAULT_IMAGE_QUALITY);
    }
    if (!contains(KEY_CACHE_ENABLED)) {
        setValue(KEY_CACHE_ENABLED, true);
    }
    if (!contains(KEY_MEMORY_CACHE_SIZE)) {
        setValue(KEY_MEMORY_CACHE_SIZE, DEFAULT_MEMORY_CACHE_SIZE);
    }
    if (!contains(KEY_DISK_CACHE_ENABLED)) {
        setValue(KEY_DISK_CACHE_ENABLED, true);
    }
    if (!contains(KEY_DISK_CACHE_MAX_SIZE)) {
        setValue(KEY_DISK_CACHE_MAX_SIZE, DEFAULT_DISK_CACHE_MAX_SIZE);
    }
    if (!contains(KEY_MAX_CONCURRENT_DOWNLOADS)) {
        setValue(KEY_MAX_CONCURRENT_DOWNLOADS, DEFAULT_MAX_CONCURRENT_DOWNLOADS);
    }
    if (!contains(KEY_DOWNLOAD_TIMEOUT)) {
        setValue(KEY_DOWNLOAD_TIMEOUT, DEFAULT_DOWNLOAD_TIMEOUT);
    }
    if (!contains(KEY_DEBUG_MODE)) {
        setValue(KEY_DEBUG_MODE, false);
    }
    if (!contains(KEY_LOG_LEVEL)) {
        setValue(KEY_LOG_LEVEL, DEFAULT_LOG_LEVEL);
    }
    if (!contains(KEY_AUTO_CHECK_UPDATES)) {
        setValue(KEY_AUTO_CHECK_UPDATES, true);
    }
}

void ConfigManager::migrateOldSettings()
{
    // 这里可以添加从旧版本迁移设置的逻辑
    // 例如：重命名键、转换值格式等
}

QString ConfigManager::encryptPassword(const QString& password) const
{
    // 简单的Base64编码（实际应用中应使用更安全的加密）
    return password.toUtf8().toBase64();
}

QString ConfigManager::decryptPassword(const QString& encryptedPassword) const
{
    // 简单的Base64解码
    return QString::fromUtf8(QByteArray::fromBase64(encryptedPassword.toUtf8()));
}
