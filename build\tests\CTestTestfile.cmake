# CMake generated Testfile for 
# Source directory: C:/Users/<USER>/Desktop/Civitai_IMG/tests
# Build directory: C:/Users/<USER>/Desktop/Civitai_IMG/build/tests
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(test_civitai_image_info "C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/Debug/test_civitai_image_info.exe")
  set_tests_properties(test_civitai_image_info PROPERTIES  TIMEOUT "30" WORKING_DIRECTORY "C:/Users/<USER>/Desktop/Civitai_IMG/build" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/Civitai_IMG/tests/CMakeLists.txt;54;add_test;C:/Users/<USER>/Desktop/Civitai_IMG/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(test_civitai_image_info "C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/Release/test_civitai_image_info.exe")
  set_tests_properties(test_civitai_image_info PROPERTIES  TIMEOUT "30" WORKING_DIRECTORY "C:/Users/<USER>/Desktop/Civitai_IMG/build" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/Civitai_IMG/tests/CMakeLists.txt;54;add_test;C:/Users/<USER>/Desktop/Civitai_IMG/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(test_civitai_image_info "C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/MinSizeRel/test_civitai_image_info.exe")
  set_tests_properties(test_civitai_image_info PROPERTIES  TIMEOUT "30" WORKING_DIRECTORY "C:/Users/<USER>/Desktop/Civitai_IMG/build" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/Civitai_IMG/tests/CMakeLists.txt;54;add_test;C:/Users/<USER>/Desktop/Civitai_IMG/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(test_civitai_image_info "C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/RelWithDebInfo/test_civitai_image_info.exe")
  set_tests_properties(test_civitai_image_info PROPERTIES  TIMEOUT "30" WORKING_DIRECTORY "C:/Users/<USER>/Desktop/Civitai_IMG/build" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/Civitai_IMG/tests/CMakeLists.txt;54;add_test;C:/Users/<USER>/Desktop/Civitai_IMG/tests/CMakeLists.txt;0;")
else()
  add_test(test_civitai_image_info NOT_AVAILABLE)
endif()
