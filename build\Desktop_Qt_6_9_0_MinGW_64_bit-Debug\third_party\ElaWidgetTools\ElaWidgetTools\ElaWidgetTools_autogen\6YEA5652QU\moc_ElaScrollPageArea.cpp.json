{"classes": [{"className": "ElaScrollPageArea", "lineNumber": 8, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pBorderRadius", "notify": "pBorderRadiusChanged", "read": "getBorderRadius", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBorderRadius"}], "qualifiedClassName": "ElaScrollPageArea", "signals": [{"access": "public", "index": 0, "name": "pBorderRadiusChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "ElaScrollPageArea.h", "outputRevision": 69}