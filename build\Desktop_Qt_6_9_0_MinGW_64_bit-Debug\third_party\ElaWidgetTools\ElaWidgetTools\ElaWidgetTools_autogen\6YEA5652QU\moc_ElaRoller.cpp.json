{"classes": [{"className": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lineNumber": 8, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pBorderRadius", "notify": "pBorderRadiusChanged", "read": "getBorderRadius", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBorderRadius"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pItemList", "notify": "pItemListChanged", "read": "getItemList", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setItemList"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "pItemHeight", "notify": "pItemHeightChanged", "read": "getItemHeight", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setItemHeight"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "pMaxVisibleItems", "notify": "pMaxVisibleItemsChanged", "read": "getMaxVisibleItems", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMaxVisibleItems"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "pCurrentIndex", "notify": "pCurrentIndexChanged", "read": "getCurrentIndex", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setCurrentIndex"}], "qualifiedClassName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "signals": [{"access": "public", "index": 0, "name": "pBorderRadiusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pItemListChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "pItemHeightChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "pMaxVisibleItemsChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "pCurrentIndexChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "ElaRoller.h", "outputRevision": 69}