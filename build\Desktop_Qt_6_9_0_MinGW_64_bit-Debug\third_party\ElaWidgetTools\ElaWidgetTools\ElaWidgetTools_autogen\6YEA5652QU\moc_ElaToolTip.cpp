/****************************************************************************
** Meta object code from reading C++ file 'ElaToolTip.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaToolTip.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaToolTip.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN10ElaToolTipE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaToolTip::qt_create_metaobjectdata<qt_meta_tag_ZN10ElaToolTipE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaToolTip",
        "pBorderRadiusChanged",
        "",
        "pDisplayMsecChanged",
        "pShowDelayMsecChanged",
        "pHideDelayMsecChanged",
        "pToolTipChanged",
        "pCustomWidgetChanged",
        "pBorderRadius",
        "pDisplayMsec",
        "pShowDelayMsec",
        "pHideDelayMsec",
        "pToolTip",
        "pCustomWidget",
        "QWidget*"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pBorderRadiusChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pDisplayMsecChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pShowDelayMsecChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pHideDelayMsecChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pToolTipChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCustomWidgetChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pBorderRadius'
        QtMocHelpers::PropertyData<int>(8, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pDisplayMsec'
        QtMocHelpers::PropertyData<int>(9, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pShowDelayMsec'
        QtMocHelpers::PropertyData<int>(10, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pHideDelayMsec'
        QtMocHelpers::PropertyData<int>(11, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 3),
        // property 'pToolTip'
        QtMocHelpers::PropertyData<QString>(12, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 4),
        // property 'pCustomWidget'
        QtMocHelpers::PropertyData<QWidget*>(13, 0x80000000 | 14, QMC::DefaultPropertyFlags | QMC::Writable | QMC::EnumOrFlag, 5),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaToolTip, qt_meta_tag_ZN10ElaToolTipE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaToolTip::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10ElaToolTipE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10ElaToolTipE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN10ElaToolTipE_t>.metaTypes,
    nullptr
} };

void ElaToolTip::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaToolTip *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pBorderRadiusChanged(); break;
        case 1: _t->pDisplayMsecChanged(); break;
        case 2: _t->pShowDelayMsecChanged(); break;
        case 3: _t->pHideDelayMsecChanged(); break;
        case 4: _t->pToolTipChanged(); break;
        case 5: _t->pCustomWidgetChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaToolTip::*)()>(_a, &ElaToolTip::pBorderRadiusChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaToolTip::*)()>(_a, &ElaToolTip::pDisplayMsecChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaToolTip::*)()>(_a, &ElaToolTip::pShowDelayMsecChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaToolTip::*)()>(_a, &ElaToolTip::pHideDelayMsecChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaToolTip::*)()>(_a, &ElaToolTip::pToolTipChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaToolTip::*)()>(_a, &ElaToolTip::pCustomWidgetChanged, 5))
            return;
    }
    if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 5:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QWidget* >(); break;
        }
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = _t->getBorderRadius(); break;
        case 1: *reinterpret_cast<int*>(_v) = _t->getDisplayMsec(); break;
        case 2: *reinterpret_cast<int*>(_v) = _t->getShowDelayMsec(); break;
        case 3: *reinterpret_cast<int*>(_v) = _t->getHideDelayMsec(); break;
        case 4: *reinterpret_cast<QString*>(_v) = _t->getToolTip(); break;
        case 5: *reinterpret_cast<QWidget**>(_v) = _t->getCustomWidget(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setBorderRadius(*reinterpret_cast<int*>(_v)); break;
        case 1: _t->setDisplayMsec(*reinterpret_cast<int*>(_v)); break;
        case 2: _t->setShowDelayMsec(*reinterpret_cast<int*>(_v)); break;
        case 3: _t->setHideDelayMsec(*reinterpret_cast<int*>(_v)); break;
        case 4: _t->setToolTip(*reinterpret_cast<QString*>(_v)); break;
        case 5: _t->setCustomWidget(*reinterpret_cast<QWidget**>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaToolTip::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaToolTip::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10ElaToolTipE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ElaToolTip::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 6;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    }
    return _id;
}

// SIGNAL 0
void ElaToolTip::pBorderRadiusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaToolTip::pDisplayMsecChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaToolTip::pShowDelayMsecChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaToolTip::pHideDelayMsecChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaToolTip::pToolTipChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ElaToolTip::pCustomWidgetChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}
QT_WARNING_POP
