@echo off
echo Building Civitai Image Viewer...

REM Clean build directory
if exist build rmdir /s /q build
mkdir build
cd build

echo Looking for Qt installation...

REM Try to find Qt installation paths
set QT_PATHS=
if exist "C:\Qt\6.5.0\msvc2019_64\bin\qmake.exe" set QT_PATHS=C:\Qt\6.5.0\msvc2019_64
if exist "C:\Qt\6.4.0\msvc2019_64\bin\qmake.exe" set QT_PATHS=C:\Qt\6.4.0\msvc2019_64
if exist "C:\Qt\6.3.0\msvc2019_64\bin\qmake.exe" set QT_PATHS=C:\Qt\6.3.0\msvc2019_64
if exist "C:\Qt\6.2.0\msvc2019_64\bin\qmake.exe" set QT_PATHS=C:\Qt\6.2.0\msvc2019_64

if defined QT_PATHS (
    echo Found Qt at: %QT_PATHS%
    set CMAKE_PREFIX_PATH=%QT_PATHS%
) else (
    echo Qt not found in standard locations, trying without explicit path...
)

echo Configuring with CMake...
if defined CMAKE_PREFIX_PATH (
    cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_PREFIX_PATH=%CMAKE_PREFIX_PATH% -G "Visual Studio 17 2022" -A x64
) else (
    cmake .. -DCMAKE_BUILD_TYPE=Release -G "Visual Studio 17 2022" -A x64
)

if %ERRORLEVEL% neq 0 (
    echo Trying Visual Studio 2019...
    if defined CMAKE_PREFIX_PATH (
        cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_PREFIX_PATH=%CMAKE_PREFIX_PATH% -G "Visual Studio 16 2019" -A x64
    ) else (
        cmake .. -DCMAKE_BUILD_TYPE=Release -G "Visual Studio 16 2019" -A x64
    )
    
    if %ERRORLEVEL% neq 0 (
        echo Configuration failed!
        echo.
        echo Please check:
        echo 1. Qt 6.2+ is installed
        echo 2. Visual Studio 2019/2022 is installed
        echo 3. CMake can find Qt (add Qt to PATH)
        pause
        exit /b 1
    )
)

echo Configuration successful!
echo Building project...
cmake --build . --config Release --parallel

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build successful!
echo Looking for executable...
for /r . %%i in (*.exe) do (
    if "%%~ni"=="CivitaiImageViewer" (
        echo Found: %%i
        set EXECUTABLE=%%i
    )
)

if defined EXECUTABLE (
    echo.
    echo SUCCESS! Executable created at: %EXECUTABLE%
    echo.
    echo Next steps:
    echo 1. Get Civitai API key from: https://civitai.com/user/account
    echo 2. Run the program and configure API key
    echo 3. Start browsing AI artwork!
    echo.
    set /p choice="Run the program now? (y/n): "
    if /i "%choice%"=="y" (
        start "" "%EXECUTABLE%"
        echo Program started!
    )
) else (
    echo Executable not found, please check build directory
)

pause
