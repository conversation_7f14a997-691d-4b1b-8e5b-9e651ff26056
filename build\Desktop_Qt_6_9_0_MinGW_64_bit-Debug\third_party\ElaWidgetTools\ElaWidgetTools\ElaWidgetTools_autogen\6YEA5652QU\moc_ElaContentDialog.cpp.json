{"classes": [{"className": "ElaContentDialog", "lineNumber": 9, "object": true, "qualifiedClassName": "ElaContentDialog", "signals": [{"access": "public", "index": 0, "name": "leftButtonClicked", "returnType": "void"}, {"access": "public", "index": 1, "name": "middleButtonClicked", "returnType": "void"}, {"access": "public", "index": 2, "name": "right<PERSON><PERSON>onClicked", "returnType": "void"}], "slots": [{"access": "public", "index": 3, "name": "onLeftButtonClicked", "returnType": "void"}, {"access": "public", "index": 4, "name": "onMiddleButtonClicked", "returnType": "void"}, {"access": "public", "index": 5, "name": "onRightButtonClicked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDialog"}]}], "inputFile": "ElaContentDialog.h", "outputRevision": 69}