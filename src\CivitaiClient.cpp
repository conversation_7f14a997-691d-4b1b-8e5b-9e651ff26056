#include "CivitaiClient.h"
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QUrlQuery>
#include <QDebug>
#include <QDateTime>
#include <QThread>

// 静态常量定义
const QString CivitaiClient::API_BASE_URL = "https://civitai.com/api/v1";
const QString CivitaiClient::USER_AGENT = "CivitaiImageViewer/1.0.0";

// PaginationInfo 实现
PaginationInfo::PaginationInfo(const QJsonObject& metadataJson)
{
    if (metadataJson.isEmpty()) {
        return;
    }

    currentPage = metadataJson.value("currentPage").toInt(1);
    totalPages = metadataJson.value("totalPages").toInt(0);
    totalItems = metadataJson.value("totalItems").toInt(0);
    itemsPerPage = metadataJson.value("pageSize").toInt(100);
    nextCursor = metadataJson.value("nextCursor").toString();
    prevCursor = metadataJson.value("prevCursor").toString();
    hasNextPage = !nextCursor.isEmpty() || currentPage < totalPages;
    hasPrevPage = !prevCursor.isEmpty() || currentPage > 1;
}

QJsonObject PaginationInfo::toJson() const
{
    QJsonObject obj;
    obj["currentPage"] = currentPage;
    obj["totalPages"] = totalPages;
    obj["totalItems"] = totalItems;
    obj["pageSize"] = itemsPerPage;
    obj["nextCursor"] = nextCursor;
    obj["prevCursor"] = prevCursor;
    obj["hasNextPage"] = hasNextPage;
    obj["hasPrevPage"] = hasPrevPage;
    return obj;
}

// ApiRequestParams 实现
QUrlQuery ApiRequestParams::toUrlQuery() const
{
    QUrlQuery query;

    if (limit > 0) {
        query.addQueryItem("limit", QString::number(limit));
    }

    if (!cursor.isEmpty()) {
        query.addQueryItem("cursor", cursor);
    } else if (page > 0) {
        query.addQueryItem("page", QString::number(page));
    }

    if (!this->query.isEmpty()) {
        query.addQueryItem("query", this->query);
    }

    if (!sort.isEmpty()) {
        query.addQueryItem("sort", sort);
    }

    if (!period.isEmpty()) {
        query.addQueryItem("period", period);
    }

    if (!nsfw.isEmpty()) {
        query.addQueryItem("nsfw", nsfw);
    }

    if (postId > 0) {
        query.addQueryItem("postId", QString::number(postId));
    }

    if (modelId > 0) {
        query.addQueryItem("modelId", QString::number(modelId));
    }

    if (modelVersionId > 0) {
        query.addQueryItem("modelVersionId", QString::number(modelVersionId));
    }

    if (!username.isEmpty()) {
        query.addQueryItem("username", username);
    }

    return query;
}

ApiRequestParams ApiRequestParams::fromVariantMap(const QVariantMap& params)
{
    ApiRequestParams result;

    if (params.contains("limit")) {
        result.limit = params.value("limit").toInt();
    }
    if (params.contains("page")) {
        result.page = params.value("page").toInt();
    }
    if (params.contains("cursor")) {
        result.cursor = params.value("cursor").toString();
    }
    if (params.contains("query")) {
        result.query = params.value("query").toString();
    }
    if (params.contains("sort")) {
        result.sort = params.value("sort").toString();
    }
    if (params.contains("period")) {
        result.period = params.value("period").toString();
    }
    if (params.contains("nsfw")) {
        result.nsfw = params.value("nsfw").toString();
    }
    if (params.contains("postId")) {
        result.postId = params.value("postId").toInt();
    }
    if (params.contains("modelId")) {
        result.modelId = params.value("modelId").toInt();
    }
    if (params.contains("modelVersionId")) {
        result.modelVersionId = params.value("modelVersionId").toInt();
    }
    if (params.contains("username")) {
        result.username = params.value("username").toString();
    }

    return result;
}

// CivitaiClient 实现
CivitaiClient::CivitaiClient(QObject *parent)
    : QObject(parent)
    , m_networkManager(new QNetworkAccessManager(this))
    , m_rateLimitTimer(new QTimer(this))
{
    // 设置速率限制定时器
    m_rateLimitTimer->setSingleShot(true);
    connect(m_rateLimitTimer, &QTimer::timeout, this, &CivitaiClient::onRateLimitTimer);

    // 连接网络管理器信号
    connect(m_networkManager, &QNetworkAccessManager::finished,
            this, &CivitaiClient::onNetworkReply);
}

CivitaiClient::~CivitaiClient()
{
    cancelAllRequests();
}

void CivitaiClient::setApiKey(const QString& apiKey)
{
    m_apiKey = apiKey.trimmed();
}

void CivitaiClient::setRateLimit(int requestsPerSecond)
{
    m_rateLimit = qMax(1, requestsPerSecond);
}

void CivitaiClient::setRetryCount(int maxRetries)
{
    m_maxRetries = qMax(0, maxRetries);
}

void CivitaiClient::fetchImages(const ApiRequestParams& params)
{
    // 使用速率控制调度请求
    scheduleRequest([this, params]() {
        sendRequest(params);
    });
}

void CivitaiClient::fetchImages(const QVariantMap& params)
{
    ApiRequestParams apiParams = ApiRequestParams::fromVariantMap(params);
    fetchImages(apiParams);
}

void CivitaiClient::cancelAllRequests()
{
    // 取消所有待处理的网络请求
    for (QNetworkReply* reply : m_pendingReplies) {
        reply->abort();
        reply->deleteLater();
    }
    m_pendingReplies.clear();

    // 清空请求队列
    m_requestQueue.clear();

    // 清空重试相关数据
    m_retryCount.clear();
    m_requestParams.clear();
}

void CivitaiClient::onNetworkReply()
{
    QNetworkReply* reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) {
        return;
    }

    // 从待处理列表中移除
    m_pendingReplies.remove(reply);

    // 处理响应
    if (reply->error() == QNetworkReply::NoError) {
        handleApiResponse(reply);
    } else {
        handleNetworkError(reply);
    }

    // 清理
    reply->deleteLater();

    // 处理下一个请求
    if (!m_requestQueue.isEmpty()) {
        QTimer::singleShot(0, this, &CivitaiClient::processRequestQueue);
    }
}

void CivitaiClient::onRateLimitTimer()
{
    processRequestQueue();
}

void CivitaiClient::processRequestQueue()
{
    if (m_requestQueue.isEmpty()) {
        return;
    }

    // 检查速率限制
    int currentTime = QDateTime::currentMSecsSinceEpoch();
    int timeSinceLastRequest = currentTime - m_lastRequestTime;
    int minInterval = 1000 / m_rateLimit; // 毫秒

    if (timeSinceLastRequest < minInterval) {
        // 需要等待
        int waitTime = minInterval - timeSinceLastRequest;
        m_rateLimitTimer->start(waitTime);
        return;
    }

    // 执行下一个请求
    auto requestFunc = m_requestQueue.dequeue();
    m_lastRequestTime = currentTime;
    requestFunc();
}

QNetworkRequest CivitaiClient::createRequest(const QString& endpoint, const QUrlQuery& query) const
{
    QUrl url(API_BASE_URL + endpoint);
    if (!query.isEmpty()) {
        url.setQuery(query);
    }

    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::UserAgentHeader, USER_AGENT);
    request.setRawHeader("Accept", "application/json");

    // 添加认证头
    if (!m_apiKey.isEmpty()) {
        QString authHeader = QString("Bearer %1").arg(m_apiKey);
        request.setRawHeader("Authorization", authHeader.toUtf8());
    }

    return request;
}

void CivitaiClient::sendRequest(const ApiRequestParams& params)
{
    QUrlQuery query = params.toUrlQuery();
    QNetworkRequest request = createRequest("/images", query);

    QNetworkReply* reply = m_networkManager->get(request);
    m_pendingReplies.insert(reply);
    m_requestParams[reply] = params;
    m_retryCount[reply] = 0;

    qDebug() << "Sending request to:" << request.url().toString();
}

void CivitaiClient::handleApiResponse(QNetworkReply* reply)
{
    QByteArray data = reply->readAll();
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);

    if (parseError.error != QJsonParseError::NoError) {
        QString errorMsg = QString("JSON解析错误: %1").arg(parseError.errorString());
        emit imageFetchFailed(errorMsg, reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt(), "ParseError");
        return;
    }

    QJsonObject responseObj = doc.object();

    // 检查API错误
    if (responseObj.contains("error")) {
        QString errorMsg = responseObj.value("error").toString();
        emit imageFetchFailed(errorMsg, reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt(), "ApiError");
        return;
    }

    // 解析图片数据
    QJsonArray itemsArray = responseObj.value("items").toArray();
    QList<CivitaiImageInfo> images = parseImagesFromResponse(itemsArray);

    // 解析分页信息
    QJsonObject metadataObj = responseObj.value("metadata").toObject();
    PaginationInfo pagination = parsePaginationFromResponse(metadataObj);

    // 发送成功信号
    emit imagesReceived(images, pagination);

    qDebug() << QString("成功获取 %1 张图片").arg(images.size());
}

void CivitaiClient::handleNetworkError(QNetworkReply* reply)
{
    int httpStatus = reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();
    QString errorMsg = getErrorMessage(reply);
    QString errorType = getErrorType(httpStatus);

    qDebug() << QString("网络错误: %1 (HTTP %2)").arg(errorMsg).arg(httpStatus);

    // 检查是否应该重试
    if (shouldRetry(reply)) {
        retryRequest(reply);
        return;
    }

    // 特殊处理速率限制
    if (httpStatus == 429) {
        int retryAfter = reply->rawHeader("Retry-After").toInt();
        if (retryAfter > 0) {
            emit rateLimitWarning(retryAfter);
        }
    }

    // 发送错误信号
    emit imageFetchFailed(errorMsg, httpStatus, errorType);
}

void CivitaiClient::retryRequest(QNetworkReply* reply)
{
    if (!m_requestParams.contains(reply)) {
        return;
    }

    ApiRequestParams params = m_requestParams[reply];
    int currentRetryCount = m_retryCount[reply];

    // 清理当前请求的数据
    m_requestParams.remove(reply);
    m_retryCount.remove(reply);

    // 计算延迟时间（指数退避）
    int delay = getRetryDelay(currentRetryCount);

    qDebug() << QString("将在 %1 毫秒后重试请求 (第 %2 次重试)").arg(delay).arg(currentRetryCount + 1);

    // 调度重试
    QTimer::singleShot(delay, this, [this, params]() {
        scheduleRequest([this, params]() {
            sendRequest(params);
        });
    });
}

void CivitaiClient::scheduleRequest(std::function<void()> requestFunc)
{
    m_requestQueue.enqueue(requestFunc);

    if (m_requestQueue.size() == 1 && !m_rateLimitTimer->isActive()) {
        // 如果队列中只有一个请求且定时器未激活，立即处理
        QTimer::singleShot(0, this, &CivitaiClient::processRequestQueue);
    }
}

QList<CivitaiImageInfo> CivitaiClient::parseImagesFromResponse(const QJsonArray& imagesArray) const
{
    QList<CivitaiImageInfo> images;
    images.reserve(imagesArray.size());

    for (const QJsonValue& value : imagesArray) {
        if (value.isObject()) {
            QJsonObject imageObj = value.toObject();
            CivitaiImageInfo imageInfo(imageObj);
            if (imageInfo.isValid()) {
                images.append(imageInfo);
            }
        }
    }

    return images;
}

PaginationInfo CivitaiClient::parsePaginationFromResponse(const QJsonObject& metadataObject) const
{
    return PaginationInfo(metadataObject);
}

QString CivitaiClient::getErrorMessage(QNetworkReply* reply) const
{
    switch (reply->error()) {
    case QNetworkReply::NoError:
        return QString();
    case QNetworkReply::ConnectionRefusedError:
        return "连接被拒绝";
    case QNetworkReply::RemoteHostClosedError:
        return "远程主机关闭连接";
    case QNetworkReply::HostNotFoundError:
        return "主机未找到";
    case QNetworkReply::TimeoutError:
        return "请求超时";
    case QNetworkReply::OperationCanceledError:
        return "操作被取消";
    case QNetworkReply::SslHandshakeFailedError:
        return "SSL握手失败";
    case QNetworkReply::TemporaryNetworkFailureError:
        return "临时网络故障";
    case QNetworkReply::NetworkSessionFailedError:
        return "网络会话失败";
    case QNetworkReply::BackgroundRequestNotAllowedError:
        return "后台请求不被允许";
    case QNetworkReply::TooManyRedirectsError:
        return "重定向次数过多";
    case QNetworkReply::InsecureRedirectError:
        return "不安全的重定向";
    case QNetworkReply::ProxyConnectionRefusedError:
        return "代理连接被拒绝";
    case QNetworkReply::ProxyConnectionClosedError:
        return "代理连接关闭";
    case QNetworkReply::ProxyNotFoundError:
        return "代理未找到";
    case QNetworkReply::ProxyTimeoutError:
        return "代理超时";
    case QNetworkReply::ProxyAuthenticationRequiredError:
        return "代理需要认证";
    case QNetworkReply::ContentAccessDenied:
        return "内容访问被拒绝";
    case QNetworkReply::ContentOperationNotPermittedError:
        return "内容操作不被允许";
    case QNetworkReply::ContentNotFoundError:
        return "内容未找到";
    case QNetworkReply::AuthenticationRequiredError:
        return "需要认证";
    case QNetworkReply::ContentReSendError:
        return "内容重发错误";
    case QNetworkReply::ContentConflictError:
        return "内容冲突";
    case QNetworkReply::ContentGoneError:
        return "内容已不存在";
    case QNetworkReply::InternalServerError:
        return "服务器内部错误";
    case QNetworkReply::OperationNotImplementedError:
        return "操作未实现";
    case QNetworkReply::ServiceUnavailableError:
        return "服务不可用";
    case QNetworkReply::ProtocolUnknownError:
        return "未知协议错误";
    case QNetworkReply::ProtocolInvalidOperationError:
        return "协议操作无效";
    case QNetworkReply::UnknownNetworkError:
        return "未知网络错误";
    case QNetworkReply::UnknownProxyError:
        return "未知代理错误";
    case QNetworkReply::UnknownContentError:
        return "未知内容错误";
    case QNetworkReply::ProtocolFailure:
        return "协议失败";
    case QNetworkReply::UnknownServerError:
        return "未知服务器错误";
    default:
        return QString("网络错误: %1").arg(reply->errorString());
    }
}

QString CivitaiClient::getErrorType(int httpStatusCode) const
{
    if (httpStatusCode >= 200 && httpStatusCode < 300) {
        return "Success";
    } else if (httpStatusCode >= 400 && httpStatusCode < 500) {
        switch (httpStatusCode) {
        case 400: return "BadRequest";
        case 401: return "Unauthorized";
        case 403: return "Forbidden";
        case 404: return "NotFound";
        case 429: return "RateLimited";
        default: return "ClientError";
        }
    } else if (httpStatusCode >= 500 && httpStatusCode < 600) {
        return "ServerError";
    } else {
        return "UnknownError";
    }
}

bool CivitaiClient::shouldRetry(QNetworkReply* reply) const
{
    if (!m_retryCount.contains(reply)) {
        return false;
    }

    int currentRetryCount = m_retryCount[reply];
    if (currentRetryCount >= m_maxRetries) {
        return false;
    }

    // 检查错误类型是否适合重试
    QNetworkReply::NetworkError error = reply->error();
    int httpStatus = reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();

    // 网络层错误通常可以重试
    if (error == QNetworkReply::TimeoutError ||
        error == QNetworkReply::TemporaryNetworkFailureError ||
        error == QNetworkReply::NetworkSessionFailedError ||
        error == QNetworkReply::RemoteHostClosedError) {
        return true;
    }

    // HTTP 5xx 服务器错误可以重试
    if (httpStatus >= 500 && httpStatus < 600) {
        return true;
    }

    // HTTP 429 速率限制可以重试
    if (httpStatus == 429) {
        return true;
    }

    return false;
}

int CivitaiClient::getRetryDelay(int retryAttempt) const
{
    // 指数退避：基础延迟 * 2^重试次数
    int baseDelay = 1000; // 1秒
    int maxDelay = 30000;  // 最大30秒

    int delay = baseDelay * (1 << retryAttempt); // 2^retryAttempt
    return qMin(delay, maxDelay);
}
