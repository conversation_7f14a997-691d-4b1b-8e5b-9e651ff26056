#include "MetaDataProcessor.h"
#include <QJsonDocument>
#include <QJsonArray>
#include <QStandardItemModel>
#include <QStandardItem>
#include <QTextStream>
#include <QFile>
#include <QDebug>

// 静态常量定义
const QStringList MetaDataProcessor::PROMPT_KEYS = {"prompt", "positive", "positive_prompt"};
const QStringList MetaDataProcessor::NEGATIVE_PROMPT_KEYS = {"negative_prompt", "negative", "negativePrompt"};
const QStringList MetaDataProcessor::MODEL_KEYS = {"model", "checkpoint", "model_name", "sd_model_name"};
const QStringList MetaDataProcessor::SAMPLER_KEYS = {"sampler", "sampler_name", "sampling_method"};
const QStringList MetaDataProcessor::STEPS_KEYS = {"steps", "sampling_steps", "num_inference_steps"};
const QStringList MetaDataProcessor::CFG_SCALE_KEYS = {"cfg_scale", "guidance_scale", "cfg"};
const QStringList MetaDataProcessor::SEED_KEYS = {"seed", "random_seed"};
const QStringList MetaDataProcessor::WIDTH_KEYS = {"width", "w", "image_width"};
const QStringList MetaDataProcessor::HEIGHT_KEYS = {"height", "h", "image_height"};
const QStringList MetaDataProcessor::LORA_KEYS = {"lora", "loras", "lora_hashes"};
const QStringList MetaDataProcessor::EMBEDDING_KEYS = {"embeddings", "textual_inversion", "ti"};

const int MetaDataProcessor::MAX_TREE_DEPTH = 10;
const int MetaDataProcessor::MAX_ARRAY_ITEMS_DISPLAY = 100;
const int MetaDataProcessor::MAX_STRING_LENGTH_DISPLAY = 1000;

MetaDataProcessor::MetaDataProcessor(QObject *parent)
    : QObject(parent)
    , m_useColorCoding(true)
    , m_showParameterTypes(true)
    , m_maxTextLength(5000)
{
    // 初始化参数别名映射
    m_parameterAliases["prompt"] = PROMPT_KEYS;
    m_parameterAliases["negative_prompt"] = NEGATIVE_PROMPT_KEYS;
    m_parameterAliases["model"] = MODEL_KEYS;
    m_parameterAliases["sampler"] = SAMPLER_KEYS;
    m_parameterAliases["steps"] = STEPS_KEYS;
    m_parameterAliases["cfg_scale"] = CFG_SCALE_KEYS;
    m_parameterAliases["seed"] = SEED_KEYS;
    m_parameterAliases["width"] = WIDTH_KEYS;
    m_parameterAliases["height"] = HEIGHT_KEYS;
    m_parameterAliases["lora"] = LORA_KEYS;
    m_parameterAliases["embeddings"] = EMBEDDING_KEYS;

    // 重要参数列表
    m_importantParameters << "prompt" << "negative_prompt" << "model" << "sampler"
                         << "steps" << "cfg_scale" << "seed" << "width" << "height";

    // 已知参数列表
    m_knownParameters = m_importantParameters;
    m_knownParameters << "denoising_strength" << "clip_skip" << "vae" << "scheduler"
                     << "eta" << "ddim_eta" << "batch_size" << "batch_count";
}

MetaDataProcessor::~MetaDataProcessor()
{
}

QString MetaDataProcessor::formatMetaToJsonString(const QJsonObject& meta, bool indented) const
{
    if (meta.isEmpty()) {
        return QString();
    }

    QJsonDocument doc(meta);
    return doc.toJson(indented ? QJsonDocument::Indented : QJsonDocument::Compact);
}

QAbstractItemModel* MetaDataProcessor::createMetaTreeModel(const QJsonObject& meta, QObject* parent) const
{
    auto* model = new QStandardItemModel(parent);
    model->setHorizontalHeaderLabels({"参数名", "参数值", "类型"});

    if (meta.isEmpty()) {
        return model;
    }

    QStandardItem* rootItem = model->invisibleRootItem();

    // 首先添加重要参数
    for (const QString& paramName : m_importantParameters) {
        QVariant value = findParameterValue(meta, m_parameterAliases.value(paramName));
        if (!value.isNull()) {
            auto* keyItem = new QStandardItem(paramName);
            auto* valueItem = new QStandardItem(formatParameterValue(value));
            auto* typeItem = new QStandardItem(value.typeName());

            keyItem->setData(true, Qt::UserRole); // 标记为重要参数
            rootItem->appendRow({keyItem, valueItem, typeItem});
        }
    }

    // 然后添加其他参数
    for (auto it = meta.begin(); it != meta.end(); ++it) {
        QString key = it.key();

        // 跳过已经添加的重要参数
        bool isImportant = false;
        for (const QString& paramName : m_importantParameters) {
            if (m_parameterAliases.value(paramName).contains(key, Qt::CaseInsensitive)) {
                isImportant = true;
                break;
            }
        }

        if (!isImportant) {
            buildTreeFromJson(it.value(), rootItem, key);
        }
    }

    return model;
}

QVariantMap MetaDataProcessor::extractKeyParameters(const QJsonObject& meta) const
{
    QVariantMap result;

    result["prompt"] = extractPrompt(meta);
    result["negative_prompt"] = extractNegativePrompt(meta);
    result["model"] = extractModel(meta);
    result["sampler"] = extractSampler(meta);
    result["steps"] = extractSteps(meta);
    result["cfg_scale"] = extractCfgScale(meta);
    result["seed"] = extractSeed(meta);

    QSize imageSize = extractImageSize(meta);
    if (imageSize.isValid()) {
        result["width"] = imageSize.width();
        result["height"] = imageSize.height();
        result["size"] = QString("%1x%2").arg(imageSize.width()).arg(imageSize.height());
    }

    QStringList loras = extractLoraList(meta);
    if (!loras.isEmpty()) {
        result["loras"] = loras;
    }

    QStringList embeddings = extractEmbeddingList(meta);
    if (!embeddings.isEmpty()) {
        result["embeddings"] = embeddings;
    }

    return result;
}

QString MetaDataProcessor::formatMetaToReadableText(const QJsonObject& meta) const
{
    if (meta.isEmpty()) {
        return "无元数据";
    }

    QStringList lines;
    QVariantMap keyParams = extractKeyParameters(meta);

    // 格式化重要参数
    if (!keyParams["prompt"].toString().isEmpty()) {
        lines << QString("提示词: %1").arg(keyParams["prompt"].toString());
    }

    if (!keyParams["negative_prompt"].toString().isEmpty()) {
        lines << QString("负面提示词: %1").arg(keyParams["negative_prompt"].toString());
    }

    if (!keyParams["model"].toString().isEmpty()) {
        lines << QString("模型: %1").arg(keyParams["model"].toString());
    }

    if (!keyParams["sampler"].toString().isEmpty()) {
        lines << QString("采样器: %1").arg(keyParams["sampler"].toString());
    }

    if (keyParams["steps"].toInt() > 0) {
        lines << QString("步数: %1").arg(keyParams["steps"].toInt());
    }

    if (keyParams["cfg_scale"].toDouble() > 0) {
        lines << QString("CFG Scale: %1").arg(keyParams["cfg_scale"].toDouble());
    }

    if (keyParams["seed"].toInt() > 0) {
        lines << QString("种子: %1").arg(keyParams["seed"].toInt());
    }

    if (!keyParams["size"].toString().isEmpty()) {
        lines << QString("尺寸: %1").arg(keyParams["size"].toString());
    }

    if (keyParams.contains("loras")) {
        QStringList loras = keyParams["loras"].toStringList();
        if (!loras.isEmpty()) {
            lines << QString("LoRA: %1").arg(loras.join(", "));
        }
    }

    return lines.join("\n");
}

// 参数提取方法
QString MetaDataProcessor::extractPrompt(const QJsonObject& meta) const
{
    return findStringParameter(meta, PROMPT_KEYS);
}

QString MetaDataProcessor::extractNegativePrompt(const QJsonObject& meta) const
{
    return findStringParameter(meta, NEGATIVE_PROMPT_KEYS);
}

QString MetaDataProcessor::extractModel(const QJsonObject& meta) const
{
    return findStringParameter(meta, MODEL_KEYS);
}

QString MetaDataProcessor::extractSampler(const QJsonObject& meta) const
{
    return findStringParameter(meta, SAMPLER_KEYS);
}

int MetaDataProcessor::extractSteps(const QJsonObject& meta) const
{
    return findIntParameter(meta, STEPS_KEYS);
}

double MetaDataProcessor::extractCfgScale(const QJsonObject& meta) const
{
    return findDoubleParameter(meta, CFG_SCALE_KEYS);
}

int MetaDataProcessor::extractSeed(const QJsonObject& meta) const
{
    return findIntParameter(meta, SEED_KEYS);
}

QSize MetaDataProcessor::extractImageSize(const QJsonObject& meta) const
{
    int width = findIntParameter(meta, WIDTH_KEYS);
    int height = findIntParameter(meta, HEIGHT_KEYS);

    if (width > 0 && height > 0) {
        return QSize(width, height);
    }

    return QSize();
}

QStringList MetaDataProcessor::extractLoraList(const QJsonObject& meta) const
{
    QStringList result;

    for (const QString& key : LORA_KEYS) {
        if (meta.contains(key)) {
            QJsonValue value = meta.value(key);
            if (value.isArray()) {
                QJsonArray array = value.toArray();
                for (const QJsonValue& item : array) {
                    if (item.isString()) {
                        result << item.toString();
                    }
                }
            } else if (value.isString()) {
                QString loraString = value.toString();
                // 解析LoRA字符串格式，如 "<lora:name:weight>"
                result << loraString;
            }
        }
    }

    return result;
}

// 搜索和过滤方法
QStringList MetaDataProcessor::searchInMeta(const QJsonObject& meta, const QString& searchTerm, bool caseSensitive) const
{
    QStringList results;
    Qt::CaseSensitivity sensitivity = caseSensitive ? Qt::CaseSensitive : Qt::CaseInsensitive;

    QStringList allText = extractTextFromJson(QJsonValue(meta));

    for (const QString& text : allText) {
        if (text.contains(searchTerm, sensitivity)) {
            results << text;
        }
    }

    return results;
}

bool MetaDataProcessor::containsKeyword(const QJsonObject& meta, const QString& keyword, bool caseSensitive) const
{
    return !searchInMeta(meta, keyword, caseSensitive).isEmpty();
}

// 验证和清理方法
bool MetaDataProcessor::isValidMeta(const QJsonObject& meta) const
{
    if (meta.isEmpty()) {
        return false;
    }

    // 检查是否包含至少一个已知参数
    for (const QString& key : meta.keys()) {
        if (m_knownParameters.contains(key, Qt::CaseInsensitive)) {
            return true;
        }
    }

    return false;
}

QJsonObject MetaDataProcessor::cleanupMeta(const QJsonObject& meta) const
{
    QJsonObject cleaned;

    for (auto it = meta.begin(); it != meta.end(); ++it) {
        QString key = cleanupParameterName(it.key());
        QJsonValue value = it.value();

        // 清理字符串值
        if (value.isString()) {
            QString stringValue = value.toString().trimmed();
            if (!stringValue.isEmpty()) {
                cleaned[key] = stringValue;
            }
        } else {
            cleaned[key] = value;
        }
    }

    return cleaned;
}

QJsonObject MetaDataProcessor::normalizeMeta(const QJsonObject& meta) const
{
    QJsonObject normalized;

    // 标准化已知参数
    for (const QString& standardKey : m_importantParameters) {
        QStringList aliases = m_parameterAliases.value(standardKey);
        QVariant value = findParameterValue(meta, aliases);

        if (!value.isNull()) {
            normalized[standardKey] = QJsonValue::fromVariant(value);
        }
    }

    // 添加其他未标准化的参数
    for (auto it = meta.begin(); it != meta.end(); ++it) {
        QString key = it.key();
        bool isKnown = false;

        for (const QString& standardKey : m_importantParameters) {
            if (m_parameterAliases.value(standardKey).contains(key, Qt::CaseInsensitive)) {
                isKnown = true;
                break;
            }
        }

        if (!isKnown) {
            normalized[key] = it.value();
        }
    }

    return normalized;
}

// 导出功能
QString MetaDataProcessor::exportMetaToText(const QJsonObject& meta, const QString& format) const
{
    if (format == "readable") {
        return formatMetaToReadableText(meta);
    } else if (format == "json") {
        return formatMetaToJsonString(meta, true);
    } else if (format == "compact") {
        return formatMetaToJsonString(meta, false);
    }

    return formatMetaToReadableText(meta);
}

bool MetaDataProcessor::exportMetaToFile(const QJsonObject& meta, const QString& filePath, const QString& format) const
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return false;
    }

    QTextStream stream(&file);
    // Qt 6: setCodec removed, UTF-8 is default
    // stream.setCodec("UTF-8");

    QString content = exportMetaToText(meta, format);
    stream << content;

    return true;
}

// 私有辅助方法
void MetaDataProcessor::buildTreeFromJson(const QJsonValue& value, QStandardItem* parentItem, const QString& key) const
{
    auto* keyItem = new QStandardItem(key);
    auto* valueItem = new QStandardItem();
    auto* typeItem = new QStandardItem();

    switch (value.type()) {
    case QJsonValue::Object: {
        valueItem->setText("{...}");
        typeItem->setText("Object");
        parentItem->appendRow({keyItem, valueItem, typeItem});

        QJsonObject obj = value.toObject();
        for (auto it = obj.begin(); it != obj.end(); ++it) {
            buildTreeFromJson(it.value(), keyItem, it.key());
        }
        break;
    }
    case QJsonValue::Array: {
        QJsonArray arr = value.toArray();
        valueItem->setText(QString("[%1 items]").arg(arr.size()));
        typeItem->setText("Array");
        parentItem->appendRow({keyItem, valueItem, typeItem});

        int displayCount = qMin(arr.size(), MAX_ARRAY_ITEMS_DISPLAY);
        for (int i = 0; i < displayCount; ++i) {
            buildTreeFromJson(arr[i], keyItem, QString("[%1]").arg(i));
        }

        if (arr.size() > MAX_ARRAY_ITEMS_DISPLAY) {
            auto* moreItem = new QStandardItem(QString("... (%1 more items)").arg(arr.size() - MAX_ARRAY_ITEMS_DISPLAY));
            keyItem->appendRow({moreItem, new QStandardItem(), new QStandardItem()});
        }
        break;
    }
    case QJsonValue::String: {
        QString text = value.toString();
        if (text.length() > MAX_STRING_LENGTH_DISPLAY) {
            text = text.left(MAX_STRING_LENGTH_DISPLAY) + "...";
        }
        valueItem->setText(text);
        typeItem->setText("String");
        parentItem->appendRow({keyItem, valueItem, typeItem});
        break;
    }
    case QJsonValue::Double:
        valueItem->setText(QString::number(value.toDouble()));
        typeItem->setText("Number");
        parentItem->appendRow({keyItem, valueItem, typeItem});
        break;
    case QJsonValue::Bool:
        valueItem->setText(value.toBool() ? "true" : "false");
        typeItem->setText("Boolean");
        parentItem->appendRow({keyItem, valueItem, typeItem});
        break;
    case QJsonValue::Null:
        valueItem->setText("null");
        typeItem->setText("Null");
        parentItem->appendRow({keyItem, valueItem, typeItem});
        break;
    default:
        valueItem->setText(value.toVariant().toString());
        typeItem->setText("Unknown");
        parentItem->appendRow({keyItem, valueItem, typeItem});
        break;
    }
}

QStringList MetaDataProcessor::extractTextFromJson(const QJsonValue& value) const
{
    QStringList result;

    switch (value.type()) {
    case QJsonValue::Object: {
        QJsonObject obj = value.toObject();
        for (auto it = obj.begin(); it != obj.end(); ++it) {
            result << it.key();
            result << extractTextFromJson(it.value());
        }
        break;
    }
    case QJsonValue::Array: {
        QJsonArray arr = value.toArray();
        for (const QJsonValue& item : arr) {
            result << extractTextFromJson(item);
        }
        break;
    }
    case QJsonValue::String:
        result << value.toString();
        break;
    default:
        result << value.toVariant().toString();
        break;
    }

    return result;
}

QVariant MetaDataProcessor::findParameterValue(const QJsonObject& meta, const QStringList& possibleKeys) const
{
    for (const QString& key : possibleKeys) {
        for (auto it = meta.begin(); it != meta.end(); ++it) {
            if (it.key().compare(key, Qt::CaseInsensitive) == 0) {
                return it.value().toVariant();
            }
        }
    }

    return QVariant();
}

QString MetaDataProcessor::findStringParameter(const QJsonObject& meta, const QStringList& possibleKeys) const
{
    QVariant value = findParameterValue(meta, possibleKeys);
    return value.toString();
}

int MetaDataProcessor::findIntParameter(const QJsonObject& meta, const QStringList& possibleKeys, int defaultValue) const
{
    QVariant value = findParameterValue(meta, possibleKeys);
    bool ok;
    int result = value.toInt(&ok);
    return ok ? result : defaultValue;
}

double MetaDataProcessor::findDoubleParameter(const QJsonObject& meta, const QStringList& possibleKeys, double defaultValue) const
{
    QVariant value = findParameterValue(meta, possibleKeys);
    bool ok;
    double result = value.toDouble(&ok);
    return ok ? result : defaultValue;
}

QString MetaDataProcessor::cleanupParameterName(const QString& name) const
{
    QString cleaned = name.trimmed();
    // Qt 6: Use QRegularExpression instead of QRegExp
    cleaned.replace(QRegularExpression("[^a-zA-Z0-9_]"), "_");
    return cleaned;
}

QString MetaDataProcessor::formatParameterValue(const QVariant& value) const
{
    // Qt 6: Use typeId() instead of deprecated type()
    switch (value.typeId()) {
    case QVariant::String: {
        QString text = value.toString();
        if (text.length() > 100) {
            return text.left(100) + "...";
        }
        return text;
    }
    case QVariant::Double:
        return QString::number(value.toDouble(), 'g', 6);
    case QVariant::Int:
    case QVariant::LongLong:
        return QString::number(value.toLongLong());
    case QVariant::Bool:
        return value.toBool() ? "true" : "false";
    default:
        return value.toString();
    }
}

QVariantMap MetaDataProcessor::parseLoraString(const QString& loraString) const
{
    QVariantMap result;

    // 解析 "<lora:name:weight>" 格式
    if (loraString.startsWith("<lora:") && loraString.endsWith(">")) {
        QString content = loraString.mid(6, loraString.length() - 7); // 移除 "<lora:" 和 ">"
        QStringList parts = content.split(":");

        if (parts.size() >= 1) {
            result["name"] = parts[0];
        }
        if (parts.size() >= 2) {
            bool ok;
            double weight = parts[1].toDouble(&ok);
            if (ok) {
                result["weight"] = weight;
            }
        }
    } else {
        result["name"] = loraString;
    }

    return result;
}
