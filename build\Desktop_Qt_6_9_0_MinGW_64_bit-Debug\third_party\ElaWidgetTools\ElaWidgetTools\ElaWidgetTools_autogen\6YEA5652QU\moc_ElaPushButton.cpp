/****************************************************************************
** Meta object code from reading C++ file 'ElaPushButton.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaPushButton.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaPushButton.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN13ElaPushButtonE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaPushButton::qt_create_metaobjectdata<qt_meta_tag_ZN13ElaPushButtonE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaPushButton",
        "pBorderRadiusChanged",
        "",
        "pLightDefaultColorChanged",
        "pDarkDefaultColorChanged",
        "pLightHoverColorChanged",
        "pDarkHoverColorChanged",
        "pLightPressColorChanged",
        "pDarkPressColorChanged",
        "pBorderRadius",
        "pLightDefaultColor",
        "pDarkDefaultColor",
        "pLightHoverColor",
        "pDarkHoverColor",
        "pLightPressColor",
        "pDarkPressColor"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pBorderRadiusChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pLightDefaultColorChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pDarkDefaultColorChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pLightHoverColorChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pDarkHoverColorChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pLightPressColorChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pDarkPressColorChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pBorderRadius'
        QtMocHelpers::PropertyData<int>(9, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pLightDefaultColor'
        QtMocHelpers::PropertyData<QColor>(10, QMetaType::QColor, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pDarkDefaultColor'
        QtMocHelpers::PropertyData<QColor>(11, QMetaType::QColor, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pLightHoverColor'
        QtMocHelpers::PropertyData<QColor>(12, QMetaType::QColor, QMC::DefaultPropertyFlags | QMC::Writable, 3),
        // property 'pDarkHoverColor'
        QtMocHelpers::PropertyData<QColor>(13, QMetaType::QColor, QMC::DefaultPropertyFlags | QMC::Writable, 4),
        // property 'pLightPressColor'
        QtMocHelpers::PropertyData<QColor>(14, QMetaType::QColor, QMC::DefaultPropertyFlags | QMC::Writable, 5),
        // property 'pDarkPressColor'
        QtMocHelpers::PropertyData<QColor>(15, QMetaType::QColor, QMC::DefaultPropertyFlags | QMC::Writable, 6),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaPushButton, qt_meta_tag_ZN13ElaPushButtonE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaPushButton::staticMetaObject = { {
    QMetaObject::SuperData::link<QPushButton::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13ElaPushButtonE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13ElaPushButtonE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN13ElaPushButtonE_t>.metaTypes,
    nullptr
} };

void ElaPushButton::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaPushButton *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pBorderRadiusChanged(); break;
        case 1: _t->pLightDefaultColorChanged(); break;
        case 2: _t->pDarkDefaultColorChanged(); break;
        case 3: _t->pLightHoverColorChanged(); break;
        case 4: _t->pDarkHoverColorChanged(); break;
        case 5: _t->pLightPressColorChanged(); break;
        case 6: _t->pDarkPressColorChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaPushButton::*)()>(_a, &ElaPushButton::pBorderRadiusChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPushButton::*)()>(_a, &ElaPushButton::pLightDefaultColorChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPushButton::*)()>(_a, &ElaPushButton::pDarkDefaultColorChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPushButton::*)()>(_a, &ElaPushButton::pLightHoverColorChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPushButton::*)()>(_a, &ElaPushButton::pDarkHoverColorChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPushButton::*)()>(_a, &ElaPushButton::pLightPressColorChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPushButton::*)()>(_a, &ElaPushButton::pDarkPressColorChanged, 6))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = _t->getBorderRadius(); break;
        case 1: *reinterpret_cast<QColor*>(_v) = _t->getLightDefaultColor(); break;
        case 2: *reinterpret_cast<QColor*>(_v) = _t->getDarkDefaultColor(); break;
        case 3: *reinterpret_cast<QColor*>(_v) = _t->getLightHoverColor(); break;
        case 4: *reinterpret_cast<QColor*>(_v) = _t->getDarkHoverColor(); break;
        case 5: *reinterpret_cast<QColor*>(_v) = _t->getLightPressColor(); break;
        case 6: *reinterpret_cast<QColor*>(_v) = _t->getDarkPressColor(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setBorderRadius(*reinterpret_cast<int*>(_v)); break;
        case 1: _t->setLightDefaultColor(*reinterpret_cast<QColor*>(_v)); break;
        case 2: _t->setDarkDefaultColor(*reinterpret_cast<QColor*>(_v)); break;
        case 3: _t->setLightHoverColor(*reinterpret_cast<QColor*>(_v)); break;
        case 4: _t->setDarkHoverColor(*reinterpret_cast<QColor*>(_v)); break;
        case 5: _t->setLightPressColor(*reinterpret_cast<QColor*>(_v)); break;
        case 6: _t->setDarkPressColor(*reinterpret_cast<QColor*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaPushButton::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaPushButton::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13ElaPushButtonE_t>.strings))
        return static_cast<void*>(this);
    return QPushButton::qt_metacast(_clname);
}

int ElaPushButton::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QPushButton::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 7;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    }
    return _id;
}

// SIGNAL 0
void ElaPushButton::pBorderRadiusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaPushButton::pLightDefaultColorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaPushButton::pDarkDefaultColorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaPushButton::pLightHoverColorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaPushButton::pDarkHoverColorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ElaPushButton::pLightPressColorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void ElaPushButton::pDarkPressColorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}
QT_WARNING_POP
