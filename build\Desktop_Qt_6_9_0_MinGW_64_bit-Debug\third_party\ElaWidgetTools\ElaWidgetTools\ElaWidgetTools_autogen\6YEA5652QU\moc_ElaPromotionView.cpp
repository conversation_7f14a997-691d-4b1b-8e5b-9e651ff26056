/****************************************************************************
** Meta object code from reading C++ file 'ElaPromotionView.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaPromotionView.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaPromotionView.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN16ElaPromotionViewE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaPromotionView::qt_create_metaobjectdata<qt_meta_tag_ZN16ElaPromotionViewE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaPromotionView",
        "pCardExpandWidthChanged",
        "",
        "pCardCollapseWidthChanged",
        "pCurrentIndexChanged",
        "pIsAutoScrollChanged",
        "pAutoScrollIntervalChanged",
        "pCardExpandWidth",
        "pCardCollapseWidth",
        "pCurrentIndex",
        "pIsAutoScroll",
        "pAutoScrollInterval"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pCardExpandWidthChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCardCollapseWidthChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCurrentIndexChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pIsAutoScrollChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pAutoScrollIntervalChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pCardExpandWidth'
        QtMocHelpers::PropertyData<int>(7, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pCardCollapseWidth'
        QtMocHelpers::PropertyData<int>(8, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pCurrentIndex'
        QtMocHelpers::PropertyData<int>(9, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pIsAutoScroll'
        QtMocHelpers::PropertyData<bool>(10, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 3),
        // property 'pAutoScrollInterval'
        QtMocHelpers::PropertyData<int>(11, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 4),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaPromotionView, qt_meta_tag_ZN16ElaPromotionViewE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaPromotionView::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16ElaPromotionViewE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16ElaPromotionViewE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN16ElaPromotionViewE_t>.metaTypes,
    nullptr
} };

void ElaPromotionView::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaPromotionView *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pCardExpandWidthChanged(); break;
        case 1: _t->pCardCollapseWidthChanged(); break;
        case 2: _t->pCurrentIndexChanged(); break;
        case 3: _t->pIsAutoScrollChanged(); break;
        case 4: _t->pAutoScrollIntervalChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionView::*)()>(_a, &ElaPromotionView::pCardExpandWidthChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionView::*)()>(_a, &ElaPromotionView::pCardCollapseWidthChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionView::*)()>(_a, &ElaPromotionView::pCurrentIndexChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionView::*)()>(_a, &ElaPromotionView::pIsAutoScrollChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaPromotionView::*)()>(_a, &ElaPromotionView::pAutoScrollIntervalChanged, 4))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = _t->getCardExpandWidth(); break;
        case 1: *reinterpret_cast<int*>(_v) = _t->getCardCollapseWidth(); break;
        case 2: *reinterpret_cast<int*>(_v) = _t->getCurrentIndex(); break;
        case 3: *reinterpret_cast<bool*>(_v) = _t->getIsAutoScroll(); break;
        case 4: *reinterpret_cast<int*>(_v) = _t->getAutoScrollInterval(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setCardExpandWidth(*reinterpret_cast<int*>(_v)); break;
        case 1: _t->setCardCollapseWidth(*reinterpret_cast<int*>(_v)); break;
        case 2: _t->setCurrentIndex(*reinterpret_cast<int*>(_v)); break;
        case 3: _t->setIsAutoScroll(*reinterpret_cast<bool*>(_v)); break;
        case 4: _t->setAutoScrollInterval(*reinterpret_cast<int*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaPromotionView::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaPromotionView::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16ElaPromotionViewE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ElaPromotionView::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 5;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    }
    return _id;
}

// SIGNAL 0
void ElaPromotionView::pCardExpandWidthChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaPromotionView::pCardCollapseWidthChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaPromotionView::pCurrentIndexChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaPromotionView::pIsAutoScrollChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaPromotionView::pAutoScrollIntervalChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}
QT_WARNING_POP
