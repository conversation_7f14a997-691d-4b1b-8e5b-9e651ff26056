{"classes": [{"className": "ElaLineEdit", "lineNumber": 9, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pBorderRadius", "notify": "pBorderRadiusChanged", "read": "getBorderRadius", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBorderRadius"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pIsClearButtonEnable", "notify": "pIsClearButtonEnableChanged", "read": "getIsClearButtonEnable", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setIsClearButtonEnable"}], "qualifiedClassName": "ElaLineEdit", "signals": [{"access": "public", "index": 0, "name": "pBorderRadiusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pIsClearButtonEnableChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 2, "name": "focusIn", "returnType": "void"}, {"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 3, "name": "focusOut", "returnType": "void"}, {"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 4, "name": "wmFocusOut", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QLineEdit"}]}], "inputFile": "ElaLineEdit.h", "outputRevision": 69}