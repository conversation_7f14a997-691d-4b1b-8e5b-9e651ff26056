@echo off
echo Building Civitai Image Viewer...

set QT_DIR=D:/Program/Qt/6.9.0/mingw_64
set CMAKE_PREFIX_PATH=%QT_DIR%
set PATH=%QT_DIR%\bin;%PATH%

echo Qt Directory: %QT_DIR%
echo Checking tools...

where gcc
where mingw32-make
where cmake

if exist build rmdir /s /q build
mkdir build
cd build

echo Configuring...
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_PREFIX_PATH=%CMAKE_PREFIX_PATH% -DCMAKE_MAKE_PROGRAM=mingw32-make -G "MinGW Makefiles"

if %ERRORLEVEL% neq 0 (
    echo Config failed, trying alternative...
    cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_PREFIX_PATH=%CMAKE_PREFIX_PATH%
    
    if %ERRORLEVEL% neq 0 (
        echo Configuration failed!
        pause
        exit /b 1
    )
)

echo Building...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Success! Looking for executable...
dir /s /b *.exe

pause
