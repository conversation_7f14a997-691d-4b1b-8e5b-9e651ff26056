{"artifacts": [{"path": "tests/test_civitai_image_info.exe"}, {"path": "tests/test_civitai_image_info.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "include_directories"], "files": ["tests/CMakeLists.txt", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6/Qt6Config.cmake", "CMakeLists.txt", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "D:/Program/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 32, "parent": 0}, {"command": 1, "file": 0, "line": 35, "parent": 0}, {"file": 4}, {"command": 4, "file": 4, "line": 15, "parent": 3}, {"file": 3, "parent": 4}, {"command": 4, "file": 3, "line": 212, "parent": 5}, {"file": 2, "parent": 6}, {"command": 3, "file": 2, "line": 55, "parent": 7}, {"file": 1, "parent": 8}, {"command": 2, "file": 1, "line": 61, "parent": 9}, {"command": 3, "file": 2, "line": 43, "parent": 7}, {"file": 9, "parent": 11}, {"command": 6, "file": 9, "line": 45, "parent": 12}, {"command": 5, "file": 8, "line": 137, "parent": 13}, {"command": 4, "file": 7, "line": 76, "parent": 14}, {"file": 6, "parent": 15}, {"command": 3, "file": 6, "line": 55, "parent": 16}, {"file": 5, "parent": 17}, {"command": 2, "file": 5, "line": 61, "parent": 18}, {"command": 4, "file": 3, "line": 212, "parent": 5}, {"file": 11, "parent": 20}, {"command": 3, "file": 11, "line": 55, "parent": 21}, {"file": 10, "parent": 22}, {"command": 2, "file": 10, "line": 61, "parent": 23}, {"command": 4, "file": 3, "line": 212, "parent": 5}, {"file": 13, "parent": 25}, {"command": 3, "file": 13, "line": 57, "parent": 26}, {"file": 12, "parent": 27}, {"command": 2, "file": 12, "line": 61, "parent": 28}, {"command": 7, "file": 0, "line": 10, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -g -std=gnu++17 -fdiagnostics-color=always"}], "defines": [{"backtrace": 2, "define": "MINGW_HAS_SECURE_API=1"}, {"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_NETWORK_LIB"}, {"backtrace": 2, "define": "QT_TESTCASE_BUILDDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/tests\""}, {"backtrace": 2, "define": "QT_TESTCASE_SOURCEDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/tests\""}, {"backtrace": 2, "define": "QT_TESTLIB_LIB"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}, {"backtrace": 2, "define": "UNICODE"}, {"backtrace": 2, "define": "WIN32"}, {"backtrace": 2, "define": "WIN64"}, {"backtrace": 2, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 2, "define": "_UNICODE"}, {"backtrace": 2, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/tests/test_civitai_image_info_autogen/include"}, {"backtrace": 30, "path": "C:/Users/<USER>/Desktop/Civitai_IMG/src"}, {"backtrace": 2, "isSystem": true, "path": "D:/Program/Qt/6.9.0/mingw_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "D:/Program/Qt/6.9.0/mingw_64/include"}, {"backtrace": 2, "isSystem": true, "path": "D:/Program/Qt/6.9.0/mingw_64/mkspecs/win32-g++"}, {"backtrace": 2, "isSystem": true, "path": "D:/Program/Qt/6.9.0/mingw_64/include/QtWidgets"}, {"backtrace": 2, "isSystem": true, "path": "D:/Program/Qt/6.9.0/mingw_64/include/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "D:/Program/Qt/6.9.0/mingw_64/include/QtNetwork"}, {"backtrace": 2, "isSystem": true, "path": "D:/Program/Qt/6.9.0/mingw_64/include/QtTest"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6]}], "dependencies": [{"id": "test_civitai_image_info_autogen_timestamp_deps::@a44f0ac069e85531cdee"}, {"backtrace": 0, "id": "test_civitai_image_info_autogen::@a44f0ac069e85531cdee"}], "id": "test_civitai_image_info::@a44f0ac069e85531cdee", "link": {"commandFragments": [{"fragment": "-DQT_QML_DEBUG -g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 2, "fragment": "D:\\Program\\Qt\\6.9.0\\mingw_64\\lib\\libQt6Widgets.a", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Program\\Qt\\6.9.0\\mingw_64\\lib\\libQt6Network.a", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Program\\Qt\\6.9.0\\mingw_64\\lib\\libQt6Test.a", "role": "libraries"}, {"backtrace": 10, "fragment": "D:\\Program\\Qt\\6.9.0\\mingw_64\\lib\\libQt6Gui.a", "role": "libraries"}, {"backtrace": 19, "fragment": "-ld3d11", "role": "libraries"}, {"backtrace": 19, "fragment": "-ldxgi", "role": "libraries"}, {"backtrace": 19, "fragment": "-ldxguid", "role": "libraries"}, {"backtrace": 19, "fragment": "-ld3d12", "role": "libraries"}, {"backtrace": 24, "fragment": "-lws2_32", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Program\\Qt\\6.9.0\\mingw_64\\lib\\libQt6Core.a", "role": "libraries"}, {"backtrace": 29, "fragment": "-lmpr", "role": "libraries"}, {"backtrace": 29, "fragment": "-l<PERSON><PERSON>v", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "test_civitai_image_info", "nameOnDisk": "test_civitai_image_info.exe", "paths": {"build": "tests", "source": "tests"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6]}, {"name": "", "sourceIndexes": [7]}, {"name": "CMake Rules", "sourceIndexes": [8]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/tests/test_civitai_image_info_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "tests/test_civitai_image_info.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/CivitaiImageInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/CivitaiClient.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ImageManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ConfigManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/MetaDataProcessor.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/tests/test_civitai_image_info_autogen/timestamp", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/tests/test_civitai_image_info_autogen/timestamp.rule", "sourceGroupIndex": 2}], "type": "EXECUTABLE"}