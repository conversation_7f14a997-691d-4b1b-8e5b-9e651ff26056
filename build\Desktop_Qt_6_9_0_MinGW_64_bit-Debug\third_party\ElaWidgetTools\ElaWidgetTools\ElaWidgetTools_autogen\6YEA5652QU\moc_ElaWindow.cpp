/****************************************************************************
** Meta object code from reading C++ file 'ElaWindow.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaWindow.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaWindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN9ElaWindowE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaWindow::qt_create_metaobjectdata<qt_meta_tag_ZN9ElaWindowE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaWindow",
        "pIsStayTopChanged",
        "",
        "pIsFixedSizeChanged",
        "pIsDefaultClosedChanged",
        "pAppBarHeightChanged",
        "pCustomWidgetMaximumWidthChanged",
        "pThemeChangeTimeChanged",
        "pIsCentralStackedWidgetTransparentChanged",
        "pIsAllowPageOpenInNewWindowChanged",
        "pNavigationBarDisplayModeChanged",
        "userInfoCardClicked",
        "closeButtonClicked",
        "navigationNodeClicked",
        "ElaNavigationType::NavigationNodeType",
        "nodeType",
        "nodeKey",
        "customWidgetChanged",
        "pageOpenInNewWindow",
        "pIsStayTop",
        "pIsFixedSize",
        "pIsDefaultClosed",
        "pAppBarHeight",
        "pCustomWidgetMaximumWidth",
        "pThemeChangeTime",
        "pIsCentralStackedWidgetTransparent",
        "pIsAllowPageOpenInNewWindow",
        "pNavigationBarDisplayMode",
        "ElaNavigationType::NavigationDisplayMode"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pIsStayTopChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pIsFixedSizeChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pIsDefaultClosedChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pAppBarHeightChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCustomWidgetMaximumWidthChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pThemeChangeTimeChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pIsCentralStackedWidgetTransparentChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pIsAllowPageOpenInNewWindowChanged'
        QtMocHelpers::SignalData<void()>(9, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pNavigationBarDisplayModeChanged'
        QtMocHelpers::SignalData<void()>(10, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'userInfoCardClicked'
        QtMocHelpers::SignalData<void()>(11, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'closeButtonClicked'
        QtMocHelpers::SignalData<void()>(12, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'navigationNodeClicked'
        QtMocHelpers::SignalData<void(ElaNavigationType::NavigationNodeType, QString)>(13, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 14, 15 }, { QMetaType::QString, 16 },
        }}),
        // Signal 'customWidgetChanged'
        QtMocHelpers::SignalData<void()>(17, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pageOpenInNewWindow'
        QtMocHelpers::SignalData<void(QString)>(18, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 16 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pIsStayTop'
        QtMocHelpers::PropertyData<bool>(19, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pIsFixedSize'
        QtMocHelpers::PropertyData<bool>(20, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pIsDefaultClosed'
        QtMocHelpers::PropertyData<bool>(21, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pAppBarHeight'
        QtMocHelpers::PropertyData<int>(22, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 3),
        // property 'pCustomWidgetMaximumWidth'
        QtMocHelpers::PropertyData<int>(23, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 4),
        // property 'pThemeChangeTime'
        QtMocHelpers::PropertyData<int>(24, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 5),
        // property 'pIsCentralStackedWidgetTransparent'
        QtMocHelpers::PropertyData<bool>(25, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 6),
        // property 'pIsAllowPageOpenInNewWindow'
        QtMocHelpers::PropertyData<bool>(26, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 7),
        // property 'pNavigationBarDisplayMode'
        QtMocHelpers::PropertyData<ElaNavigationType::NavigationDisplayMode>(27, 0x80000000 | 28, QMC::DefaultPropertyFlags | QMC::Writable | QMC::EnumOrFlag, 8),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaWindow, qt_meta_tag_ZN9ElaWindowE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT static const QMetaObject::SuperData qt_meta_extradata_ZN9ElaWindowE[] = {
    QMetaObject::SuperData::link<ElaNavigationType::staticMetaObject>(),
    nullptr
};

Q_CONSTINIT const QMetaObject ElaWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9ElaWindowE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9ElaWindowE_t>.data,
    qt_static_metacall,
    qt_meta_extradata_ZN9ElaWindowE,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN9ElaWindowE_t>.metaTypes,
    nullptr
} };

void ElaWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaWindow *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pIsStayTopChanged(); break;
        case 1: _t->pIsFixedSizeChanged(); break;
        case 2: _t->pIsDefaultClosedChanged(); break;
        case 3: _t->pAppBarHeightChanged(); break;
        case 4: _t->pCustomWidgetMaximumWidthChanged(); break;
        case 5: _t->pThemeChangeTimeChanged(); break;
        case 6: _t->pIsCentralStackedWidgetTransparentChanged(); break;
        case 7: _t->pIsAllowPageOpenInNewWindowChanged(); break;
        case 8: _t->pNavigationBarDisplayModeChanged(); break;
        case 9: _t->userInfoCardClicked(); break;
        case 10: _t->closeButtonClicked(); break;
        case 11: _t->navigationNodeClicked((*reinterpret_cast< std::add_pointer_t<ElaNavigationType::NavigationNodeType>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 12: _t->customWidgetChanged(); break;
        case 13: _t->pageOpenInNewWindow((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaWindow::*)()>(_a, &ElaWindow::pIsStayTopChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWindow::*)()>(_a, &ElaWindow::pIsFixedSizeChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWindow::*)()>(_a, &ElaWindow::pIsDefaultClosedChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWindow::*)()>(_a, &ElaWindow::pAppBarHeightChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWindow::*)()>(_a, &ElaWindow::pCustomWidgetMaximumWidthChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWindow::*)()>(_a, &ElaWindow::pThemeChangeTimeChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWindow::*)()>(_a, &ElaWindow::pIsCentralStackedWidgetTransparentChanged, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWindow::*)()>(_a, &ElaWindow::pIsAllowPageOpenInNewWindowChanged, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWindow::*)()>(_a, &ElaWindow::pNavigationBarDisplayModeChanged, 8))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWindow::*)()>(_a, &ElaWindow::userInfoCardClicked, 9))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWindow::*)()>(_a, &ElaWindow::closeButtonClicked, 10))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWindow::*)(ElaNavigationType::NavigationNodeType , QString )>(_a, &ElaWindow::navigationNodeClicked, 11))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWindow::*)()>(_a, &ElaWindow::customWidgetChanged, 12))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWindow::*)(QString )>(_a, &ElaWindow::pageOpenInNewWindow, 13))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<bool*>(_v) = _t->getIsStayTop(); break;
        case 1: *reinterpret_cast<bool*>(_v) = _t->getIsFixedSize(); break;
        case 2: *reinterpret_cast<bool*>(_v) = _t->getIsDefaultClosed(); break;
        case 3: *reinterpret_cast<int*>(_v) = _t->getAppBarHeight(); break;
        case 4: *reinterpret_cast<int*>(_v) = _t->getCustomWidgetMaximumWidth(); break;
        case 5: *reinterpret_cast<int*>(_v) = _t->getThemeChangeTime(); break;
        case 6: *reinterpret_cast<bool*>(_v) = _t->getIsCentralStackedWidgetTransparent(); break;
        case 7: *reinterpret_cast<bool*>(_v) = _t->getIsAllowPageOpenInNewWindow(); break;
        case 8: *reinterpret_cast<ElaNavigationType::NavigationDisplayMode*>(_v) = _t->getNavigationBarDisplayMode(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setIsStayTop(*reinterpret_cast<bool*>(_v)); break;
        case 1: _t->setIsFixedSize(*reinterpret_cast<bool*>(_v)); break;
        case 2: _t->setIsDefaultClosed(*reinterpret_cast<bool*>(_v)); break;
        case 3: _t->setAppBarHeight(*reinterpret_cast<int*>(_v)); break;
        case 4: _t->setCustomWidgetMaximumWidth(*reinterpret_cast<int*>(_v)); break;
        case 5: _t->setThemeChangeTime(*reinterpret_cast<int*>(_v)); break;
        case 6: _t->setIsCentralStackedWidgetTransparent(*reinterpret_cast<bool*>(_v)); break;
        case 7: _t->setIsAllowPageOpenInNewWindow(*reinterpret_cast<bool*>(_v)); break;
        case 8: _t->setNavigationBarDisplayMode(*reinterpret_cast<ElaNavigationType::NavigationDisplayMode*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9ElaWindowE_t>.strings))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int ElaWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 14)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 14;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    }
    return _id;
}

// SIGNAL 0
void ElaWindow::pIsStayTopChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaWindow::pIsFixedSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaWindow::pIsDefaultClosedChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaWindow::pAppBarHeightChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaWindow::pCustomWidgetMaximumWidthChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ElaWindow::pThemeChangeTimeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void ElaWindow::pIsCentralStackedWidgetTransparentChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void ElaWindow::pIsAllowPageOpenInNewWindowChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}

// SIGNAL 8
void ElaWindow::pNavigationBarDisplayModeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 8, nullptr);
}

// SIGNAL 9
void ElaWindow::userInfoCardClicked()
{
    QMetaObject::activate(this, &staticMetaObject, 9, nullptr);
}

// SIGNAL 10
void ElaWindow::closeButtonClicked()
{
    QMetaObject::activate(this, &staticMetaObject, 10, nullptr);
}

// SIGNAL 11
void ElaWindow::navigationNodeClicked(ElaNavigationType::NavigationNodeType _t1, QString _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 11, nullptr, _t1, _t2);
}

// SIGNAL 12
void ElaWindow::customWidgetChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 12, nullptr);
}

// SIGNAL 13
void ElaWindow::pageOpenInNewWindow(QString _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 13, nullptr, _t1);
}
QT_WARNING_POP
