{"classes": [{"className": "Ela<PERSON>ush<PERSON><PERSON>on", "lineNumber": 8, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pBorderRadius", "notify": "pBorderRadiusChanged", "read": "getBorderRadius", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBorderRadius"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pLightDefaultColor", "notify": "pLightDefaultColorChanged", "read": "getLightDefaultColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setLightDefaultColor"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "pDarkDefaultColor", "notify": "pDarkDefaultColorChanged", "read": "getDarkDefaultColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setDarkDefaultColor"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "pLightHoverColor", "notify": "pLightHoverColorChanged", "read": "getLightHoverColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setLightHoverColor"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "pDarkHoverColor", "notify": "pDarkHoverColorChanged", "read": "getDarkHoverColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setDarkHoverColor"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "pLightPressColor", "notify": "pLightPressColorChanged", "read": "getLightPressColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setLightPressColor"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "pDarkPressColor", "notify": "pDarkPressColorChanged", "read": "getDarkPressColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setDarkPressColor"}], "qualifiedClassName": "Ela<PERSON>ush<PERSON><PERSON>on", "signals": [{"access": "public", "index": 0, "name": "pBorderRadiusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pLightDefaultColorChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "pDarkDefaultColorChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "pLightHoverColorChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "pDarkHoverColorChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "pLightPressColorChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "pDarkPressColorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPushButton"}]}], "inputFile": "ElaPushButton.h", "outputRevision": 69}