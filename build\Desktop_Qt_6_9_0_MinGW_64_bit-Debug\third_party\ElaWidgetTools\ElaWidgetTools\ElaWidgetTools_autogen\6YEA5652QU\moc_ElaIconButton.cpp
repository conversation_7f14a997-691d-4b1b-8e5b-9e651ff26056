/****************************************************************************
** Meta object code from reading C++ file 'ElaIconButton.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaIconButton.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaIconButton.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN13ElaIconButtonE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaIconButton::qt_create_metaobjectdata<qt_meta_tag_ZN13ElaIconButtonE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaIconButton",
        "pBorderRadiusChanged",
        "",
        "pOpacityChanged",
        "pLightHoverColorChanged",
        "pDarkHoverColorChanged",
        "pLightIconColorChanged",
        "pDarkIconColorChanged",
        "pLightHoverIconColorChanged",
        "pDarkHoverIconColorChanged",
        "pIsSelectedChanged",
        "pBorderRadius",
        "pOpacity",
        "pLightHoverColor",
        "pDarkHoverColor",
        "pLightIconColor",
        "pDarkIconColor",
        "pLightHoverIconColor",
        "pDarkHoverIconColor",
        "pIsSelected"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pBorderRadiusChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pOpacityChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pLightHoverColorChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pDarkHoverColorChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pLightIconColorChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pDarkIconColorChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pLightHoverIconColorChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pDarkHoverIconColorChanged'
        QtMocHelpers::SignalData<void()>(9, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pIsSelectedChanged'
        QtMocHelpers::SignalData<void()>(10, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pBorderRadius'
        QtMocHelpers::PropertyData<int>(11, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pOpacity'
        QtMocHelpers::PropertyData<qreal>(12, QMetaType::QReal, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pLightHoverColor'
        QtMocHelpers::PropertyData<QColor>(13, QMetaType::QColor, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pDarkHoverColor'
        QtMocHelpers::PropertyData<QColor>(14, QMetaType::QColor, QMC::DefaultPropertyFlags | QMC::Writable, 3),
        // property 'pLightIconColor'
        QtMocHelpers::PropertyData<QColor>(15, QMetaType::QColor, QMC::DefaultPropertyFlags | QMC::Writable, 4),
        // property 'pDarkIconColor'
        QtMocHelpers::PropertyData<QColor>(16, QMetaType::QColor, QMC::DefaultPropertyFlags | QMC::Writable, 5),
        // property 'pLightHoverIconColor'
        QtMocHelpers::PropertyData<QColor>(17, QMetaType::QColor, QMC::DefaultPropertyFlags | QMC::Writable, 6),
        // property 'pDarkHoverIconColor'
        QtMocHelpers::PropertyData<QColor>(18, QMetaType::QColor, QMC::DefaultPropertyFlags | QMC::Writable, 7),
        // property 'pIsSelected'
        QtMocHelpers::PropertyData<bool>(19, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 8),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaIconButton, qt_meta_tag_ZN13ElaIconButtonE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaIconButton::staticMetaObject = { {
    QMetaObject::SuperData::link<QPushButton::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13ElaIconButtonE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13ElaIconButtonE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN13ElaIconButtonE_t>.metaTypes,
    nullptr
} };

void ElaIconButton::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaIconButton *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pBorderRadiusChanged(); break;
        case 1: _t->pOpacityChanged(); break;
        case 2: _t->pLightHoverColorChanged(); break;
        case 3: _t->pDarkHoverColorChanged(); break;
        case 4: _t->pLightIconColorChanged(); break;
        case 5: _t->pDarkIconColorChanged(); break;
        case 6: _t->pLightHoverIconColorChanged(); break;
        case 7: _t->pDarkHoverIconColorChanged(); break;
        case 8: _t->pIsSelectedChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaIconButton::*)()>(_a, &ElaIconButton::pBorderRadiusChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaIconButton::*)()>(_a, &ElaIconButton::pOpacityChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaIconButton::*)()>(_a, &ElaIconButton::pLightHoverColorChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaIconButton::*)()>(_a, &ElaIconButton::pDarkHoverColorChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaIconButton::*)()>(_a, &ElaIconButton::pLightIconColorChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaIconButton::*)()>(_a, &ElaIconButton::pDarkIconColorChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaIconButton::*)()>(_a, &ElaIconButton::pLightHoverIconColorChanged, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaIconButton::*)()>(_a, &ElaIconButton::pDarkHoverIconColorChanged, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaIconButton::*)()>(_a, &ElaIconButton::pIsSelectedChanged, 8))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = _t->getBorderRadius(); break;
        case 1: *reinterpret_cast<qreal*>(_v) = _t->getOpacity(); break;
        case 2: *reinterpret_cast<QColor*>(_v) = _t->getLightHoverColor(); break;
        case 3: *reinterpret_cast<QColor*>(_v) = _t->getDarkHoverColor(); break;
        case 4: *reinterpret_cast<QColor*>(_v) = _t->getLightIconColor(); break;
        case 5: *reinterpret_cast<QColor*>(_v) = _t->getDarkIconColor(); break;
        case 6: *reinterpret_cast<QColor*>(_v) = _t->getLightHoverIconColor(); break;
        case 7: *reinterpret_cast<QColor*>(_v) = _t->getDarkHoverIconColor(); break;
        case 8: *reinterpret_cast<bool*>(_v) = _t->getIsSelected(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setBorderRadius(*reinterpret_cast<int*>(_v)); break;
        case 1: _t->setOpacity(*reinterpret_cast<qreal*>(_v)); break;
        case 2: _t->setLightHoverColor(*reinterpret_cast<QColor*>(_v)); break;
        case 3: _t->setDarkHoverColor(*reinterpret_cast<QColor*>(_v)); break;
        case 4: _t->setLightIconColor(*reinterpret_cast<QColor*>(_v)); break;
        case 5: _t->setDarkIconColor(*reinterpret_cast<QColor*>(_v)); break;
        case 6: _t->setLightHoverIconColor(*reinterpret_cast<QColor*>(_v)); break;
        case 7: _t->setDarkHoverIconColor(*reinterpret_cast<QColor*>(_v)); break;
        case 8: _t->setIsSelected(*reinterpret_cast<bool*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaIconButton::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaIconButton::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13ElaIconButtonE_t>.strings))
        return static_cast<void*>(this);
    return QPushButton::qt_metacast(_clname);
}

int ElaIconButton::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QPushButton::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 9)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 9)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 9;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    }
    return _id;
}

// SIGNAL 0
void ElaIconButton::pBorderRadiusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaIconButton::pOpacityChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaIconButton::pLightHoverColorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaIconButton::pDarkHoverColorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaIconButton::pLightIconColorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ElaIconButton::pDarkIconColorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void ElaIconButton::pLightHoverIconColorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void ElaIconButton::pDarkHoverIconColorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}

// SIGNAL 8
void ElaIconButton::pIsSelectedChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 8, nullptr);
}
QT_WARNING_POP
