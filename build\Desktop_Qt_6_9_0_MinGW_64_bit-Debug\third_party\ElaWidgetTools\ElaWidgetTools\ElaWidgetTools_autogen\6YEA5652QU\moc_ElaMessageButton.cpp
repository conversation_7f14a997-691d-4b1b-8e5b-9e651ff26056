/****************************************************************************
** Meta object code from reading C++ file 'ElaMessageButton.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaMessageButton.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaMessageButton.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN16ElaMessageButtonE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaMessageButton::qt_create_metaobjectdata<qt_meta_tag_ZN16ElaMessageButtonE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaMessageButton",
        "pBorderRadiusChanged",
        "",
        "pBarTitleChanged",
        "pBarTextChanged",
        "pDisplayMsecChanged",
        "pMessageTargetWidgetChanged",
        "pMessageModeChanged",
        "pPositionPolicyChanged",
        "pBorderRadius",
        "pBarTitle",
        "pBarText",
        "pDisplayMsec",
        "pMessageTargetWidget",
        "QWidget*",
        "pMessageMode",
        "ElaMessageBarType::MessageMode",
        "pPositionPolicy",
        "ElaMessageBarType::PositionPolicy"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pBorderRadiusChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pBarTitleChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pBarTextChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pDisplayMsecChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pMessageTargetWidgetChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pMessageModeChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pPositionPolicyChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pBorderRadius'
        QtMocHelpers::PropertyData<int>(9, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pBarTitle'
        QtMocHelpers::PropertyData<QString>(10, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pBarText'
        QtMocHelpers::PropertyData<QString>(11, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pDisplayMsec'
        QtMocHelpers::PropertyData<int>(12, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 3),
        // property 'pMessageTargetWidget'
        QtMocHelpers::PropertyData<QWidget*>(13, 0x80000000 | 14, QMC::DefaultPropertyFlags | QMC::Writable | QMC::EnumOrFlag, 4),
        // property 'pMessageMode'
        QtMocHelpers::PropertyData<ElaMessageBarType::MessageMode>(15, 0x80000000 | 16, QMC::DefaultPropertyFlags | QMC::Writable | QMC::EnumOrFlag, 5),
        // property 'pPositionPolicy'
        QtMocHelpers::PropertyData<ElaMessageBarType::PositionPolicy>(17, 0x80000000 | 18, QMC::DefaultPropertyFlags | QMC::Writable | QMC::EnumOrFlag, 6),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaMessageButton, qt_meta_tag_ZN16ElaMessageButtonE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT static const QMetaObject::SuperData qt_meta_extradata_ZN16ElaMessageButtonE[] = {
    QMetaObject::SuperData::link<ElaMessageBarType::staticMetaObject>(),
    nullptr
};

Q_CONSTINIT const QMetaObject ElaMessageButton::staticMetaObject = { {
    QMetaObject::SuperData::link<QPushButton::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16ElaMessageButtonE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16ElaMessageButtonE_t>.data,
    qt_static_metacall,
    qt_meta_extradata_ZN16ElaMessageButtonE,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN16ElaMessageButtonE_t>.metaTypes,
    nullptr
} };

void ElaMessageButton::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaMessageButton *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pBorderRadiusChanged(); break;
        case 1: _t->pBarTitleChanged(); break;
        case 2: _t->pBarTextChanged(); break;
        case 3: _t->pDisplayMsecChanged(); break;
        case 4: _t->pMessageTargetWidgetChanged(); break;
        case 5: _t->pMessageModeChanged(); break;
        case 6: _t->pPositionPolicyChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaMessageButton::*)()>(_a, &ElaMessageButton::pBorderRadiusChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaMessageButton::*)()>(_a, &ElaMessageButton::pBarTitleChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaMessageButton::*)()>(_a, &ElaMessageButton::pBarTextChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaMessageButton::*)()>(_a, &ElaMessageButton::pDisplayMsecChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaMessageButton::*)()>(_a, &ElaMessageButton::pMessageTargetWidgetChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaMessageButton::*)()>(_a, &ElaMessageButton::pMessageModeChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaMessageButton::*)()>(_a, &ElaMessageButton::pPositionPolicyChanged, 6))
            return;
    }
    if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 4:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QWidget* >(); break;
        }
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = _t->getBorderRadius(); break;
        case 1: *reinterpret_cast<QString*>(_v) = _t->getBarTitle(); break;
        case 2: *reinterpret_cast<QString*>(_v) = _t->getBarText(); break;
        case 3: *reinterpret_cast<int*>(_v) = _t->getDisplayMsec(); break;
        case 4: *reinterpret_cast<QWidget**>(_v) = _t->getMessageTargetWidget(); break;
        case 5: *reinterpret_cast<ElaMessageBarType::MessageMode*>(_v) = _t->getMessageMode(); break;
        case 6: *reinterpret_cast<ElaMessageBarType::PositionPolicy*>(_v) = _t->getPositionPolicy(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setBorderRadius(*reinterpret_cast<int*>(_v)); break;
        case 1: _t->setBarTitle(*reinterpret_cast<QString*>(_v)); break;
        case 2: _t->setBarText(*reinterpret_cast<QString*>(_v)); break;
        case 3: _t->setDisplayMsec(*reinterpret_cast<int*>(_v)); break;
        case 4: _t->setMessageTargetWidget(*reinterpret_cast<QWidget**>(_v)); break;
        case 5: _t->setMessageMode(*reinterpret_cast<ElaMessageBarType::MessageMode*>(_v)); break;
        case 6: _t->setPositionPolicy(*reinterpret_cast<ElaMessageBarType::PositionPolicy*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaMessageButton::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaMessageButton::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16ElaMessageButtonE_t>.strings))
        return static_cast<void*>(this);
    return QPushButton::qt_metacast(_clname);
}

int ElaMessageButton::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QPushButton::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 7;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    }
    return _id;
}

// SIGNAL 0
void ElaMessageButton::pBorderRadiusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaMessageButton::pBarTitleChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaMessageButton::pBarTextChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaMessageButton::pDisplayMsecChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaMessageButton::pMessageTargetWidgetChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ElaMessageButton::pMessageModeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void ElaMessageButton::pPositionPolicyChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}
QT_WARNING_POP
