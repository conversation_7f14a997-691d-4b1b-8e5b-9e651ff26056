#ifndef IMAGEMANAGER_H
#define IMAGEMANAGER_H

#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QPixmap>
#include <QCache>
#include <QQueue>
#include <QTimer>
#include <QMutex>
#include <QDir>
#include <QStandardPaths>

/**
 * @brief 图片管理器类
 * 
 * 负责异步下载、缓存和管理图片资源
 * 支持内存缓存和磁盘缓存
 */
class ImageManager : public QObject
{
    Q_OBJECT

public:
    explicit ImageManager(QObject *parent = nullptr);
    ~ImageManager();

    // 缓存配置
    void setMemoryCacheSize(int maxCostKB);
    int memoryCacheSize() const;
    
    void setDiskCacheEnabled(bool enabled);
    bool isDiskCacheEnabled() const;
    
    void setDiskCacheDirectory(const QString& directory);
    QString diskCacheDirectory() const;
    
    void setDiskCacheMaxSize(qint64 maxSizeBytes);
    qint64 diskCacheMaxSize() const;
    
    // 下载配置
    void setMaxConcurrentDownloads(int maxDownloads);
    int maxConcurrentDownloads() const;
    
    void setDownloadTimeout(int timeoutMs);
    int downloadTimeout() const;
    
    // 主要功能
    void loadImage(int imageId, const QString& url, const QSize& preferredSize = QSize());
    void preloadImage(const QString& url, const QSize& preferredSize = QSize());
    
    // 缓存管理
    void clearMemoryCache();
    void clearDiskCache();
    void clearAllCache();
    
    // 状态查询
    bool isImageCached(const QString& url, const QSize& size = QSize()) const;
    QPixmap getCachedImage(const QString& url, const QSize& size = QSize()) const;
    
    // 统计信息
    int pendingDownloads() const;
    int activeDownloads() const;
    qint64 diskCacheUsage() const;
    int memoryCacheUsage() const;

signals:
    /**
     * @brief 图片加载成功
     * @param imageId 图片ID（如果通过loadImage调用）
     * @param url 图片URL
     * @param pixmap 加载的图片
     */
    void imageLoaded(int imageId, const QString& url, const QPixmap& pixmap);
    
    /**
     * @brief 图片加载失败
     * @param imageId 图片ID（如果通过loadImage调用）
     * @param url 图片URL
     * @param errorMessage 错误信息
     */
    void imageLoadFailed(int imageId, const QString& url, const QString& errorMessage);
    
    /**
     * @brief 下载进度更新
     * @param url 图片URL
     * @param bytesReceived 已接收字节数
     * @param bytesTotal 总字节数
     */
    void downloadProgress(const QString& url, qint64 bytesReceived, qint64 bytesTotal);

private slots:
    void onNetworkReply();
    void onDownloadProgress(qint64 bytesReceived, qint64 bytesTotal);
    void processDownloadQueue();

private:
    struct DownloadRequest {
        int imageId = -1;           // -1 表示预加载请求
        QString url;
        QSize preferredSize;
        int retryCount = 0;
        
        DownloadRequest() = default;
        DownloadRequest(int id, const QString& u, const QSize& size = QSize())
            : imageId(id), url(u), preferredSize(size) {}
    };

    // 网络相关
    QNetworkAccessManager* m_networkManager;
    QMap<QNetworkReply*, DownloadRequest> m_activeDownloads;
    QQueue<DownloadRequest> m_downloadQueue;
    QTimer* m_queueTimer;
    
    // 缓存相关
    QCache<QString, QPixmap>* m_memoryCache;  // 内存缓存
    QString m_diskCacheDir;                   // 磁盘缓存目录
    bool m_diskCacheEnabled;                  // 是否启用磁盘缓存
    qint64 m_diskCacheMaxSize;               // 磁盘缓存最大大小
    mutable QMutex m_cacheMutex;             // 缓存访问互斥锁
    
    // 配置参数
    int m_maxConcurrentDownloads;            // 最大并发下载数
    int m_downloadTimeoutMs;                 // 下载超时时间
    int m_maxRetries;                        // 最大重试次数
    
    // 私有方法
    void initializeDiskCache();
    QString generateCacheKey(const QString& url, const QSize& size = QSize()) const;
    QString getCacheFilePath(const QString& cacheKey) const;
    
    // 内存缓存操作
    void cacheImageInMemory(const QString& cacheKey, const QPixmap& pixmap);
    QPixmap getImageFromMemoryCache(const QString& cacheKey) const;
    
    // 磁盘缓存操作
    void cacheImageOnDisk(const QString& cacheKey, const QPixmap& pixmap);
    QPixmap getImageFromDiskCache(const QString& cacheKey) const;
    void cleanupDiskCache();
    qint64 calculateDiskCacheSize() const;
    
    // 下载管理
    void startDownload(const DownloadRequest& request);
    void retryDownload(const DownloadRequest& request);
    bool canStartNewDownload() const;
    
    // 图片处理
    QPixmap processDownloadedImage(const QByteArray& imageData, const QSize& preferredSize) const;
    QPixmap scaleImage(const QPixmap& pixmap, const QSize& targetSize) const;
    
    // 工具方法
    QString getErrorMessage(QNetworkReply* reply) const;
    bool isValidImageData(const QByteArray& data) const;
};

#endif // IMAGEMANAGER_H
