cmake_minimum_required(VERSION 3.16)

project(CivitaiImageViewer VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt6
find_package(QT NAMES Qt6 Qt5 REQUIRED COMPONENTS Core Widgets Network)
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Core Widgets Network)

# Set Qt MOC, UIC, RCC to ON
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Add ElaWidgetTools subdirectory
# Note: ElaWidgetTools should be placed in third_party/ElaWidgetTools
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/third_party/ElaWidgetTools/CMakeLists.txt")
    add_subdirectory(third_party/ElaWidgetTools)
    set(ELAWIDGETTOOLS_AVAILABLE TRUE)
else()
    message(WARNING "ElaWidgetTools not found in third_party/ElaWidgetTools. Please clone it from GitHub.")
    set(ELAWIDGETTOOLS_AVAILABLE FALSE)
endif()

# Source files
set(SOURCES
    src/main.cpp
    src/MainWindow.cpp
    src/CivitaiClient.cpp
    src/CivitaiImageInfo.cpp
    src/ImageManager.cpp
    src/ConfigManager.cpp
    src/MetaDataProcessor.cpp
)

# Header files
set(HEADERS
    src/MainWindow.h
    src/CivitaiClient.h
    src/CivitaiImageInfo.h
    src/ImageManager.h
    src/ConfigManager.h
    src/MetaDataProcessor.h
)

# Create executable
add_executable(CivitaiImageViewer ${SOURCES} ${HEADERS})

# Link Qt libraries
target_link_libraries(CivitaiImageViewer 
    Qt${QT_VERSION_MAJOR}::Core
    Qt${QT_VERSION_MAJOR}::Widgets
    Qt${QT_VERSION_MAJOR}::Network
)

# Link ElaWidgetTools if available
if(ELAWIDGETTOOLS_AVAILABLE)
    target_link_libraries(CivitaiImageViewer ElaWidgetTools)
    target_include_directories(CivitaiImageViewer PRIVATE 
        ${CMAKE_CURRENT_SOURCE_DIR}/third_party/ElaWidgetTools
    )
    target_compile_definitions(CivitaiImageViewer PRIVATE ELAWIDGETTOOLS_AVAILABLE)
endif()

# Set MSVC specific options
if(MSVC)
    target_compile_options(CivitaiImageViewer PRIVATE /utf-8)
endif()

# Include directories
target_include_directories(CivitaiImageViewer PRIVATE src)

# Set output directory
set_target_properties(CivitaiImageViewer PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Copy Qt DLLs on Windows (for development)
if(WIN32 AND Qt${QT_VERSION_MAJOR}_FOUND)
    find_program(QT_DEPLOY_EXECUTABLE windeployqt HINTS ${Qt${QT_VERSION_MAJOR}_DIR}/../../../bin)
    if(QT_DEPLOY_EXECUTABLE)
        add_custom_command(TARGET CivitaiImageViewer POST_BUILD
            COMMAND ${QT_DEPLOY_EXECUTABLE} $<TARGET_FILE:CivitaiImageViewer>
            COMMENT "Deploying Qt libraries"
        )
    endif()
endif()
