{"classes": [{"className": "ElaTableView", "lineNumber": 9, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "<PERSON><PERSON>erMargin", "notify": "pHeaderMarginChanged", "read": "getHeaderMargin", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "set<PERSON><PERSON>erMargin"}], "qualifiedClassName": "ElaTableView", "signals": [{"access": "public", "index": 0, "name": "pHeaderMarginChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "tableViewShow", "returnType": "void"}, {"access": "public", "index": 2, "name": "tableViewHide", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QTableView"}]}], "inputFile": "ElaTableView.h", "outputRevision": 69}