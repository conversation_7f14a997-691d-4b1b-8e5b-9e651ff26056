{"artifacts": [{"path": "bin/CivitaiImageViewer.exe"}, {"path": "bin/CivitaiImageViewer.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "target_include_directories"], "files": ["CMakeLists.txt", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6/Qt6Config.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "D:/Program/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "D:/Program/Qt/6.9.0/mingw_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 65, "parent": 0}, {"command": 1, "file": 0, "line": 68, "parent": 0}, {"command": 4, "file": 0, "line": 15, "parent": 0}, {"file": 3, "parent": 3}, {"command": 4, "file": 3, "line": 212, "parent": 4}, {"file": 2, "parent": 5}, {"command": 3, "file": 2, "line": 55, "parent": 6}, {"file": 1, "parent": 7}, {"command": 2, "file": 1, "line": 61, "parent": 8}, {"command": 3, "file": 2, "line": 43, "parent": 6}, {"file": 8, "parent": 10}, {"command": 6, "file": 8, "line": 45, "parent": 11}, {"command": 5, "file": 7, "line": 137, "parent": 12}, {"command": 4, "file": 6, "line": 76, "parent": 13}, {"file": 5, "parent": 14}, {"command": 3, "file": 5, "line": 55, "parent": 15}, {"file": 4, "parent": 16}, {"command": 2, "file": 4, "line": 61, "parent": 17}, {"command": 4, "file": 3, "line": 212, "parent": 4}, {"file": 10, "parent": 19}, {"command": 3, "file": 10, "line": 57, "parent": 20}, {"file": 9, "parent": 21}, {"command": 2, "file": 9, "line": 61, "parent": 22}, {"command": 4, "file": 3, "line": 212, "parent": 4}, {"file": 12, "parent": 24}, {"command": 3, "file": 12, "line": 55, "parent": 25}, {"file": 11, "parent": 26}, {"command": 2, "file": 11, "line": 61, "parent": 27}, {"command": 7, "file": 0, "line": 89, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -g -std=gnu++17 -fdiagnostics-color=always"}], "defines": [{"backtrace": 2, "define": "MINGW_HAS_SECURE_API=1"}, {"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_NETWORK_LIB"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}, {"backtrace": 2, "define": "UNICODE"}, {"backtrace": 2, "define": "WIN32"}, {"backtrace": 2, "define": "WIN64"}, {"backtrace": 2, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 2, "define": "_UNICODE"}, {"backtrace": 2, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/CivitaiImageViewer_autogen/include"}, {"backtrace": 29, "path": "C:/Users/<USER>/Desktop/Civitai_IMG/src"}, {"backtrace": 2, "isSystem": true, "path": "D:/Program/Qt/6.9.0/mingw_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "D:/Program/Qt/6.9.0/mingw_64/include"}, {"backtrace": 2, "isSystem": true, "path": "D:/Program/Qt/6.9.0/mingw_64/mkspecs/win32-g++"}, {"backtrace": 2, "isSystem": true, "path": "D:/Program/Qt/6.9.0/mingw_64/include/QtWidgets"}, {"backtrace": 2, "isSystem": true, "path": "D:/Program/Qt/6.9.0/mingw_64/include/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "D:/Program/Qt/6.9.0/mingw_64/include/QtNetwork"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}], "dependencies": [{"id": "CivitaiImageViewer_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}, {"backtrace": 0, "id": "CivitaiImageViewer_autogen::@6890427a1f51a3e7e1df"}], "id": "CivitaiImageViewer::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-DQT_QML_DEBUG -g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 2, "fragment": "D:\\Program\\Qt\\6.9.0\\mingw_64\\lib\\libQt6Widgets.a", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Program\\Qt\\6.9.0\\mingw_64\\lib\\libQt6Network.a", "role": "libraries"}, {"backtrace": 9, "fragment": "D:\\Program\\Qt\\6.9.0\\mingw_64\\lib\\libQt6Gui.a", "role": "libraries"}, {"backtrace": 18, "fragment": "-ld3d11", "role": "libraries"}, {"backtrace": 18, "fragment": "-ldxgi", "role": "libraries"}, {"backtrace": 18, "fragment": "-ldxguid", "role": "libraries"}, {"backtrace": 18, "fragment": "-ld3d12", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Program\\Qt\\6.9.0\\mingw_64\\lib\\libQt6Core.a", "role": "libraries"}, {"backtrace": 23, "fragment": "-lmpr", "role": "libraries"}, {"backtrace": 23, "fragment": "-l<PERSON><PERSON>v", "role": "libraries"}, {"backtrace": 28, "fragment": "-lws2_32", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "CivitaiImageViewer", "nameOnDisk": "CivitaiImageViewer.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [10, 11, 12, 13, 14, 15, 16, 17]}, {"name": "", "sourceIndexes": [18]}, {"name": "CMake Rules", "sourceIndexes": [19]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/CivitaiImageViewer_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/MainWindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/CivitaiClient.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/CivitaiImageInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ImageManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ConfigManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/MetaDataProcessor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ImageCardWidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/SettingsDialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/MainWindow.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/CivitaiClient.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/CivitaiImageInfo.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ImageManager.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ConfigManager.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/MetaDataProcessor.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ImageCardWidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/SettingsDialog.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/CivitaiImageViewer_autogen/timestamp", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/CivitaiImageViewer_autogen/timestamp.rule", "sourceGroupIndex": 3}], "type": "EXECUTABLE"}