/****************************************************************************
** Meta object code from reading C++ file 'ElaSuggestBoxPrivate.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/private/ElaSuggestBoxPrivate.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaSuggestBoxPrivate.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN13ElaSuggestionE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaSuggestion::qt_create_metaobjectdata<qt_meta_tag_ZN13ElaSuggestionE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaSuggestion",
        "pElaIconChanged",
        "",
        "pSuggestTextChanged",
        "pSuggestKeyChanged",
        "pSuggestDataChanged",
        "pElaIcon",
        "ElaIconType::IconName",
        "pSuggestText",
        "pSuggestKey",
        "pSuggestData",
        "QVariantMap"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pElaIconChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pSuggestTextChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pSuggestKeyChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pSuggestDataChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pElaIcon'
        QtMocHelpers::PropertyData<ElaIconType::IconName>(6, 0x80000000 | 7, QMC::DefaultPropertyFlags | QMC::Writable | QMC::EnumOrFlag, 0),
        // property 'pSuggestText'
        QtMocHelpers::PropertyData<QString>(8, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pSuggestKey'
        QtMocHelpers::PropertyData<QString>(9, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pSuggestData'
        QtMocHelpers::PropertyData<QVariantMap>(10, 0x80000000 | 11, QMC::DefaultPropertyFlags | QMC::Writable | QMC::EnumOrFlag, 3),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaSuggestion, qt_meta_tag_ZN13ElaSuggestionE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT static const QMetaObject::SuperData qt_meta_extradata_ZN13ElaSuggestionE[] = {
    QMetaObject::SuperData::link<ElaIconType::staticMetaObject>(),
    nullptr
};

Q_CONSTINIT const QMetaObject ElaSuggestion::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13ElaSuggestionE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13ElaSuggestionE_t>.data,
    qt_static_metacall,
    qt_meta_extradata_ZN13ElaSuggestionE,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN13ElaSuggestionE_t>.metaTypes,
    nullptr
} };

void ElaSuggestion::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaSuggestion *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pElaIconChanged(); break;
        case 1: _t->pSuggestTextChanged(); break;
        case 2: _t->pSuggestKeyChanged(); break;
        case 3: _t->pSuggestDataChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaSuggestion::*)()>(_a, &ElaSuggestion::pElaIconChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaSuggestion::*)()>(_a, &ElaSuggestion::pSuggestTextChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaSuggestion::*)()>(_a, &ElaSuggestion::pSuggestKeyChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaSuggestion::*)()>(_a, &ElaSuggestion::pSuggestDataChanged, 3))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<ElaIconType::IconName*>(_v) = _t->_pElaIcon; break;
        case 1: *reinterpret_cast<QString*>(_v) = _t->_pSuggestText; break;
        case 2: *reinterpret_cast<QString*>(_v) = _t->_pSuggestKey; break;
        case 3: *reinterpret_cast<QVariantMap*>(_v) = _t->_pSuggestData; break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0:
            if (QtMocHelpers::setProperty(_t->_pElaIcon, *reinterpret_cast<ElaIconType::IconName*>(_v)))
                Q_EMIT _t->pElaIconChanged();
            break;
        case 1:
            if (QtMocHelpers::setProperty(_t->_pSuggestText, *reinterpret_cast<QString*>(_v)))
                Q_EMIT _t->pSuggestTextChanged();
            break;
        case 2:
            if (QtMocHelpers::setProperty(_t->_pSuggestKey, *reinterpret_cast<QString*>(_v)))
                Q_EMIT _t->pSuggestKeyChanged();
            break;
        case 3:
            if (QtMocHelpers::setProperty(_t->_pSuggestData, *reinterpret_cast<QVariantMap*>(_v)))
                Q_EMIT _t->pSuggestDataChanged();
            break;
        default: break;
        }
    }
}

const QMetaObject *ElaSuggestion::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaSuggestion::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13ElaSuggestionE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ElaSuggestion::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 4;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void ElaSuggestion::pElaIconChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaSuggestion::pSuggestTextChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaSuggestion::pSuggestKeyChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaSuggestion::pSuggestDataChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}
namespace {
struct qt_meta_tag_ZN20ElaSuggestBoxPrivateE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaSuggestBoxPrivate::qt_create_metaobjectdata<qt_meta_tag_ZN20ElaSuggestBoxPrivateE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaSuggestBoxPrivate",
        "onThemeModeChanged",
        "",
        "ElaThemeType::ThemeMode",
        "themeMode",
        "onSearchEditTextEdit",
        "searchText",
        "onSearchViewClicked",
        "QModelIndex",
        "index"
    };

    QtMocHelpers::UintData qt_methods {
        // Slot 'onThemeModeChanged'
        QtMocHelpers::SlotData<void(ElaThemeType::ThemeMode)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 3, 4 },
        }}),
        // Slot 'onSearchEditTextEdit'
        QtMocHelpers::SlotData<void(const QString &)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 },
        }}),
        // Slot 'onSearchViewClicked'
        QtMocHelpers::SlotData<void(const QModelIndex &)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 8, 9 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaSuggestBoxPrivate, qt_meta_tag_ZN20ElaSuggestBoxPrivateE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaSuggestBoxPrivate::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN20ElaSuggestBoxPrivateE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN20ElaSuggestBoxPrivateE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN20ElaSuggestBoxPrivateE_t>.metaTypes,
    nullptr
} };

void ElaSuggestBoxPrivate::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaSuggestBoxPrivate *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->onThemeModeChanged((*reinterpret_cast< std::add_pointer_t<ElaThemeType::ThemeMode>>(_a[1]))); break;
        case 1: _t->onSearchEditTextEdit((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 2: _t->onSearchViewClicked((*reinterpret_cast< std::add_pointer_t<QModelIndex>>(_a[1]))); break;
        default: ;
        }
    }
}

const QMetaObject *ElaSuggestBoxPrivate::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaSuggestBoxPrivate::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN20ElaSuggestBoxPrivateE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ElaSuggestBoxPrivate::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 3)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 3)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 3;
    }
    return _id;
}
QT_WARNING_POP
