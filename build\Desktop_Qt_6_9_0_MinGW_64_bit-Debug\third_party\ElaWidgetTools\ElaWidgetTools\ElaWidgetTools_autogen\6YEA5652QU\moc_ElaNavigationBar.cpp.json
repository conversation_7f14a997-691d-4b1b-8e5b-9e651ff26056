{"classes": [{"className": "ElaNavigationBar", "lineNumber": 10, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pIs<PERSON><PERSON><PERSON><PERSON><PERSON>", "notify": "pIsTransparentChanged", "read": "getIsTransparent", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setIsTransparent"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pIsAllowPageOpenInNewWindow", "notify": "pIsAllowPageOpenInNewWindowChanged", "read": "getIsAllowPageOpenInNewWindow", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setIsAllowPageOpenInNewWindow"}], "qualifiedClassName": "ElaNavigationBar", "signals": [{"access": "public", "index": 0, "name": "pIsTransparentChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pIsAllowPageOpenInNewWindowChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "nodeKey", "type": "QString"}], "index": 2, "name": "pageOpenInNewWindow", "returnType": "void"}, {"access": "public", "index": 3, "name": "userInfoCardClicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "nodeType", "type": "ElaNavigationType::NavigationNodeType"}, {"name": "nodeKey", "type": "QString"}], "index": 4, "name": "navigationNodeClicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "nodeType", "type": "ElaNavigationType::NavigationNodeType"}, {"name": "nodeKey", "type": "QString"}, {"name": "page", "type": "QWidget*"}], "index": 5, "name": "navigationNodeAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "nodeType", "type": "ElaNavigationType::NavigationNodeType"}, {"name": "nodeKey", "type": "QString"}], "index": 6, "name": "navigationNodeRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "ElaNavigationBar.h", "outputRevision": 69}