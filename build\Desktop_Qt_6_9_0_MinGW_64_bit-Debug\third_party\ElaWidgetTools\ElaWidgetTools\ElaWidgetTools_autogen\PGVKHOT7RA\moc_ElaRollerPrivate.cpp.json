{"classes": [{"className": "ElaRollerPrivate", "lineNumber": 11, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "member": "_pScrollOffset", "name": "pScrollOffset", "notify": "pScrollOffsetChanged", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "ElaRollerPrivate", "signals": [{"access": "public", "index": 0, "name": "pScrollOffsetChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "ElaRollerPrivate.h", "outputRevision": 69}