#ifndef CIVITAICLIENT_H
#define CIVITAICLIENT_H

#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QJsonObject>
#include <QJsonArray>
#include <QVariantMap>
#include <QTimer>
#include <QQueue>
#include <QUrlQuery>
#include "CivitaiImageInfo.h"

/**
 * @brief 分页信息结构体
 */
struct PaginationInfo {
    int currentPage = 1;
    int totalPages = 0;
    int totalItems = 0;
    int itemsPerPage = 100;
    QString nextCursor;
    QString prevCursor;
    bool hasNextPage = false;
    bool hasPrevPage = false;

    // 从API响应构造
    explicit PaginationInfo(const QJsonObject& metadataJson = QJsonObject());

    // 转换为JSON
    QJsonObject toJson() const;
};

/**
 * @brief API请求参数结构体
 */
struct ApiRequestParams {
    int limit = 100;                    // 每页数量 (0-200)
    int page = 1;                       // 页码
    QString cursor;                     // 游标分页
    QString query;                      // 搜索关键词
    QString sort = "Most Reactions";    // 排序方式
    QString period = "AllTime";         // 时间范围
    QString nsfw = "None";              // NSFW等级
    int postId = 0;                     // 帖子ID
    int modelId = 0;                    // 模型ID
    int modelVersionId = 0;             // 模型版本ID
    QString username;                   // 用户名

    // 转换为查询参数
    QUrlQuery toUrlQuery() const;

    // 从QVariantMap构造
    static ApiRequestParams fromVariantMap(const QVariantMap& params);
};

/**
 * @brief Civitai API 客户端类
 *
 * 负责与Civitai REST API的所有通信，包括认证、请求构建、响应处理、错误管理和速率控制
 */
class CivitaiClient : public QObject
{
    Q_OBJECT

public:
    explicit CivitaiClient(QObject *parent = nullptr);
    ~CivitaiClient();

    // API Key 管理
    void setApiKey(const QString& apiKey);
    QString apiKey() const { return m_apiKey; }
    bool hasApiKey() const { return !m_apiKey.isEmpty(); }

    // 速率控制设置
    void setRateLimit(int requestsPerSecond);
    int rateLimit() const { return m_rateLimit; }

    void setRetryCount(int maxRetries);
    int retryCount() const { return m_maxRetries; }

    // 主要API方法
    void fetchImages(const ApiRequestParams& params = ApiRequestParams());
    void fetchImages(const QVariantMap& params);

    // 取消当前请求
    void cancelAllRequests();

    // 获取API状态
    bool isBusy() const { return !m_pendingReplies.isEmpty() || !m_requestQueue.isEmpty(); }

signals:
    /**
     * @brief 图片数据获取成功
     * @param images 图片信息列表
     * @param pagination 分页信息
     */
    void imagesReceived(const QList<CivitaiImageInfo>& images, const PaginationInfo& pagination);

    /**
     * @brief 图片数据获取失败
     * @param errorMessage 错误信息
     * @param httpStatusCode HTTP状态码
     * @param errorType 错误类型
     */
    void imageFetchFailed(const QString& errorMessage, int httpStatusCode, const QString& errorType);

    /**
     * @brief 请求进度更新
     * @param current 当前进度
     * @param total 总进度
     */
    void requestProgress(int current, int total);

    /**
     * @brief API速率限制警告
     * @param retryAfterSeconds 建议重试间隔（秒）
     */
    void rateLimitWarning(int retryAfterSeconds);

private slots:
    void onNetworkReply();
    void onRateLimitTimer();
    void processRequestQueue();

private:
    // 网络相关
    QNetworkAccessManager* m_networkManager;
    QSet<QNetworkReply*> m_pendingReplies;

    // 认证
    QString m_apiKey;

    // 速率控制
    QTimer* m_rateLimitTimer;
    QQueue<std::function<void()>> m_requestQueue;
    int m_rateLimit = 1; // 每秒请求数
    int m_lastRequestTime = 0;

    // 重试机制
    int m_maxRetries = 3;
    QMap<QNetworkReply*, int> m_retryCount;
    QMap<QNetworkReply*, ApiRequestParams> m_requestParams;

    // 常量
    static const QString API_BASE_URL;
    static const QString USER_AGENT;

    // 私有方法
    QNetworkRequest createRequest(const QString& endpoint, const QUrlQuery& query = QUrlQuery()) const;
    void sendRequest(const ApiRequestParams& params);
    void handleApiResponse(QNetworkReply* reply);
    void handleNetworkError(QNetworkReply* reply);
    void retryRequest(QNetworkReply* reply);
    void scheduleRequest(std::function<void()> requestFunc);

    // 响应解析
    QList<CivitaiImageInfo> parseImagesFromResponse(const QJsonArray& imagesArray) const;
    PaginationInfo parsePaginationFromResponse(const QJsonObject& metadataObject) const;

    // 错误处理
    QString getErrorMessage(QNetworkReply* reply) const;
    QString getErrorType(int httpStatusCode) const;

    // 工具方法
    bool shouldRetry(QNetworkReply* reply) const;
    int getRetryDelay(int retryAttempt) const; // 指数退避
};

// 注册元类型
Q_DECLARE_METATYPE(PaginationInfo)
Q_DECLARE_METATYPE(ApiRequestParams)

#endif // CIVITAICLIENT_H
