﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{CDF828BD-4DED-371A-8904-EA88369F0836}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>test_civitai_image_info</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">test_civitai_image_info.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">test_civitai_image_info</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">test_civitai_image_info.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">test_civitai_image_info</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">test_civitai_image_info.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">test_civitai_image_info</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">test_civitai_image_info.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">test_civitai_image_info</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\test_civitai_image_info_autogen\include_Debug;C:\Users\<USER>\Desktop\Civitai_IMG\src;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtCore" /external:I "D:/Program/Qt/6.9.0/mingw_64/include" /external:I "D:/Program/Qt/6.9.0/mingw_64/mkspecs/win32-g++" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtWidgets" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtGui" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtNetwork" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtTest" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;ELAWIDGETTOOLS_AVAILABLE;QT_CORE_LIB;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;QT_TESTLIB_LIB;QT_TESTCASE_BUILDDIR="C:/Users/<USER>/Desktop/Civitai_IMG/build/tests";QT_TESTCASE_SOURCEDIR="C:/Users/<USER>/Desktop/Civitai_IMG/tests";CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;ELAWIDGETTOOLS_AVAILABLE;QT_CORE_LIB;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;QT_TESTLIB_LIB;QT_TESTCASE_BUILDDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/build/tests\";QT_TESTCASE_SOURCEDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/tests\";CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\test_civitai_image_info_autogen\include_Debug;C:\Users\<USER>\Desktop\Civitai_IMG\src;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;D:\Program\Qt\6.9.0\mingw_64\include\QtNetwork;D:\Program\Qt\6.9.0\mingw_64\include\QtTest;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\test_civitai_image_info_autogen\include_Debug;C:\Users\<USER>\Desktop\Civitai_IMG\src;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;D:\Program\Qt\6.9.0\mingw_64\include\QtNetwork;D:\Program\Qt\6.9.0\mingw_64\include\QtTest;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target test_civitai_image_info</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\Civitai_IMG\build\tests
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autogen C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/CMakeFiles/test_civitai_image_info_autogen.dir/AutogenInfo.json Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Network.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Test.a;..\third_party\ElaWidgetTools\ElaWidgetTools\Debug\ElaWidgetToolsd.lib;ws2_32.lib;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Widgets.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Gui.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;D3D11.lib;DXGI.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/Debug/test_civitai_image_info.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/Debug/test_civitai_image_info.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\test_civitai_image_info_autogen\include_Release;C:\Users\<USER>\Desktop\Civitai_IMG\src;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtCore" /external:I "D:/Program/Qt/6.9.0/mingw_64/include" /external:I "D:/Program/Qt/6.9.0/mingw_64/mkspecs/win32-g++" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtWidgets" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtGui" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtNetwork" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtTest" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ELAWIDGETTOOLS_AVAILABLE;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;QT_TESTLIB_LIB;QT_TESTCASE_BUILDDIR="C:/Users/<USER>/Desktop/Civitai_IMG/build/tests";QT_TESTCASE_SOURCEDIR="C:/Users/<USER>/Desktop/Civitai_IMG/tests";CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ELAWIDGETTOOLS_AVAILABLE;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;QT_TESTLIB_LIB;QT_TESTCASE_BUILDDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/build/tests\";QT_TESTCASE_SOURCEDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/tests\";CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\test_civitai_image_info_autogen\include_Release;C:\Users\<USER>\Desktop\Civitai_IMG\src;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;D:\Program\Qt\6.9.0\mingw_64\include\QtNetwork;D:\Program\Qt\6.9.0\mingw_64\include\QtTest;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\test_civitai_image_info_autogen\include_Release;C:\Users\<USER>\Desktop\Civitai_IMG\src;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;D:\Program\Qt\6.9.0\mingw_64\include\QtNetwork;D:\Program\Qt\6.9.0\mingw_64\include\QtTest;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target test_civitai_image_info</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\Civitai_IMG\build\tests
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autogen C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/CMakeFiles/test_civitai_image_info_autogen.dir/AutogenInfo.json Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Network.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Test.a;..\third_party\ElaWidgetTools\ElaWidgetTools\Release\ElaWidgetTools.lib;ws2_32.lib;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Widgets.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Gui.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;D3D11.lib;DXGI.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/Release/test_civitai_image_info.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/Release/test_civitai_image_info.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\test_civitai_image_info_autogen\include_MinSizeRel;C:\Users\<USER>\Desktop\Civitai_IMG\src;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtCore" /external:I "D:/Program/Qt/6.9.0/mingw_64/include" /external:I "D:/Program/Qt/6.9.0/mingw_64/mkspecs/win32-g++" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtWidgets" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtGui" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtNetwork" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtTest" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ELAWIDGETTOOLS_AVAILABLE;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;QT_TESTLIB_LIB;QT_TESTCASE_BUILDDIR="C:/Users/<USER>/Desktop/Civitai_IMG/build/tests";QT_TESTCASE_SOURCEDIR="C:/Users/<USER>/Desktop/Civitai_IMG/tests";CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ELAWIDGETTOOLS_AVAILABLE;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;QT_TESTLIB_LIB;QT_TESTCASE_BUILDDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/build/tests\";QT_TESTCASE_SOURCEDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/tests\";CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\test_civitai_image_info_autogen\include_MinSizeRel;C:\Users\<USER>\Desktop\Civitai_IMG\src;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;D:\Program\Qt\6.9.0\mingw_64\include\QtNetwork;D:\Program\Qt\6.9.0\mingw_64\include\QtTest;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\test_civitai_image_info_autogen\include_MinSizeRel;C:\Users\<USER>\Desktop\Civitai_IMG\src;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;D:\Program\Qt\6.9.0\mingw_64\include\QtNetwork;D:\Program\Qt\6.9.0\mingw_64\include\QtTest;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target test_civitai_image_info</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\Civitai_IMG\build\tests
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autogen C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/CMakeFiles/test_civitai_image_info_autogen.dir/AutogenInfo.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Network.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Test.a;..\third_party\ElaWidgetTools\ElaWidgetTools\MinSizeRel\ElaWidgetTools.lib;ws2_32.lib;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Widgets.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Gui.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;D3D11.lib;DXGI.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/MinSizeRel/test_civitai_image_info.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/MinSizeRel/test_civitai_image_info.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\test_civitai_image_info_autogen\include_RelWithDebInfo;C:\Users\<USER>\Desktop\Civitai_IMG\src;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtCore" /external:I "D:/Program/Qt/6.9.0/mingw_64/include" /external:I "D:/Program/Qt/6.9.0/mingw_64/mkspecs/win32-g++" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtWidgets" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtGui" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtNetwork" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtTest" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ELAWIDGETTOOLS_AVAILABLE;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;QT_TESTLIB_LIB;QT_TESTCASE_BUILDDIR="C:/Users/<USER>/Desktop/Civitai_IMG/build/tests";QT_TESTCASE_SOURCEDIR="C:/Users/<USER>/Desktop/Civitai_IMG/tests";CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ELAWIDGETTOOLS_AVAILABLE;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;QT_TESTLIB_LIB;QT_TESTCASE_BUILDDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/build/tests\";QT_TESTCASE_SOURCEDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/tests\";CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\test_civitai_image_info_autogen\include_RelWithDebInfo;C:\Users\<USER>\Desktop\Civitai_IMG\src;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;D:\Program\Qt\6.9.0\mingw_64\include\QtNetwork;D:\Program\Qt\6.9.0\mingw_64\include\QtTest;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\test_civitai_image_info_autogen\include_RelWithDebInfo;C:\Users\<USER>\Desktop\Civitai_IMG\src;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;D:\Program\Qt\6.9.0\mingw_64\include\QtNetwork;D:\Program\Qt\6.9.0\mingw_64\include\QtTest;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target test_civitai_image_info</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\Civitai_IMG\build\tests
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autogen C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/CMakeFiles/test_civitai_image_info_autogen.dir/AutogenInfo.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Network.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Test.a;..\third_party\ElaWidgetTools\ElaWidgetTools\RelWithDebInfo\ElaWidgetTools.lib;ws2_32.lib;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Widgets.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Gui.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;D3D11.lib;DXGI.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/RelWithDebInfo/test_civitai_image_info.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/RelWithDebInfo/test_civitai_image_info.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\Civitai_IMG\tests\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Civitai_IMG/tests/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -SC:/Users/<USER>/Desktop/Civitai_IMG -BC:/Users/<USER>/Desktop/Civitai_IMG/build --check-stamp-file C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CheckCXXCompilerFlag.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Config.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Dependencies.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Targets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeature.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeatureCommon.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtInstallPaths.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestAdditionalTargetInfo.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestConfig.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestConfigVersion.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestConfigVersionImpl.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestDependencies.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestTargets-relwithdebinfo.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestVersionlessAliasTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\QtTestProperties.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateAdditionalTargetInfo.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfig.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfigVersion.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfigVersionImpl.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateDependencies.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateVersionlessAliasTargets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Civitai_IMG/tests/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -SC:/Users/<USER>/Desktop/Civitai_IMG -BC:/Users/<USER>/Desktop/Civitai_IMG/build --check-stamp-file C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CheckCXXCompilerFlag.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Config.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Dependencies.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Targets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeature.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeatureCommon.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtInstallPaths.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestAdditionalTargetInfo.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestConfig.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestConfigVersion.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestConfigVersionImpl.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestDependencies.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestTargets-relwithdebinfo.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestVersionlessAliasTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\QtTestProperties.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateAdditionalTargetInfo.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfig.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfigVersion.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfigVersionImpl.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateDependencies.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateVersionlessAliasTargets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Civitai_IMG/tests/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -SC:/Users/<USER>/Desktop/Civitai_IMG -BC:/Users/<USER>/Desktop/Civitai_IMG/build --check-stamp-file C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CheckCXXCompilerFlag.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Config.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Dependencies.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Targets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeature.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeatureCommon.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtInstallPaths.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestAdditionalTargetInfo.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestConfig.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestConfigVersion.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestConfigVersionImpl.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestDependencies.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestTargets-relwithdebinfo.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestVersionlessAliasTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\QtTestProperties.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateAdditionalTargetInfo.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfig.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfigVersion.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfigVersionImpl.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateDependencies.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateVersionlessAliasTargets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Civitai_IMG/tests/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -SC:/Users/<USER>/Desktop/Civitai_IMG -BC:/Users/<USER>/Desktop/Civitai_IMG/build --check-stamp-file C:/Users/<USER>/Desktop/Civitai_IMG/build/tests/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CheckCXXCompilerFlag.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Config.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Dependencies.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Targets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeature.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeatureCommon.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtInstallPaths.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestAdditionalTargetInfo.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestConfig.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestConfigVersion.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestConfigVersionImpl.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestDependencies.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestTargets-relwithdebinfo.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\Qt6TestVersionlessAliasTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Test\QtTestProperties.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateAdditionalTargetInfo.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfig.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfigVersion.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfigVersionImpl.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateDependencies.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateVersionlessAliasTargets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\test_civitai_image_info_autogen\mocs_compilation_Debug.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\tests\test_civitai_image_info.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\src\CivitaiImageInfo.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\src\CivitaiClient.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\src\ImageManager.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\src\ConfigManager.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\src\MetaDataProcessor.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\test_civitai_image_info_autogen\mocs_compilation_Release.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\test_civitai_image_info_autogen\mocs_compilation_MinSizeRel.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\tests\test_civitai_image_info_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\ZERO_CHECK.vcxproj">
      <Project>{442297F5-2886-3548-A699-3DC346C07EB5}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools.vcxproj">
      <Project>{E373B025-08E8-3046-A048-3D2157436208}</Project>
      <Name>ElaWidgetTools</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>