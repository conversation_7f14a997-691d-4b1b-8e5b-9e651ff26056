# 🚀 Civitai 图片查看器启动指南

## 📍 当前状态
您的项目位于：`c:\Users\<USER>\Desktop\Civitai_IMG`
环境：Windows 10 + Git Bash

## 🎯 快速启动方法

### 方法一：使用 Windows 命令提示符（推荐）

#### 1. 打开 Windows 命令提示符
- 按 `Win + R`
- 输入 `cmd`
- 按回车

#### 2. 切换到项目目录
```cmd
cd c:\Users\<USER>\Desktop\Civitai_IMG
```

#### 3. 运行自动化构建脚本
```cmd
test_build.bat
```

这个脚本会：
- ✅ 检查所有必要工具
- ✅ 自动构建项目
- ✅ 提供详细的错误诊断
- ✅ 询问是否立即运行程序

### 方法二：使用 Git Bash

#### 1. 在当前 Git Bash 中运行
```bash
# 检查必要工具
which cmake
which qmake

# 手动构建
mkdir -p build
cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
cmake --build . --config Release

# 运行程序
./bin/CivitaiImageViewer.exe
```

### 方法三：使用 Visual Studio（如果已安装）

#### 1. 打开 Visual Studio
#### 2. 选择 "打开本地文件夹"
#### 3. 选择项目目录：`c:\Users\<USER>\Desktop\Civitai_IMG`
#### 4. Visual Studio 会自动识别 CMake 项目
#### 5. 点击 "生成" → "全部生成"
#### 6. 运行程序

## 🔧 环境要求检查

### 必需工具
- [ ] **Qt 6.2+** - 用于 UI 框架
- [ ] **CMake 3.16+** - 用于构建系统
- [ ] **Visual Studio 2019+** 或 **MinGW** - C++ 编译器

### 检查方法
```cmd
# 检查 Qt
qmake -version

# 检查 CMake
cmake --version

# 检查编译器
cl      # Visual Studio
gcc --version  # MinGW
```

## 📦 依赖获取

### 获取 ElaWidgetTools
如果 `third_party/ElaWidgetTools` 目录为空：

```bash
# 在 Git Bash 中运行
git clone https://github.com/Liniyous/ElaWidgetTools.git third_party/ElaWidgetTools
```

或者手动下载：
1. 访问：https://github.com/Liniyous/ElaWidgetTools
2. 点击 "Code" → "Download ZIP"
3. 解压到 `third_party/ElaWidgetTools` 目录

## 🎮 首次运行配置

### 1. 获取 Civitai API 密钥
1. 访问：https://civitai.com/user/account
2. 登录您的账户
3. 在 "API Keys" 部分生成新密钥
4. 复制生成的密钥

### 2. 配置应用程序
1. 启动应用程序
2. 点击菜单 "文件" → "设置"
3. 在 "API" 选项卡中输入密钥
4. 点击 "测试连接" 验证
5. 保存设置

### 3. 开始使用
1. 在搜索框输入关键词（如 "landscape"）
2. 点击搜索按钮
3. 浏览返回的图片
4. 点击图片查看详细元数据

## 🐛 常见问题解决

### 问题 1: "cmake 不是内部或外部命令"
**解决方案：**
- 下载并安装 CMake：https://cmake.org/download/
- 安装时选择 "Add CMake to system PATH"

### 问题 2: "qmake 不是内部或外部命令"
**解决方案：**
- 下载并安装 Qt：https://www.qt.io/download
- 选择 Qt 6.5 LTS 版本
- 安装时包含 MinGW 编译器

### 问题 3: "ElaWidgetTools 编译错误"
**解决方案：**
```bash
# 重新克隆 ElaWidgetTools
rm -rf third_party/ElaWidgetTools
git clone https://github.com/Liniyous/ElaWidgetTools.git third_party/ElaWidgetTools
```

### 问题 4: 应用程序启动后崩溃
**解决方案：**
- 确保 Qt 运行时库完整
- 检查是否缺少 DLL 文件
- 尝试在 Qt Creator 中运行以获取详细错误信息

## 📞 获取帮助

如果遇到问题：

1. **查看构建日志**
   - 运行 `test_build.bat` 会显示详细的错误信息

2. **检查系统环境**
   - 确保所有必需工具已正确安装

3. **重新开始**
   - 删除 `build` 目录
   - 重新运行构建脚本

## 🎉 成功标志

当您看到以下内容时，说明启动成功：
- ✅ 应用程序窗口正常显示
- ✅ 菜单栏和工具栏加载完成
- ✅ 状态栏显示 "就绪"
- ✅ 可以打开设置对话框

---

**推荐步骤：**
1. 使用 Windows 命令提示符
2. 运行 `test_build.bat`
3. 按照脚本提示操作
4. 配置 API 密钥后开始使用

祝您使用愉快！🎨
