#ifndef IMAGECARDWIDGET_H
#define IMAGECARDWIDGET_H

#include <QWidget>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPixmap>
#include <QPushButton>
#include <QMouseEvent>
#include <QFrame>
#include "CivitaiImageInfo.h"

/**
 * @brief 图片卡片组件
 * 
 * 用于在图片画廊中显示单张图片的卡片组件
 * 如果ElaWidgetTools不可用，提供一个基础的替代实现
 */
class ImageCardWidget : public QFrame
{
    Q_OBJECT

public:
    explicit ImageCardWidget(QWidget *parent = nullptr);
    explicit ImageCardWidget(const CivitaiImageInfo& imageInfo, QWidget *parent = nullptr);
    ~ImageCardWidget();

    // 设置和获取图片信息
    void setImageInfo(const CivitaiImageInfo& imageInfo);
    CivitaiImageInfo imageInfo() const { return m_imageInfo; }
    
    // 设置图片
    void setPixmap(const QPixmap& pixmap);
    QPixmap pixmap() const;
    
    // 设置加载状态
    void setLoading(bool loading);
    bool isLoading() const { return m_isLoading; }
    
    // 设置错误状态
    void setError(const QString& errorMessage);
    void clearError();
    
    // 外观设置
    void setCardSize(const QSize& size);
    QSize cardSize() const;
    
    void setSelected(bool selected);
    bool isSelected() const { return m_isSelected; }

signals:
    /**
     * @brief 卡片被点击
     * @param imageInfo 图片信息
     */
    void cardClicked(const CivitaiImageInfo& imageInfo);
    
    /**
     * @brief 请求加载图片
     * @param imageId 图片ID
     * @param url 图片URL
     */
    void imageLoadRequested(int imageId, const QString& url);

protected:
    void mousePressEvent(QMouseEvent *event) override;
    void enterEvent(QEnterEvent *event) override;
    void leaveEvent(QEvent *event) override;
    void paintEvent(QPaintEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private:
    void initializeUI();
    void updateDisplay();
    void updateImageDisplay();
    void updateInfoDisplay();
    void applyStyle();
    void requestImageLoad();
    
    // 工具方法
    QPixmap createPlaceholderPixmap(const QSize& size) const;
    QPixmap createLoadingPixmap(const QSize& size) const;
    QPixmap createErrorPixmap(const QSize& size) const;
    QString formatImageInfo() const;
    QString formatStats() const;

private:
    // 数据
    CivitaiImageInfo m_imageInfo;
    bool m_isLoading;
    bool m_isSelected;
    QString m_errorMessage;
    QSize m_cardSize;
    
    // UI 组件
    QVBoxLayout* m_mainLayout;
    QLabel* m_imageLabel;
    QLabel* m_titleLabel;
    QLabel* m_authorLabel;
    QLabel* m_sizeLabel;
    QLabel* m_statsLabel;
    QLabel* m_nsfwLabel;
    QWidget* m_infoWidget;
    QVBoxLayout* m_infoLayout;
    
    // 样式相关
    bool m_isHovered;
    QColor m_normalBorderColor;
    QColor m_hoverBorderColor;
    QColor m_selectedBorderColor;
    QColor m_backgroundColor;
    QColor m_textColor;
    
    // 常量
    static const int DEFAULT_CARD_WIDTH;
    static const int DEFAULT_CARD_HEIGHT;
    static const int IMAGE_HEIGHT;
    static const int INFO_HEIGHT;
    static const int BORDER_WIDTH;
    static const int BORDER_RADIUS;
    static const int MARGIN;
    static const int SPACING;
};

#endif // IMAGECARDWIDGET_H
