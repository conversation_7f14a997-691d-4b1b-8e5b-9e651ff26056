[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\CivitaiImageViewer_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\main.cpp"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\CivitaiImageViewer_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\MainWindow.cpp"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/MainWindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\CivitaiImageViewer_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\CivitaiClient.cpp"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/CivitaiClient.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\CivitaiImageViewer_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\CivitaiImageInfo.cpp"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/CivitaiImageInfo.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\CivitaiImageViewer_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\ImageManager.cpp"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/ImageManager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\CivitaiImageViewer_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\ConfigManager.cpp"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/ConfigManager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\CivitaiImageViewer_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\MetaDataProcessor.cpp"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/MetaDataProcessor.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\CivitaiImageViewer_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\ImageCardWidget.cpp"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/ImageCardWidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\CivitaiImageViewer_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\SettingsDialog.cpp"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/SettingsDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\CivitaiImageViewer_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\MainWindow.h"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/MainWindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\CivitaiImageViewer_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\CivitaiClient.h"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/CivitaiClient.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\CivitaiImageViewer_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\CivitaiImageInfo.h"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/CivitaiImageInfo.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\CivitaiImageViewer_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\ImageManager.h"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/ImageManager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\CivitaiImageViewer_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\ConfigManager.h"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/ConfigManager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\CivitaiImageViewer_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\MetaDataProcessor.h"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/MetaDataProcessor.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\CivitaiImageViewer_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\ImageCardWidget.h"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/ImageCardWidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\CivitaiImageViewer_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\SettingsDialog.h"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/SettingsDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_TESTCASE_BUILDDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/tests\"", "-DQT_TESTCASE_SOURCEDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/tests\"", "-DQT_TESTLIB_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\tests\\test_civitai_image_info_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtTest", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\tests\\test_civitai_image_info.cpp"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/tests/test_civitai_image_info.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_TESTCASE_BUILDDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/tests\"", "-DQT_TESTCASE_SOURCEDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/tests\"", "-DQT_TESTLIB_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\tests\\test_civitai_image_info_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtTest", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\CivitaiImageInfo.cpp"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/CivitaiImageInfo.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_TESTCASE_BUILDDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/tests\"", "-DQT_TESTCASE_SOURCEDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/tests\"", "-DQT_TESTLIB_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\tests\\test_civitai_image_info_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtTest", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\CivitaiClient.cpp"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/CivitaiClient.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_TESTCASE_BUILDDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/tests\"", "-DQT_TESTCASE_SOURCEDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/tests\"", "-DQT_TESTLIB_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\tests\\test_civitai_image_info_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtTest", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\ImageManager.cpp"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/ImageManager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_TESTCASE_BUILDDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/tests\"", "-DQT_TESTCASE_SOURCEDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/tests\"", "-DQT_TESTLIB_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\tests\\test_civitai_image_info_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtTest", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\ConfigManager.cpp"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/ConfigManager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_TESTCASE_BUILDDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/tests\"", "-DQT_TESTCASE_SOURCEDIR=\"C:/Users/<USER>/Desktop/Civitai_IMG/tests\"", "-DQT_TESTLIB_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Program\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\tests\\test_civitai_image_info_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtWidgets", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-isystem", "D:\\Program\\Qt\\6.9.0\\mingw_64\\include\\QtTest", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Program\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "D:\\Program\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\Civitai_IMG\\src\\MetaDataProcessor.cpp"], "directory": "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/Civitai_IMG/src/MetaDataProcessor.cpp"}]