#ifndef METADATAPROCESSOR_H
#define METADATAPROCESSOR_H

#include <QObject>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonValue>
#include <QAbstractItemModel>
#include <QStandardItemModel>
#include <QStringList>
#include <QVariantMap>
#include <QRegularExpression>

/**
 * @brief 元数据处理器类
 *
 * 负责处理和解析Civitai图片的元数据（meta字段）
 * 提供多种格式的数据转换和展示功能
 */
class MetaDataProcessor : public QObject
{
    Q_OBJECT

public:
    explicit MetaDataProcessor(QObject *parent = nullptr);
    ~MetaDataProcessor();

    // 主要处理方法
    QString formatMetaToJsonString(const QJsonObject& meta, bool indented = true) const;
    QAbstractItemModel* createMetaTreeModel(const QJsonObject& meta, QObject* parent = nullptr) const;
    QVariantMap extractKeyParameters(const QJsonObject& meta) const;
    QString formatMetaToReadableText(const QJsonObject& meta) const;

    // 特定参数提取
    QString extractPrompt(const QJsonObject& meta) const;
    QString extractNegativePrompt(const QJsonObject& meta) const;
    QString extractModel(const QJsonObject& meta) const;
    QString extractSampler(const QJsonObject& meta) const;
    int extractSteps(const QJsonObject& meta) const;
    double extractCfgScale(const QJsonObject& meta) const;
    int extractSeed(const QJsonObject& meta) const;
    QSize extractImageSize(const QJsonObject& meta) const;

    // LoRA 和嵌入处理
    QStringList extractLoraList(const QJsonObject& meta) const;
    QStringList extractEmbeddingList(const QJsonObject& meta) const;
    QVariantMap parseLoraString(const QString& loraString) const;

    // 搜索和过滤
    QStringList searchInMeta(const QJsonObject& meta, const QString& searchTerm, bool caseSensitive = false) const;
    bool containsKeyword(const QJsonObject& meta, const QString& keyword, bool caseSensitive = false) const;

    // 元数据验证和清理
    bool isValidMeta(const QJsonObject& meta) const;
    QJsonObject cleanupMeta(const QJsonObject& meta) const;
    QJsonObject normalizeMeta(const QJsonObject& meta) const;

    // 比较和分析
    QStringList compareMetaData(const QJsonObject& meta1, const QJsonObject& meta2) const;
    QVariantMap analyzeMetaComplexity(const QJsonObject& meta) const;

    // 导出功能
    QString exportMetaToText(const QJsonObject& meta, const QString& format = "readable") const;
    bool exportMetaToFile(const QJsonObject& meta, const QString& filePath, const QString& format = "json") const;

signals:
    /**
     * @brief 元数据处理完成
     * @param result 处理结果
     */
    void metaProcessed(const QVariantMap& result);

    /**
     * @brief 元数据处理出错
     * @param errorMessage 错误信息
     */
    void metaProcessingError(const QString& errorMessage);

private:
    // 内部处理方法
    void buildTreeFromJson(const QJsonValue& value, QStandardItem* parentItem, const QString& key) const;
    QString formatJsonValue(const QJsonValue& value, int indentLevel = 0) const;
    QStringList extractTextFromJson(const QJsonValue& value) const;

    // 参数识别和提取
    QVariant findParameterValue(const QJsonObject& meta, const QStringList& possibleKeys) const;
    QString findStringParameter(const QJsonObject& meta, const QStringList& possibleKeys) const;
    int findIntParameter(const QJsonObject& meta, const QStringList& possibleKeys, int defaultValue = 0) const;
    double findDoubleParameter(const QJsonObject& meta, const QStringList& possibleKeys, double defaultValue = 0.0) const;

    // 特殊格式解析
    QVariantMap parseComfyUIWorkflow(const QJsonObject& meta) const;
    QVariantMap parseA1111Parameters(const QJsonObject& meta) const;
    QVariantMap parseNovelAIParameters(const QJsonObject& meta) const;

    // 文本处理工具
    QString cleanupParameterName(const QString& name) const;
    QString formatParameterValue(const QVariant& value) const;
    QStringList splitParameterString(const QString& paramString, const QString& separator = ",") const;

    // 验证工具
    bool isValidParameterName(const QString& name) const;
    bool isValidParameterValue(const QVariant& value) const;

private:
    // 已知参数映射
    QMap<QString, QStringList> m_parameterAliases;
    QStringList m_knownParameters;
    QStringList m_importantParameters;

    // 格式化选项
    bool m_useColorCoding;
    bool m_showParameterTypes;
    int m_maxTextLength;

    // 常量定义
    static const QStringList PROMPT_KEYS;
    static const QStringList NEGATIVE_PROMPT_KEYS;
    static const QStringList MODEL_KEYS;
    static const QStringList SAMPLER_KEYS;
    static const QStringList STEPS_KEYS;
    static const QStringList CFG_SCALE_KEYS;
    static const QStringList SEED_KEYS;
    static const QStringList WIDTH_KEYS;
    static const QStringList HEIGHT_KEYS;
    static const QStringList LORA_KEYS;
    static const QStringList EMBEDDING_KEYS;

    static const int MAX_TREE_DEPTH;
    static const int MAX_ARRAY_ITEMS_DISPLAY;
    static const int MAX_STRING_LENGTH_DISPLAY;
};

#endif // METADATAPROCESSOR_H
