#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include <QObject>
#include <QSettings>
#include <QVariant>
#include <QSize>
#include <QStringList>

/**
 * @brief 配置管理器类
 * 
 * 负责应用程序配置的读取、保存和管理
 * 使用QSettings进行持久化存储
 */
class ConfigManager : public QObject
{
    Q_OBJECT

public:
    explicit ConfigManager(QObject *parent = nullptr);
    ~ConfigManager();

    // 通用配置方法
    void setValue(const QString& key, const QVariant& value);
    QVariant getValue(const QString& key, const QVariant& defaultValue = QVariant()) const;
    bool contains(const QString& key) const;
    void removeKey(const QString& key);
    
    // API 配置
    void setApiKey(const QString& apiKey);
    QString apiKey() const;
    
    void setApiRateLimit(int requestsPerSecond);
    int apiRateLimit() const;
    
    void setApiRetryCount(int maxRetries);
    int apiRetryCount() const;
    
    // UI 配置
    void setWindowGeometry(const QByteArray& geometry);
    QByteArray windowGeometry() const;
    
    void setWindowState(const QByteArray& state);
    QByteArray windowState() const;
    
    void setSplitterState(const QByteArray& state);
    QByteArray splitterState() const;
    
    void setTheme(const QString& theme);
    QString theme() const;
    
    void setLanguage(const QString& language);
    QString language() const;
    
    // 搜索配置
    void setDefaultSort(const QString& sort);
    QString defaultSort() const;
    
    void setDefaultPeriod(const QString& period);
    QString defaultPeriod() const;
    
    void setDefaultNsfwLevel(const QString& nsfwLevel);
    QString defaultNsfwLevel() const;
    
    void setSearchHistory(const QStringList& history);
    QStringList searchHistory() const;
    void addToSearchHistory(const QString& query);
    void clearSearchHistory();
    
    // 图片显示配置
    void setImageCardSize(const QSize& size);
    QSize imageCardSize() const;
    
    void setGridColumns(int columns);
    int gridColumns() const;
    
    void setAutoLoadImages(bool autoLoad);
    bool autoLoadImages() const;
    
    void setImageQuality(int quality); // 0-100
    int imageQuality() const;
    
    // 缓存配置
    void setCacheEnabled(bool enabled);
    bool isCacheEnabled() const;
    
    void setMemoryCacheSize(int sizeKB);
    int memoryCacheSize() const;
    
    void setDiskCacheEnabled(bool enabled);
    bool isDiskCacheEnabled() const;
    
    void setDiskCacheDirectory(const QString& directory);
    QString diskCacheDirectory() const;
    
    void setDiskCacheMaxSize(qint64 sizeBytes);
    qint64 diskCacheMaxSize() const;
    
    void setMaxConcurrentDownloads(int maxDownloads);
    int maxConcurrentDownloads() const;
    
    // 网络配置
    void setDownloadTimeout(int timeoutMs);
    int downloadTimeout() const;
    
    void setProxyEnabled(bool enabled);
    bool isProxyEnabled() const;
    
    void setProxyHost(const QString& host);
    QString proxyHost() const;
    
    void setProxyPort(int port);
    int proxyPort() const;
    
    void setProxyUsername(const QString& username);
    QString proxyUsername() const;
    
    void setProxyPassword(const QString& password);
    QString proxyPassword() const;
    
    // 高级配置
    void setDebugMode(bool enabled);
    bool isDebugMode() const;
    
    void setLogLevel(int level);
    int logLevel() const;
    
    void setAutoCheckUpdates(bool enabled);
    bool autoCheckUpdates() const;
    
    void setLastUpdateCheck(const QDateTime& dateTime);
    QDateTime lastUpdateCheck() const;
    
    // 配置管理
    void resetToDefaults();
    void exportSettings(const QString& filePath) const;
    bool importSettings(const QString& filePath);
    
    // 配置验证
    bool validateApiKey(const QString& apiKey) const;
    bool validateCacheDirectory(const QString& directory) const;

signals:
    /**
     * @brief 配置值发生变化
     * @param key 配置键
     * @param value 新值
     */
    void configChanged(const QString& key, const QVariant& value);
    
    /**
     * @brief API密钥发生变化
     * @param apiKey 新的API密钥
     */
    void apiKeyChanged(const QString& apiKey);
    
    /**
     * @brief 主题发生变化
     * @param theme 新主题名称
     */
    void themeChanged(const QString& theme);

private:
    void initializeDefaults();
    void migrateOldSettings();
    QString encryptPassword(const QString& password) const;
    QString decryptPassword(const QString& encryptedPassword) const;
    
private:
    QSettings* m_settings;
    
    // 配置键常量
    static const QString KEY_API_KEY;
    static const QString KEY_API_RATE_LIMIT;
    static const QString KEY_API_RETRY_COUNT;
    
    static const QString KEY_WINDOW_GEOMETRY;
    static const QString KEY_WINDOW_STATE;
    static const QString KEY_SPLITTER_STATE;
    static const QString KEY_THEME;
    static const QString KEY_LANGUAGE;
    
    static const QString KEY_DEFAULT_SORT;
    static const QString KEY_DEFAULT_PERIOD;
    static const QString KEY_DEFAULT_NSFW_LEVEL;
    static const QString KEY_SEARCH_HISTORY;
    
    static const QString KEY_IMAGE_CARD_SIZE;
    static const QString KEY_GRID_COLUMNS;
    static const QString KEY_AUTO_LOAD_IMAGES;
    static const QString KEY_IMAGE_QUALITY;
    
    static const QString KEY_CACHE_ENABLED;
    static const QString KEY_MEMORY_CACHE_SIZE;
    static const QString KEY_DISK_CACHE_ENABLED;
    static const QString KEY_DISK_CACHE_DIRECTORY;
    static const QString KEY_DISK_CACHE_MAX_SIZE;
    static const QString KEY_MAX_CONCURRENT_DOWNLOADS;
    
    static const QString KEY_DOWNLOAD_TIMEOUT;
    static const QString KEY_PROXY_ENABLED;
    static const QString KEY_PROXY_HOST;
    static const QString KEY_PROXY_PORT;
    static const QString KEY_PROXY_USERNAME;
    static const QString KEY_PROXY_PASSWORD;
    
    static const QString KEY_DEBUG_MODE;
    static const QString KEY_LOG_LEVEL;
    static const QString KEY_AUTO_CHECK_UPDATES;
    static const QString KEY_LAST_UPDATE_CHECK;
    
    // 默认值
    static const int DEFAULT_API_RATE_LIMIT;
    static const int DEFAULT_API_RETRY_COUNT;
    static const QString DEFAULT_THEME;
    static const QString DEFAULT_LANGUAGE;
    static const QString DEFAULT_SORT;
    static const QString DEFAULT_PERIOD;
    static const QString DEFAULT_NSFW_LEVEL;
    static const QSize DEFAULT_IMAGE_CARD_SIZE;
    static const int DEFAULT_GRID_COLUMNS;
    static const int DEFAULT_IMAGE_QUALITY;
    static const int DEFAULT_MEMORY_CACHE_SIZE;
    static const qint64 DEFAULT_DISK_CACHE_MAX_SIZE;
    static const int DEFAULT_MAX_CONCURRENT_DOWNLOADS;
    static const int DEFAULT_DOWNLOAD_TIMEOUT;
    static const int DEFAULT_LOG_LEVEL;
};

#endif // CONFIGMANAGER_H
