@echo off
echo Building Civitai Image Viewer...

if exist build rmdir /s /q build
mkdir build
cd build

echo Configuring with CMake...
cmake .. -DCMAKE_BUILD_TYPE=Release

if %ERRORLEVEL% neq 0 (
    echo Trying Visual Studio 2019...
    cmake .. -DCMAKE_BUILD_TYPE=Release -G "Visual Studio 16 2019" -A x64
    
    if %ERRORLEVEL% neq 0 (
        echo Trying Visual Studio 2022...
        cmake .. -DCMAKE_BUILD_TYPE=Release -G "Visual Studio 17 2022" -A x64
        
        if %ERRORLEVEL% neq 0 (
            echo Configuration failed!
            pause
            exit /b 1
        )
    )
)

echo Building project...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build successful!
echo Looking for executable...
dir /s /b *.exe

echo Done!
pause
