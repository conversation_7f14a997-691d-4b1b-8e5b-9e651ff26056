{"classes": [{"className": "ElaToolTip", "lineNumber": 8, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pBorderRadius", "notify": "pBorderRadiusChanged", "read": "getBorderRadius", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBorderRadius"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pDisplayMsec", "notify": "pDisplayMsecChanged", "read": "getDisplayMsec", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setDisplayMsec"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "pShowDelayMsec", "notify": "pShowDelayMsecChanged", "read": "getShowDelayMsec", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setShowDelayMsec"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "pHideDelayMsec", "notify": "pHideDelayMsecChanged", "read": "getHideDelayMsec", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setHideDelayMsec"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "pToolTip", "notify": "pToolTipChanged", "read": "getToolTip", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setToolTip"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "pCustomWidget", "notify": "pCustomWidgetChanged", "read": "getCustomWidget", "required": false, "scriptable": true, "stored": true, "type": "QWidget*", "user": false, "write": "setCustomWidget"}], "qualifiedClassName": "ElaToolTip", "signals": [{"access": "public", "index": 0, "name": "pBorderRadiusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pDisplayMsecChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "pShowDelayMsecChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "pHideDelayMsecChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "pToolTipChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "pCustomWidgetChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "ElaToolTip.h", "outputRevision": 69}