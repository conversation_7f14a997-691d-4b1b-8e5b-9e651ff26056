/****************************************************************************
** Meta object code from reading C++ file 'ElaCalendar.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaCalendar.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaCalendar.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN11ElaCalendarE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaCalendar::qt_create_metaobjectdata<qt_meta_tag_ZN11ElaCalendarE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaCalendar",
        "pBorderRaiudsChanged",
        "",
        "pSelectedDateChanged",
        "pMinimumDateChanged",
        "pMaximumDateChanged",
        "clicked",
        "date",
        "pBorderRaiuds",
        "pSelectedDate",
        "pMinimumDate",
        "pMaximumDate"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pBorderRaiudsChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pSelectedDateChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pMinimumDateChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pMaximumDateChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'clicked'
        QtMocHelpers::SignalData<void(QDate)>(6, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QDate, 7 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pBorderRaiuds'
        QtMocHelpers::PropertyData<int>(8, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pSelectedDate'
        QtMocHelpers::PropertyData<QDate>(9, QMetaType::QDate, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pMinimumDate'
        QtMocHelpers::PropertyData<QDate>(10, QMetaType::QDate, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pMaximumDate'
        QtMocHelpers::PropertyData<QDate>(11, QMetaType::QDate, QMC::DefaultPropertyFlags | QMC::Writable, 3),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaCalendar, qt_meta_tag_ZN11ElaCalendarE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaCalendar::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11ElaCalendarE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11ElaCalendarE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN11ElaCalendarE_t>.metaTypes,
    nullptr
} };

void ElaCalendar::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaCalendar *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pBorderRaiudsChanged(); break;
        case 1: _t->pSelectedDateChanged(); break;
        case 2: _t->pMinimumDateChanged(); break;
        case 3: _t->pMaximumDateChanged(); break;
        case 4: _t->clicked((*reinterpret_cast< std::add_pointer_t<QDate>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaCalendar::*)()>(_a, &ElaCalendar::pBorderRaiudsChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaCalendar::*)()>(_a, &ElaCalendar::pSelectedDateChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaCalendar::*)()>(_a, &ElaCalendar::pMinimumDateChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaCalendar::*)()>(_a, &ElaCalendar::pMaximumDateChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaCalendar::*)(QDate )>(_a, &ElaCalendar::clicked, 4))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = _t->getBorderRaiuds(); break;
        case 1: *reinterpret_cast<QDate*>(_v) = _t->getSelectedDate(); break;
        case 2: *reinterpret_cast<QDate*>(_v) = _t->getMinimumDate(); break;
        case 3: *reinterpret_cast<QDate*>(_v) = _t->getMaximumDate(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setBorderRaiuds(*reinterpret_cast<int*>(_v)); break;
        case 1: _t->setSelectedDate(*reinterpret_cast<QDate*>(_v)); break;
        case 2: _t->setMinimumDate(*reinterpret_cast<QDate*>(_v)); break;
        case 3: _t->setMaximumDate(*reinterpret_cast<QDate*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaCalendar::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaCalendar::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11ElaCalendarE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ElaCalendar::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 5;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void ElaCalendar::pBorderRaiudsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaCalendar::pSelectedDateChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaCalendar::pMinimumDateChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaCalendar::pMaximumDateChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaCalendar::clicked(QDate _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}
QT_WARNING_POP
