{"classes": [{"className": "ElaThemeAnimationWidget", "lineNumber": 7, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "member": "_pRadius", "name": "pRadius", "notify": "pRadius<PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "member": "_pEndRadius", "name": "pEndRadius", "notify": "pEndRadiusChanged", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "member": "_pCenter", "name": "pCenter", "notify": "pCenterChanged", "required": false, "scriptable": true, "stored": true, "type": "QPoint", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "member": "_pOldWindowBackground", "name": "pOldWindowBackground", "notify": "pOldWindowBackgroundChanged", "required": false, "scriptable": true, "stored": true, "type": "QImage", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "member": "_pNewWindowBackground", "name": "pNewWindowBackground", "notify": "pNewWindowBackgroundChanged", "required": false, "scriptable": true, "stored": true, "type": "QImage", "user": false}], "qualifiedClassName": "ElaThemeAnimationWidget", "signals": [{"access": "public", "index": 0, "name": "pRadius<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 1, "name": "pEndRadiusChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "pCenterChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "pOldWindowBackgroundChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "pNewWindowBackgroundChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "animationFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "ElaThemeAnimationWidget.h", "outputRevision": 69}