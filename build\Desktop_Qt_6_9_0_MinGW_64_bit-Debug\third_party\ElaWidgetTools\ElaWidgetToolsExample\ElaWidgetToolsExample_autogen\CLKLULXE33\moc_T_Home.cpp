/****************************************************************************
** Meta object code from reading C++ file 'T_Home.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetToolsExample/ExamplePage/T_Home.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'T_Home.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN6T_HomeE_t {};
} // unnamed namespace

template <> constexpr inline auto T_Home::qt_create_metaobjectdata<qt_meta_tag_ZN6T_HomeE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "T_Home",
        "elaScreenNavigation",
        "",
        "elaBaseComponentNavigation",
        "elaSceneNavigation",
        "elaCardNavigation",
        "elaIconNavigation",
        "QWidget*",
        "parent"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'elaScreenNavigation'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'elaBaseComponentNavigation'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'elaSceneNavigation'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'elaCardNavigation'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'elaIconNavigation'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    using Constructor = QtMocHelpers::NoType;
    QtMocHelpers::UintData qt_constructors {
        QtMocHelpers::ConstructorData<Constructor(QWidget *)>(2, QMC::AccessPublic, {{
            { 0x80000000 | 7, 8 },
        }}),
        QtMocHelpers::ConstructorData<Constructor()>(2, QMC::AccessPublic | QMC::MethodCloned),
    };
    return QtMocHelpers::metaObjectData<T_Home, qt_meta_tag_ZN6T_HomeE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums, qt_constructors);
}
Q_CONSTINIT const QMetaObject T_Home::staticMetaObject = { {
    QMetaObject::SuperData::link<T_BasePage::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN6T_HomeE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN6T_HomeE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN6T_HomeE_t>.metaTypes,
    nullptr
} };

void T_Home::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<T_Home *>(_o);
    if (_c == QMetaObject::CreateInstance) {
        switch (_id) {
        case 0: { T_Home *_r = new T_Home((*reinterpret_cast<std::add_pointer_t<QWidget*>>(_a[1])));
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        case 1: { T_Home *_r = new T_Home();
            if (_a[0]) *reinterpret_cast<QObject**>(_a[0]) = _r; } break;
        default: break;
        }
    }
    if (_c == QMetaObject::ConstructInPlace) {
        switch (_id) {
        case 0: { new (_a[0]) T_Home((*reinterpret_cast<std::add_pointer_t<QWidget*>>(_a[1]))); } break;
        case 1: { new (_a[0]) T_Home(); } break;
        default: break;
        }
    }
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->elaScreenNavigation(); break;
        case 1: _t->elaBaseComponentNavigation(); break;
        case 2: _t->elaSceneNavigation(); break;
        case 3: _t->elaCardNavigation(); break;
        case 4: _t->elaIconNavigation(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (T_Home::*)()>(_a, &T_Home::elaScreenNavigation, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (T_Home::*)()>(_a, &T_Home::elaBaseComponentNavigation, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (T_Home::*)()>(_a, &T_Home::elaSceneNavigation, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (T_Home::*)()>(_a, &T_Home::elaCardNavigation, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (T_Home::*)()>(_a, &T_Home::elaIconNavigation, 4))
            return;
    }
}

const QMetaObject *T_Home::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *T_Home::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN6T_HomeE_t>.strings))
        return static_cast<void*>(this);
    return T_BasePage::qt_metacast(_clname);
}

int T_Home::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = T_BasePage::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 5;
    }
    return _id;
}

// SIGNAL 0
void T_Home::elaScreenNavigation()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void T_Home::elaBaseComponentNavigation()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void T_Home::elaSceneNavigation()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void T_Home::elaCardNavigation()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void T_Home::elaIconNavigation()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}
QT_WARNING_POP
