#include "CivitaiImageInfo.h"
#include <QJsonDocument>
#include <QJsonArray>
#include <QStandardItemModel>
#include <QStandardItem>
#include <QDebug>
#include <functional>

// CivitaiImageStats 实现
CivitaiImageStats::CivitaiImageStats(const QJsonObject& statsJson)
{
    cryCount = statsJson.value("cryCount").toInt(0);
    laughCount = statsJson.value("laughCount").toInt(0);
    likeCount = statsJson.value("likeCount").toInt(0);
    heartCount = statsJson.value("heartCount").toInt(0);
    commentCount = statsJson.value("commentCount").toInt(0);
}

QJsonObject CivitaiImageStats::toJson() const
{
    QJsonObject obj;
    obj["cryCount"] = cryCount;
    obj["laughCount"] = laughCount;
    obj["likeCount"] = likeCount;
    obj["heartCount"] = heartCount;
    obj["commentCount"] = commentCount;
    return obj;
}

// CivitaiImageInfo 实现
CivitaiImageInfo::CivitaiImageInfo()
{
    // 默认构造函数，所有成员变量已在头文件中初始化
}

CivitaiImageInfo::CivitaiImageInfo(const QJsonObject& apiResponse)
{
    parseFromApiResponse(apiResponse);
}

CivitaiImageInfo::CivitaiImageInfo(const CivitaiImageInfo& other)
{
    copyFrom(other);
}

CivitaiImageInfo& CivitaiImageInfo::operator=(const CivitaiImageInfo& other)
{
    if (this != &other) {
        copyFrom(other);
    }
    return *this;
}

CivitaiImageInfo::CivitaiImageInfo(CivitaiImageInfo&& other) noexcept
{
    moveFrom(std::move(other));
}

CivitaiImageInfo& CivitaiImageInfo::operator=(CivitaiImageInfo&& other) noexcept
{
    if (this != &other) {
        moveFrom(std::move(other));
    }
    return *this;
}

CivitaiImageInfo::~CivitaiImageInfo()
{
    // 析构函数，Qt对象会自动管理内存
}

void CivitaiImageInfo::setMetaData(const QJsonObject& meta)
{
    m_metaData = meta;
    updateFormattedMetaString();
}

QString CivitaiImageInfo::displayTitle() const
{
    if (!m_authorUsername.isEmpty()) {
        return QString("Image by %1").arg(m_authorUsername);
    }
    return QString("Image #%1").arg(m_id);
}

QString CivitaiImageInfo::sizeString() const
{
    if (m_width > 0 && m_height > 0) {
        return QString("%1 x %2").arg(m_width).arg(m_height);
    }
    return QString();
}

void CivitaiImageInfo::parseFromApiResponse(const QJsonObject& apiResponse)
{
    m_id = apiResponse.value("id").toInt(0);
    m_url = apiResponse.value("url").toString();
    m_width = apiResponse.value("width").toInt(0);
    m_height = apiResponse.value("height").toInt(0);
    m_nsfwLevel = apiResponse.value("nsfwLevel").toString();
    m_nsfw = apiResponse.value("nsfw").toBool(false);
    m_postId = apiResponse.value("postId").toInt(0);
    m_authorUsername = apiResponse.value("username").toString();

    // 解析创建时间
    QString createdAtStr = apiResponse.value("createdAt").toString();
    if (!createdAtStr.isEmpty()) {
        m_createdAt = QDateTime::fromString(createdAtStr, Qt::ISODate);
    }

    // 解析统计信息
    QJsonObject statsObj = apiResponse.value("stats").toObject();
    if (!statsObj.isEmpty()) {
        m_stats = CivitaiImageStats(statsObj);
    }

    // 解析元数据
    QJsonObject metaObj = apiResponse.value("meta").toObject();
    if (!metaObj.isEmpty()) {
        setMetaData(metaObj);
    }

    // 生成缩略图URL（如果API没有提供的话）
    if (m_thumbnailUrl.isEmpty() && !m_url.isEmpty()) {
        m_thumbnailUrl = m_url; // 暂时使用原图URL，后续可以优化
    }
}

QJsonObject CivitaiImageInfo::toJson() const
{
    QJsonObject obj;
    obj["id"] = m_id;
    obj["url"] = m_url;
    obj["thumbnailUrl"] = m_thumbnailUrl;
    obj["width"] = m_width;
    obj["height"] = m_height;
    obj["nsfwLevel"] = m_nsfwLevel;
    obj["nsfw"] = m_nsfw;
    obj["createdAt"] = m_createdAt.toString(Qt::ISODate);
    obj["postId"] = m_postId;
    obj["username"] = m_authorUsername;
    obj["stats"] = m_stats.toJson();
    obj["meta"] = m_metaData;
    return obj;
}

void CivitaiImageInfo::updateFormattedMetaString()
{
    if (m_metaData.isEmpty()) {
        m_formattedMetaJsonString.clear();
        return;
    }

    QJsonDocument doc(m_metaData);
    m_formattedMetaJsonString = doc.toJson(QJsonDocument::Indented);
}

void CivitaiImageInfo::copyFrom(const CivitaiImageInfo& other)
{
    m_id = other.m_id;
    m_url = other.m_url;
    m_thumbnailUrl = other.m_thumbnailUrl;
    m_width = other.m_width;
    m_height = other.m_height;
    m_nsfwLevel = other.m_nsfwLevel;
    m_nsfw = other.m_nsfw;
    m_createdAt = other.m_createdAt;
    m_postId = other.m_postId;
    m_authorUsername = other.m_authorUsername;
    m_stats = other.m_stats;
    m_previewPixmap = other.m_previewPixmap;
    m_metaData = other.m_metaData;
    m_formattedMetaJsonString = other.m_formattedMetaJsonString;
}

void CivitaiImageInfo::moveFrom(CivitaiImageInfo&& other) noexcept
{
    m_id = other.m_id;
    m_url = std::move(other.m_url);
    m_thumbnailUrl = std::move(other.m_thumbnailUrl);
    m_width = other.m_width;
    m_height = other.m_height;
    m_nsfwLevel = std::move(other.m_nsfwLevel);
    m_nsfw = other.m_nsfw;
    m_createdAt = std::move(other.m_createdAt);
    m_postId = other.m_postId;
    m_authorUsername = std::move(other.m_authorUsername);
    m_stats = std::move(other.m_stats);
    m_previewPixmap = std::move(other.m_previewPixmap);
    m_metaData = std::move(other.m_metaData);
    m_formattedMetaJsonString = std::move(other.m_formattedMetaJsonString);

    // 重置other对象
    other.m_id = 0;
    other.m_width = 0;
    other.m_height = 0;
    other.m_nsfw = false;
    other.m_postId = 0;
}

QAbstractItemModel* CivitaiImageInfo::createMetaTreeModel(QObject* parent) const
{
    auto* model = new QStandardItemModel(parent);
    model->setHorizontalHeaderLabels({"参数名", "参数值"});

    if (m_metaData.isEmpty()) {
        return model;
    }

    // 递归函数，用于构建树结构
    std::function<void(const QJsonValue&, QStandardItem*, const QString&)> buildTree;
    buildTree = [&](const QJsonValue& value, QStandardItem* parentItem, const QString& key) {
        auto* keyItem = new QStandardItem(key);
        auto* valueItem = new QStandardItem();

        switch (value.type()) {
        case QJsonValue::Object: {
            valueItem->setText("{...}");
            parentItem->appendRow({keyItem, valueItem});

            QJsonObject obj = value.toObject();
            for (auto it = obj.begin(); it != obj.end(); ++it) {
                buildTree(it.value(), keyItem, it.key());
            }
            break;
        }
        case QJsonValue::Array: {
            QJsonArray arr = value.toArray();
            valueItem->setText(QString("[%1 items]").arg(arr.size()));
            parentItem->appendRow({keyItem, valueItem});

            for (int i = 0; i < arr.size(); ++i) {
                buildTree(arr[i], keyItem, QString("[%1]").arg(i));
            }
            break;
        }
        case QJsonValue::String:
            valueItem->setText(value.toString());
            parentItem->appendRow({keyItem, valueItem});
            break;
        case QJsonValue::Double:
            valueItem->setText(QString::number(value.toDouble()));
            parentItem->appendRow({keyItem, valueItem});
            break;
        case QJsonValue::Bool:
            valueItem->setText(value.toBool() ? "true" : "false");
            parentItem->appendRow({keyItem, valueItem});
            break;
        case QJsonValue::Null:
            valueItem->setText("null");
            parentItem->appendRow({keyItem, valueItem});
            break;
        default:
            valueItem->setText(value.toVariant().toString());
            parentItem->appendRow({keyItem, valueItem});
            break;
        }
    };

    // 构建根级别的项目
    QStandardItem* rootItem = model->invisibleRootItem();
    for (auto it = m_metaData.begin(); it != m_metaData.end(); ++it) {
        buildTree(it.value(), rootItem, it.key());
    }

    return model;
}
