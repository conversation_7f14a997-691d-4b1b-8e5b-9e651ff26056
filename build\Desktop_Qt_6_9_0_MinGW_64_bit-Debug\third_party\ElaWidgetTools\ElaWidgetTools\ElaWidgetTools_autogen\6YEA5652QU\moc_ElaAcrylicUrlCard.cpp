/****************************************************************************
** Meta object code from reading C++ file 'ElaAcrylicUrlCard.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaAcrylicUrlCard.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaAcrylicUrlCard.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN17ElaAcrylicUrlCardE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaAcrylicUrlCard::qt_create_metaobjectdata<qt_meta_tag_ZN17ElaAcrylicUrlCardE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaAcrylicUrlCard",
        "pBorderRadiusChanged",
        "",
        "pMainOpacityChanged",
        "pNoiseOpacityChanged",
        "pBrushAlphaChanged",
        "pTitleChanged",
        "pSubTitleChanged",
        "pTitlePixelSizeChanged",
        "pSubTitlePixelSizeChanged",
        "pTitleSpacingChanged",
        "pSubTitleSpacingChanged",
        "pCardPixmapChanged",
        "pCardPixmapSizeChanged",
        "pCardPixmapBorderRadiusChanged",
        "pCardPixModeChanged",
        "pUrlChanged",
        "pBorderRadius",
        "pMainOpacity",
        "pNoiseOpacity",
        "pBrushAlpha",
        "pTitle",
        "pSubTitle",
        "pTitlePixelSize",
        "pSubTitlePixelSize",
        "pTitleSpacing",
        "pSubTitleSpacing",
        "pCardPixmap",
        "pCardPixmapSize",
        "pCardPixmapBorderRadius",
        "pCardPixMode",
        "ElaCardPixType::PixMode",
        "pUrl"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pBorderRadiusChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pMainOpacityChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pNoiseOpacityChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pBrushAlphaChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pTitleChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pSubTitleChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pTitlePixelSizeChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pSubTitlePixelSizeChanged'
        QtMocHelpers::SignalData<void()>(9, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pTitleSpacingChanged'
        QtMocHelpers::SignalData<void()>(10, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pSubTitleSpacingChanged'
        QtMocHelpers::SignalData<void()>(11, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCardPixmapChanged'
        QtMocHelpers::SignalData<void()>(12, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCardPixmapSizeChanged'
        QtMocHelpers::SignalData<void()>(13, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCardPixmapBorderRadiusChanged'
        QtMocHelpers::SignalData<void()>(14, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCardPixModeChanged'
        QtMocHelpers::SignalData<void()>(15, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pUrlChanged'
        QtMocHelpers::SignalData<void()>(16, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pBorderRadius'
        QtMocHelpers::PropertyData<int>(17, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pMainOpacity'
        QtMocHelpers::PropertyData<qreal>(18, QMetaType::QReal, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pNoiseOpacity'
        QtMocHelpers::PropertyData<qreal>(19, QMetaType::QReal, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pBrushAlpha'
        QtMocHelpers::PropertyData<int>(20, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 3),
        // property 'pTitle'
        QtMocHelpers::PropertyData<QString>(21, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 4),
        // property 'pSubTitle'
        QtMocHelpers::PropertyData<QString>(22, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 5),
        // property 'pTitlePixelSize'
        QtMocHelpers::PropertyData<int>(23, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 6),
        // property 'pSubTitlePixelSize'
        QtMocHelpers::PropertyData<int>(24, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 7),
        // property 'pTitleSpacing'
        QtMocHelpers::PropertyData<int>(25, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 8),
        // property 'pSubTitleSpacing'
        QtMocHelpers::PropertyData<int>(26, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 9),
        // property 'pCardPixmap'
        QtMocHelpers::PropertyData<QPixmap>(27, QMetaType::QPixmap, QMC::DefaultPropertyFlags | QMC::Writable, 10),
        // property 'pCardPixmapSize'
        QtMocHelpers::PropertyData<QSize>(28, QMetaType::QSize, QMC::DefaultPropertyFlags | QMC::Writable, 11),
        // property 'pCardPixmapBorderRadius'
        QtMocHelpers::PropertyData<int>(29, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 12),
        // property 'pCardPixMode'
        QtMocHelpers::PropertyData<ElaCardPixType::PixMode>(30, 0x80000000 | 31, QMC::DefaultPropertyFlags | QMC::Writable | QMC::EnumOrFlag, 13),
        // property 'pUrl'
        QtMocHelpers::PropertyData<QString>(32, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 14),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaAcrylicUrlCard, qt_meta_tag_ZN17ElaAcrylicUrlCardE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT static const QMetaObject::SuperData qt_meta_extradata_ZN17ElaAcrylicUrlCardE[] = {
    QMetaObject::SuperData::link<ElaCardPixType::staticMetaObject>(),
    nullptr
};

Q_CONSTINIT const QMetaObject ElaAcrylicUrlCard::staticMetaObject = { {
    QMetaObject::SuperData::link<QPushButton::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN17ElaAcrylicUrlCardE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN17ElaAcrylicUrlCardE_t>.data,
    qt_static_metacall,
    qt_meta_extradata_ZN17ElaAcrylicUrlCardE,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN17ElaAcrylicUrlCardE_t>.metaTypes,
    nullptr
} };

void ElaAcrylicUrlCard::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaAcrylicUrlCard *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pBorderRadiusChanged(); break;
        case 1: _t->pMainOpacityChanged(); break;
        case 2: _t->pNoiseOpacityChanged(); break;
        case 3: _t->pBrushAlphaChanged(); break;
        case 4: _t->pTitleChanged(); break;
        case 5: _t->pSubTitleChanged(); break;
        case 6: _t->pTitlePixelSizeChanged(); break;
        case 7: _t->pSubTitlePixelSizeChanged(); break;
        case 8: _t->pTitleSpacingChanged(); break;
        case 9: _t->pSubTitleSpacingChanged(); break;
        case 10: _t->pCardPixmapChanged(); break;
        case 11: _t->pCardPixmapSizeChanged(); break;
        case 12: _t->pCardPixmapBorderRadiusChanged(); break;
        case 13: _t->pCardPixModeChanged(); break;
        case 14: _t->pUrlChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaAcrylicUrlCard::*)()>(_a, &ElaAcrylicUrlCard::pBorderRadiusChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAcrylicUrlCard::*)()>(_a, &ElaAcrylicUrlCard::pMainOpacityChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAcrylicUrlCard::*)()>(_a, &ElaAcrylicUrlCard::pNoiseOpacityChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAcrylicUrlCard::*)()>(_a, &ElaAcrylicUrlCard::pBrushAlphaChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAcrylicUrlCard::*)()>(_a, &ElaAcrylicUrlCard::pTitleChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAcrylicUrlCard::*)()>(_a, &ElaAcrylicUrlCard::pSubTitleChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAcrylicUrlCard::*)()>(_a, &ElaAcrylicUrlCard::pTitlePixelSizeChanged, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAcrylicUrlCard::*)()>(_a, &ElaAcrylicUrlCard::pSubTitlePixelSizeChanged, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAcrylicUrlCard::*)()>(_a, &ElaAcrylicUrlCard::pTitleSpacingChanged, 8))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAcrylicUrlCard::*)()>(_a, &ElaAcrylicUrlCard::pSubTitleSpacingChanged, 9))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAcrylicUrlCard::*)()>(_a, &ElaAcrylicUrlCard::pCardPixmapChanged, 10))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAcrylicUrlCard::*)()>(_a, &ElaAcrylicUrlCard::pCardPixmapSizeChanged, 11))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAcrylicUrlCard::*)()>(_a, &ElaAcrylicUrlCard::pCardPixmapBorderRadiusChanged, 12))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAcrylicUrlCard::*)()>(_a, &ElaAcrylicUrlCard::pCardPixModeChanged, 13))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaAcrylicUrlCard::*)()>(_a, &ElaAcrylicUrlCard::pUrlChanged, 14))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = _t->getBorderRadius(); break;
        case 1: *reinterpret_cast<qreal*>(_v) = _t->getMainOpacity(); break;
        case 2: *reinterpret_cast<qreal*>(_v) = _t->getNoiseOpacity(); break;
        case 3: *reinterpret_cast<int*>(_v) = _t->getBrushAlpha(); break;
        case 4: *reinterpret_cast<QString*>(_v) = _t->getTitle(); break;
        case 5: *reinterpret_cast<QString*>(_v) = _t->getSubTitle(); break;
        case 6: *reinterpret_cast<int*>(_v) = _t->getTitlePixelSize(); break;
        case 7: *reinterpret_cast<int*>(_v) = _t->getSubTitlePixelSize(); break;
        case 8: *reinterpret_cast<int*>(_v) = _t->getTitleSpacing(); break;
        case 9: *reinterpret_cast<int*>(_v) = _t->getSubTitleSpacing(); break;
        case 10: *reinterpret_cast<QPixmap*>(_v) = _t->getCardPixmap(); break;
        case 11: *reinterpret_cast<QSize*>(_v) = _t->getCardPixmapSize(); break;
        case 12: *reinterpret_cast<int*>(_v) = _t->getCardPixmapBorderRadius(); break;
        case 13: *reinterpret_cast<ElaCardPixType::PixMode*>(_v) = _t->getCardPixMode(); break;
        case 14: *reinterpret_cast<QString*>(_v) = _t->getUrl(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setBorderRadius(*reinterpret_cast<int*>(_v)); break;
        case 1: _t->setMainOpacity(*reinterpret_cast<qreal*>(_v)); break;
        case 2: _t->setNoiseOpacity(*reinterpret_cast<qreal*>(_v)); break;
        case 3: _t->setBrushAlpha(*reinterpret_cast<int*>(_v)); break;
        case 4: _t->setTitle(*reinterpret_cast<QString*>(_v)); break;
        case 5: _t->setSubTitle(*reinterpret_cast<QString*>(_v)); break;
        case 6: _t->setTitlePixelSize(*reinterpret_cast<int*>(_v)); break;
        case 7: _t->setSubTitlePixelSize(*reinterpret_cast<int*>(_v)); break;
        case 8: _t->setTitleSpacing(*reinterpret_cast<int*>(_v)); break;
        case 9: _t->setSubTitleSpacing(*reinterpret_cast<int*>(_v)); break;
        case 10: _t->setCardPixmap(*reinterpret_cast<QPixmap*>(_v)); break;
        case 11: _t->setCardPixmapSize(*reinterpret_cast<QSize*>(_v)); break;
        case 12: _t->setCardPixmapBorderRadius(*reinterpret_cast<int*>(_v)); break;
        case 13: _t->setCardPixMode(*reinterpret_cast<ElaCardPixType::PixMode*>(_v)); break;
        case 14: _t->setUrl(*reinterpret_cast<QString*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaAcrylicUrlCard::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaAcrylicUrlCard::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN17ElaAcrylicUrlCardE_t>.strings))
        return static_cast<void*>(this);
    return QPushButton::qt_metacast(_clname);
}

int ElaAcrylicUrlCard::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QPushButton::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 15)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 15;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 15)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 15;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 15;
    }
    return _id;
}

// SIGNAL 0
void ElaAcrylicUrlCard::pBorderRadiusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaAcrylicUrlCard::pMainOpacityChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaAcrylicUrlCard::pNoiseOpacityChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaAcrylicUrlCard::pBrushAlphaChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaAcrylicUrlCard::pTitleChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ElaAcrylicUrlCard::pSubTitleChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void ElaAcrylicUrlCard::pTitlePixelSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void ElaAcrylicUrlCard::pSubTitlePixelSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}

// SIGNAL 8
void ElaAcrylicUrlCard::pTitleSpacingChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 8, nullptr);
}

// SIGNAL 9
void ElaAcrylicUrlCard::pSubTitleSpacingChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 9, nullptr);
}

// SIGNAL 10
void ElaAcrylicUrlCard::pCardPixmapChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 10, nullptr);
}

// SIGNAL 11
void ElaAcrylicUrlCard::pCardPixmapSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 11, nullptr);
}

// SIGNAL 12
void ElaAcrylicUrlCard::pCardPixmapBorderRadiusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 12, nullptr);
}

// SIGNAL 13
void ElaAcrylicUrlCard::pCardPixModeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 13, nullptr);
}

// SIGNAL 14
void ElaAcrylicUrlCard::pUrlChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 14, nullptr);
}
QT_WARNING_POP
