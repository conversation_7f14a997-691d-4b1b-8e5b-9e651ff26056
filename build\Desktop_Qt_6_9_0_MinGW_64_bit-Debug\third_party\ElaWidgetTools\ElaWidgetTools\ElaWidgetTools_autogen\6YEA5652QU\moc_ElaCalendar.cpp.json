{"classes": [{"className": "ElaCalendar", "lineNumber": 8, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pBorderRaiuds", "notify": "pBorderRaiudsChanged", "read": "getBorderRaiuds", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBorderRaiuds"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pSelectedDate", "notify": "pSelectedDateChanged", "read": "getSelectedDate", "required": false, "scriptable": true, "stored": true, "type": "QDate", "user": false, "write": "setSelectedDate"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "pMinimumDate", "notify": "pMinimumDateChanged", "read": "getMinimumDate", "required": false, "scriptable": true, "stored": true, "type": "QDate", "user": false, "write": "setMinimumDate"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "pMaximumDate", "notify": "pMaximumDateChanged", "read": "getMaximumDate", "required": false, "scriptable": true, "stored": true, "type": "QDate", "user": false, "write": "setMaximumDate"}], "qualifiedClassName": "ElaCalendar", "signals": [{"access": "public", "index": 0, "name": "pBorderRaiudsChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pSelectedDateChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "pMinimumDateChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "pMaximumDateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "date", "type": "QDate"}], "index": 4, "name": "clicked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "ElaCalendar.h", "outputRevision": 69}