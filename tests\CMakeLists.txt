# 测试配置文件

# 启用测试
enable_testing()

# 查找Qt Test模块
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Test)

# 包含源代码目录
include_directories(${CMAKE_SOURCE_DIR}/src)

# 测试源文件列表
set(TEST_SOURCES
    test_civitai_image_info.cpp
)

# 需要链接的源文件（不包括main.cpp）
set(MAIN_SOURCES
    ${CMAKE_SOURCE_DIR}/src/CivitaiImageInfo.cpp
    ${CMAKE_SOURCE_DIR}/src/CivitaiClient.cpp
    ${CMAKE_SOURCE_DIR}/src/ImageManager.cpp
    ${CMAKE_SOURCE_DIR}/src/ConfigManager.cpp
    ${CMAKE_SOURCE_DIR}/src/MetaDataProcessor.cpp
)

# 创建测试可执行文件
foreach(TEST_SOURCE ${TEST_SOURCES})
    # 获取测试名称（去掉扩展名）
    get_filename_component(TEST_NAME ${TEST_SOURCE} NAME_WE)
    
    # 创建测试可执行文件
    add_executable(${TEST_NAME} ${TEST_SOURCE} ${MAIN_SOURCES})
    
    # 链接Qt库
    target_link_libraries(${TEST_NAME}
        Qt${QT_VERSION_MAJOR}::Core
        Qt${QT_VERSION_MAJOR}::Widgets
        Qt${QT_VERSION_MAJOR}::Network
        Qt${QT_VERSION_MAJOR}::Test
    )
    
    # 链接ElaWidgetTools（如果可用）
    if(ELAWIDGETTOOLS_AVAILABLE)
        target_link_libraries(${TEST_NAME} ElaWidgetTools)
        target_compile_definitions(${TEST_NAME} PRIVATE ELAWIDGETTOOLS_AVAILABLE)
    endif()
    
    # 设置编译选项
    if(MSVC)
        target_compile_options(${TEST_NAME} PRIVATE /utf-8)
    endif()
    
    # 添加到测试套件
    add_test(NAME ${TEST_NAME} COMMAND ${TEST_NAME})
    
    # 设置测试属性
    set_tests_properties(${TEST_NAME} PROPERTIES
        TIMEOUT 30
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    )
endforeach()

# 创建一个运行所有测试的目标
add_custom_target(run_tests
    COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure
    DEPENDS ${TEST_SOURCES}
    COMMENT "Running all tests"
)
