# Civitai 图片查看器测试指南

## 🎯 测试目标

本指南将帮助您全面测试 Civitai 图片查看器的所有功能，确保应用程序正常工作。

## 📋 完整功能清单

### 核心功能模块

#### 1. **应用程序启动和初始化**
- [ ] 应用程序正常启动
- [ ] 主窗口正确显示
- [ ] 菜单栏和工具栏加载
- [ ] 状态栏显示"就绪"

#### 2. **API 连接和配置**
- [ ] 设置对话框打开
- [ ] API 密钥输入和保存
- [ ] API 连接测试功能
- [ ] 配置参数验证

#### 3. **图片搜索功能**
- [ ] 关键词搜索
- [ ] 排序选项切换
- [ ] 时间范围筛选
- [ ] NSFW 等级设置
- [ ] 搜索结果显示

#### 4. **图片显示功能**
- [ ] 图片卡片正确显示
- [ ] 图片异步加载
- [ ] 缩略图显示
- [ ] 图片信息展示
- [ ] 加载状态指示

#### 5. **元数据查看功能**
- [ ] 点击图片显示元数据
- [ ] JSON 文本格式显示
- [ ] 树状结构视图
- [ ] 元数据内容正确性
- [ ] 视图切换功能

#### 6. **分页浏览功能**
- [ ] 下一页按钮功能
- [ ] 上一页按钮功能
- [ ] 页码显示正确
- [ ] 分页状态更新

#### 7. **缓存系统功能**
- [ ] 图片缓存到内存
- [ ] 图片缓存到磁盘
- [ ] 缓存大小控制
- [ ] 缓存清理功能

#### 8. **设置管理功能**
- [ ] 设置保存和加载
- [ ] 主题切换
- [ ] 配置导入/导出
- [ ] 默认设置恢复

## 🧪 详细测试步骤

### 第一阶段：环境准备

#### 1. 构建项目
```bash
# Windows
build.bat

# Linux/macOS
./build.sh
```

#### 2. 获取 API 密钥
1. 访问 https://civitai.com
2. 注册/登录账户
3. 进入账户设置页面
4. 生成 API 密钥
5. 复制密钥备用

### 第二阶段：基础功能测试

#### 测试 1: 应用程序启动
**步骤:**
1. 运行可执行文件
2. 观察启动过程
3. 检查主窗口显示

**预期结果:**
- 应用程序正常启动
- 主窗口完整显示
- 无错误提示

#### 测试 2: API 配置
**步骤:**
1. 点击菜单 "文件" → "设置"
2. 切换到 "API" 选项卡
3. 输入 API 密钥
4. 点击 "测试连接"
5. 保存设置

**预期结果:**
- 设置对话框正常打开
- API 密钥输入正常
- 连接测试显示成功
- 设置保存成功

#### 测试 3: 基础搜索
**步骤:**
1. 在搜索框输入 "landscape"
2. 点击搜索按钮
3. 等待结果加载

**预期结果:**
- 搜索请求发送成功
- 图片卡片开始显示
- 状态栏显示加载进度

### 第三阶段：核心功能测试

#### 测试 4: 图片浏览
**步骤:**
1. 观察图片卡片显示
2. 检查图片信息
3. 测试图片加载

**预期结果:**
- 图片卡片正确布局
- 图片异步加载显示
- 基本信息正确显示

#### 测试 5: 元数据查看
**步骤:**
1. 点击任意图片卡片
2. 查看右侧元数据面板
3. 切换 JSON 和树状视图

**预期结果:**
- 元数据面板更新
- JSON 格式正确显示
- 树状视图正常展开

#### 测试 6: 搜索筛选
**步骤:**
1. 更改排序方式为 "Newest"
2. 设置时间范围为 "Month"
3. 重新搜索

**预期结果:**
- 搜索参数正确应用
- 结果按新条件显示
- 分页信息更新

#### 测试 7: 分页功能
**步骤:**
1. 点击 "下一页" 按钮
2. 观察页面变化
3. 点击 "上一页" 按钮

**预期结果:**
- 页面正确切换
- 新图片正确加载
- 页码信息正确

### 第四阶段：高级功能测试

#### 测试 8: 缓存系统
**步骤:**
1. 进入设置 → 缓存
2. 查看缓存使用情况
3. 测试缓存清理

**预期结果:**
- 缓存信息正确显示
- 清理功能正常工作
- 缓存设置生效

#### 测试 9: 主题切换
**步骤:**
1. 点击菜单 "视图" → "切换主题"
2. 观察界面变化

**预期结果:**
- 主题正确切换
- 界面颜色改变
- 设置保存成功

#### 测试 10: 设置管理
**步骤:**
1. 修改各种设置
2. 导出设置到文件
3. 恢复默认设置
4. 导入之前的设置

**预期结果:**
- 设置修改生效
- 导出/导入正常
- 默认设置恢复

### 第五阶段：错误处理测试

#### 测试 11: 网络错误处理
**步骤:**
1. 断开网络连接
2. 尝试搜索图片
3. 观察错误处理

**预期结果:**
- 显示网络错误提示
- 应用程序不崩溃
- 重连后恢复正常

#### 测试 12: 无效 API 密钥
**步骤:**
1. 输入无效的 API 密钥
2. 尝试搜索
3. 观察错误提示

**预期结果:**
- 显示认证错误
- 提示检查 API 密钥
- 不影响其他功能

## 📊 测试检查清单

### 基础功能 ✅
- [ ] 应用程序启动
- [ ] 主窗口显示
- [ ] 菜单和工具栏
- [ ] 设置对话框

### 搜索功能 ✅
- [ ] 关键词搜索
- [ ] 排序筛选
- [ ] 时间范围
- [ ] NSFW 设置

### 显示功能 ✅
- [ ] 图片卡片
- [ ] 图片加载
- [ ] 信息显示
- [ ] 布局正确

### 元数据功能 ✅
- [ ] 元数据显示
- [ ] JSON 格式
- [ ] 树状视图
- [ ] 视图切换

### 分页功能 ✅
- [ ] 下一页
- [ ] 上一页
- [ ] 页码显示
- [ ] 状态更新

### 缓存功能 ✅
- [ ] 内存缓存
- [ ] 磁盘缓存
- [ ] 缓存管理
- [ ] 清理功能

### 设置功能 ✅
- [ ] 配置保存
- [ ] 主题切换
- [ ] 导入导出
- [ ] 默认恢复

### 错误处理 ✅
- [ ] 网络错误
- [ ] API 错误
- [ ] 输入验证
- [ ] 异常处理

## 🐛 常见问题和解决方案

### 问题 1: 应用程序无法启动
**可能原因:**
- Qt 运行时库缺失
- ElaWidgetTools 未正确编译

**解决方案:**
- 安装 Qt 6.2+ 运行时
- 重新编译 ElaWidgetTools

### 问题 2: API 连接失败
**可能原因:**
- API 密钥无效
- 网络连接问题
- 防火墙阻止

**解决方案:**
- 检查 API 密钥
- 测试网络连接
- 配置防火墙规则

### 问题 3: 图片加载缓慢
**可能原因:**
- 网络速度慢
- 并发数设置过低
- 缓存未启用

**解决方案:**
- 增加并发下载数
- 启用磁盘缓存
- 检查网络状况

## 📈 性能测试建议

### 内存使用测试
- 长时间运行观察内存使用
- 大量图片加载测试
- 缓存清理效果验证

### 网络性能测试
- 不同网络环境测试
- 并发下载性能测试
- 错误恢复能力测试

### 界面响应测试
- 大量数据加载时界面响应
- 快速操作响应性
- 主题切换流畅性

## 🎯 测试完成标准

当以上所有测试项目都通过时，可以认为应用程序功能完整且稳定。建议在不同操作系统和硬件配置下重复测试，确保跨平台兼容性。

---

**注意**: 测试过程中如发现任何问题，请记录详细的错误信息和重现步骤，以便后续修复和改进。
