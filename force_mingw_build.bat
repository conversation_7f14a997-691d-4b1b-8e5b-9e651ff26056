@echo off
echo Building with MinGW...

REM 强制设置 MinGW 路径到 PATH 最前面
set QT_DIR=D:\Program\Qt\6.9.0\mingw_64
set PATH=%QT_DIR%\bin;%PATH%
set CMAKE_PREFIX_PATH=%QT_DIR%

REM 强制指定编译器
set CC=%QT_DIR%\bin\gcc.exe
set CXX=%QT_DIR%\bin\g++.exe

echo Qt Directory: %QT_DIR%
echo CC: %CC%
echo CXX: %CXX%

REM 检查工具
echo Checking MinGW tools...
%QT_DIR%\bin\gcc.exe --version
%QT_DIR%\bin\g++.exe --version
%QT_DIR%\bin\mingw32-make.exe --version

if exist build rmdir /s /q build
mkdir build
cd build

echo Configuring with explicit MinGW...
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_PREFIX_PATH=%CMAKE_PREFIX_PATH% -DCMAKE_C_COMPILER=%CC% -DCMAKE_CXX_COMPILER=%CXX% -DCMAKE_MAKE_PROGRAM=%QT_DIR%\bin\mingw32-make.exe -G "MinGW Makefiles"

if %ERRORLEVEL% neq 0 (
    echo Configuration failed!
    pause
    exit /b 1
)

echo Building...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Success!
dir /s /b *.exe

pause
