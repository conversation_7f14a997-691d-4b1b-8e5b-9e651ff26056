{"classes": [{"className": "ElaComboBox", "lineNumber": 9, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pBorderRadius", "notify": "pBorderRadiusChanged", "read": "getBorderRadius", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBorderRadius"}], "qualifiedClassName": "ElaComboBox", "signals": [{"access": "public", "index": 0, "name": "pBorderRadiusChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QComboBox"}]}], "inputFile": "ElaComboBox.h", "outputRevision": 69}