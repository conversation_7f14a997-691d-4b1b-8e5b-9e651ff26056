{"classes": [{"className": "ElaToolTipPrivate", "lineNumber": 10, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "member": "_pOpacity", "name": "pOpacity", "notify": "pOpacityChanged", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "ElaToolTipPrivate", "signals": [{"access": "public", "index": 0, "name": "pOpacityChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "ElaToolTipPrivate.h", "outputRevision": 69}