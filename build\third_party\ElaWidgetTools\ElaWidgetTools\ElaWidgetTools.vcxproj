﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{E373B025-08E8-3046-A048-3D2157436208}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ElaWidgetTools</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ElaWidgetTools.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ElaWidgetToolsd</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ElaWidgetTools.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ElaWidgetTools</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">ElaWidgetTools.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">ElaWidgetTools</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">ElaWidgetTools.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">ElaWidgetTools</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\include_Debug;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtWidgets" /external:I "D:/Program/Qt/6.9.0/mingw_64/include" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtCore" /external:I "D:/Program/Qt/6.9.0/mingw_64/mkspecs/win32-g++" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtGui" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;ELAWIDGETTOOLS_LIBRARY;QT_WIDGETS_LIB;QT_CORE_LIB;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_GUI_LIB;CMAKE_INTDIR="Debug";ElaWidgetTools_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;ELAWIDGETTOOLS_LIBRARY;QT_WIDGETS_LIB;QT_CORE_LIB;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_GUI_LIB;CMAKE_INTDIR=\"Debug\";ElaWidgetTools_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\include_Debug;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\include_Debug;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target ElaWidgetTools</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autogen C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/ElaWidgetTools_autogen.dir/AutogenInfo.json Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Widgets.a;D3D11.lib;DXGI.lib;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Gui.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/Debug/ElaWidgetToolsd.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/Debug/ElaWidgetToolsd.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\include_Release;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtWidgets" /external:I "D:/Program/Qt/6.9.0/mingw_64/include" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtCore" /external:I "D:/Program/Qt/6.9.0/mingw_64/mkspecs/win32-g++" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtGui" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ELAWIDGETTOOLS_LIBRARY;QT_WIDGETS_LIB;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_GUI_LIB;CMAKE_INTDIR="Release";ElaWidgetTools_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ELAWIDGETTOOLS_LIBRARY;QT_WIDGETS_LIB;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_GUI_LIB;CMAKE_INTDIR=\"Release\";ElaWidgetTools_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\include_Release;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\include_Release;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target ElaWidgetTools</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autogen C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/ElaWidgetTools_autogen.dir/AutogenInfo.json Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Widgets.a;D3D11.lib;DXGI.lib;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Gui.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/Release/ElaWidgetTools.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/Release/ElaWidgetTools.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\include_MinSizeRel;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtWidgets" /external:I "D:/Program/Qt/6.9.0/mingw_64/include" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtCore" /external:I "D:/Program/Qt/6.9.0/mingw_64/mkspecs/win32-g++" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtGui" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ELAWIDGETTOOLS_LIBRARY;QT_WIDGETS_LIB;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_GUI_LIB;CMAKE_INTDIR="MinSizeRel";ElaWidgetTools_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ELAWIDGETTOOLS_LIBRARY;QT_WIDGETS_LIB;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_GUI_LIB;CMAKE_INTDIR=\"MinSizeRel\";ElaWidgetTools_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\include_MinSizeRel;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\include_MinSizeRel;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target ElaWidgetTools</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autogen C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/ElaWidgetTools_autogen.dir/AutogenInfo.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Widgets.a;D3D11.lib;DXGI.lib;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Gui.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/MinSizeRel/ElaWidgetTools.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/MinSizeRel/ElaWidgetTools.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\include_RelWithDebInfo;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtWidgets" /external:I "D:/Program/Qt/6.9.0/mingw_64/include" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtCore" /external:I "D:/Program/Qt/6.9.0/mingw_64/mkspecs/win32-g++" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtGui" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ELAWIDGETTOOLS_LIBRARY;QT_WIDGETS_LIB;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_GUI_LIB;CMAKE_INTDIR="RelWithDebInfo";ElaWidgetTools_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ELAWIDGETTOOLS_LIBRARY;QT_WIDGETS_LIB;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_GUI_LIB;CMAKE_INTDIR=\"RelWithDebInfo\";ElaWidgetTools_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\include_RelWithDebInfo;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\include_RelWithDebInfo;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target ElaWidgetTools</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autogen C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/ElaWidgetTools_autogen.dir/AutogenInfo.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Widgets.a;D3D11.lib;DXGI.lib;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Gui.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/RelWithDebInfo/ElaWidgetTools.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/RelWithDebInfo/ElaWidgetTools.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\CMakeFiles\9188092315b2bb591492cd35e358ee63\qt6elawidgettools_metatypes.json.gen.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Running moc --collect-json for target ElaWidgetTools</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\Program\Qt\6.9.0\mingw_64\bin\moc.exe -o C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/qt6elawidgettools_metatypes.json.gen --collect-json @C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/ElaWidgetTools_json_file_list.txt
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/qt6elawidgettools_metatypes.json.gen C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/qt6elawidgettools_metatypes.json
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Program\Qt\6.9.0\mingw_64\bin\moc.exe;C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\meta_types\ElaWidgetTools_json_file_list.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\meta_types\qt6elawidgettools_metatypes.json.gen</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Running moc --collect-json for target ElaWidgetTools</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\Program\Qt\6.9.0\mingw_64\bin\moc.exe -o C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/qt6elawidgettools_metatypes.json.gen --collect-json @C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/ElaWidgetTools_json_file_list.txt
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/qt6elawidgettools_metatypes.json.gen C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/qt6elawidgettools_metatypes.json
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Program\Qt\6.9.0\mingw_64\bin\moc.exe;C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\meta_types\ElaWidgetTools_json_file_list.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\meta_types\qt6elawidgettools_metatypes.json.gen</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Running moc --collect-json for target ElaWidgetTools</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\Program\Qt\6.9.0\mingw_64\bin\moc.exe -o C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/qt6elawidgettools_metatypes.json.gen --collect-json @C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/ElaWidgetTools_json_file_list.txt
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/qt6elawidgettools_metatypes.json.gen C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/qt6elawidgettools_metatypes.json
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Program\Qt\6.9.0\mingw_64\bin\moc.exe;C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\meta_types\ElaWidgetTools_json_file_list.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\meta_types\qt6elawidgettools_metatypes.json.gen</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Running moc --collect-json for target ElaWidgetTools</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\Program\Qt\6.9.0\mingw_64\bin\moc.exe -o C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/qt6elawidgettools_metatypes.json.gen --collect-json @C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/ElaWidgetTools_json_file_list.txt
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/qt6elawidgettools_metatypes.json.gen C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/meta_types/qt6elawidgettools_metatypes.json
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Program\Qt\6.9.0\mingw_64\bin\moc.exe;C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\meta_types\ElaWidgetTools_json_file_list.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\meta_types\qt6elawidgettools_metatypes.json.gen</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\CMakeFiles\79da0592dcb4e466964be98e73d9aead\qrc_ElaWidgetTools.cpp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Automatic RCC for include/ElaWidgetTools.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autorcc C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/ElaWidgetTools_autogen.dir/AutoRcc_ElaWidgetTools_6YEA5652QU_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaWidgetTools.qrc;C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\CMakeFiles\ElaWidgetTools_autogen.dir\AutoRcc_ElaWidgetTools_6YEA5652QU_Info.json;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Image\Cirno.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Image\Moon.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Image\noise.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Image\MicaBase.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Font\ElaAwesome.ttf;D:\Program\Qt\6.9.0\mingw_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\6YEA5652QU_Debug\qrc_ElaWidgetTools.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Automatic RCC for include/ElaWidgetTools.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autorcc C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/ElaWidgetTools_autogen.dir/AutoRcc_ElaWidgetTools_6YEA5652QU_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaWidgetTools.qrc;C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\CMakeFiles\ElaWidgetTools_autogen.dir\AutoRcc_ElaWidgetTools_6YEA5652QU_Info.json;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Image\Cirno.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Image\Moon.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Image\noise.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Image\MicaBase.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Font\ElaAwesome.ttf;D:\Program\Qt\6.9.0\mingw_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\6YEA5652QU_Release\qrc_ElaWidgetTools.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Automatic RCC for include/ElaWidgetTools.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
cd C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autorcc C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/ElaWidgetTools_autogen.dir/AutoRcc_ElaWidgetTools_6YEA5652QU_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaWidgetTools.qrc;C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\CMakeFiles\ElaWidgetTools_autogen.dir\AutoRcc_ElaWidgetTools_6YEA5652QU_Info.json;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Image\Cirno.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Image\Moon.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Image\noise.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Image\MicaBase.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Font\ElaAwesome.ttf;D:\Program\Qt\6.9.0\mingw_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\6YEA5652QU_MinSizeRel\qrc_ElaWidgetTools.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Automatic RCC for include/ElaWidgetTools.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
cd C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autorcc C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/ElaWidgetTools_autogen.dir/AutoRcc_ElaWidgetTools_6YEA5652QU_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaWidgetTools.qrc;C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\CMakeFiles\ElaWidgetTools_autogen.dir\AutoRcc_ElaWidgetTools_6YEA5652QU_Info.json;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Image\Cirno.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Image\Moon.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Image\noise.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Image\MicaBase.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Font\ElaAwesome.ttf;D:\Program\Qt\6.9.0\mingw_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\6YEA5652QU_RelWithDebInfo\qrc_ElaWidgetTools.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -SC:/Users/<USER>/Desktop/Civitai_IMG -BC:/Users/<USER>/Desktop/Civitai_IMG/build --check-stamp-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CMakePackageConfigHelpers.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\WriteBasicConfigVersionFile.cmake;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetToolsConfig.cmake.in;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaWidgetTools.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -SC:/Users/<USER>/Desktop/Civitai_IMG -BC:/Users/<USER>/Desktop/Civitai_IMG/build --check-stamp-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CMakePackageConfigHelpers.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\WriteBasicConfigVersionFile.cmake;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetToolsConfig.cmake.in;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaWidgetTools.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -SC:/Users/<USER>/Desktop/Civitai_IMG -BC:/Users/<USER>/Desktop/Civitai_IMG/build --check-stamp-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CMakePackageConfigHelpers.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\WriteBasicConfigVersionFile.cmake;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetToolsConfig.cmake.in;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaWidgetTools.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -SC:/Users/<USER>/Desktop/Civitai_IMG -BC:/Users/<USER>/Desktop/Civitai_IMG/build --check-stamp-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CMakePackageConfigHelpers.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\WriteBasicConfigVersionFile.cmake;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetToolsConfig.cmake.in;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaWidgetTools.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\mocs_compilation_Debug.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaAcrylicUrlCard.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaAppBar.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaApplication.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaBreadcrumbBar.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaCalendar.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaCalendarPicker.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaCheckBox.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaColorDialog.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaComboBox.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaContentDialog.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaDockWidget.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaDoubleSpinBox.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaDrawerArea.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaDxgiManager.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaEventBus.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaExponentialBlur.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaFlowLayout.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaGraphicsItem.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaGraphicsLineItem.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaGraphicsScene.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaGraphicsView.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaIcon.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaIconButton.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaImageCard.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaInteractiveCard.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaKeyBinder.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaLCDNumber.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaLineEdit.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaListView.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaLog.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaMenu.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaMenuBar.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaMessageBar.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaMessageButton.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaMultiSelectComboBox.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaNavigationBar.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaNavigationRouter.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaPivot.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaPlainTextEdit.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaPopularCard.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaProgressBar.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaProgressRing.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaPromotionCard.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaPromotionView.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaPushButton.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaRadioButton.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaReminderCard.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaRoller.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaScrollArea.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaScrollBar.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaScrollPage.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaScrollPageArea.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaSlider.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaSpinBox.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaStatusBar.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaSuggestBox.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaTabBar.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaTabWidget.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaTableView.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaText.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaTheme.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaToggleButton.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaToggleSwitch.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaToolBar.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaToolButton.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaToolTip.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaTreeView.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidget.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\ElaWindow.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\Def.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaAcrylicUrlCard.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaAppBar.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaApplication.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaBreadcrumbBar.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaCalendar.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaCalendarPicker.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaCheckBox.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaColorDialog.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaComboBox.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaContentDialog.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaDockWidget.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaDoubleSpinBox.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaDrawerArea.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaDxgiManager.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaEventBus.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaExponentialBlur.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaFlowLayout.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaGraphicsItem.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaGraphicsLineItem.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaGraphicsScene.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaGraphicsView.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaIcon.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaIconButton.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaImageCard.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaInteractiveCard.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaKeyBinder.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaLCDNumber.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaLineEdit.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaListView.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaLog.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaMenu.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaMenuBar.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaMessageBar.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaMessageButton.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaMultiSelectComboBox.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaNavigationBar.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaNavigationRouter.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaPivot.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaPlainTextEdit.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaPopularCard.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaProgressBar.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaProgressRing.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaPromotionCard.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaPromotionView.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaPushButton.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaRadioButton.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaReminderCard.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaRoller.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaScrollArea.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaScrollBar.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaScrollPage.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaScrollPageArea.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaSlider.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaSpinBox.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaStatusBar.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaSuggestBox.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaTabBar.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaTabWidget.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaTableView.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaText.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaTheme.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaToggleButton.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaToggleSwitch.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaToolBar.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaToolButton.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaToolTip.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaTreeView.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaWidget.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaWindow.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\singleton.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\stdafx.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaAcrylicUrlCardPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaAcrylicUrlCardPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaAppBarPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaAppBarPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaApplicationPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaApplicationPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaBreadcrumbBarPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaBreadcrumbBarPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaCalendarPickerPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaCalendarPickerPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaCalendarPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaCalendarPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaColorDialogPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaColorDialogPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaComboBoxPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaComboBoxPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaContentDialogPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaContentDialogPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaDockWidgetPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaDockWidgetPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaDoubleSpinBoxPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaDoubleSpinBoxPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaDrawerAreaPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaDrawerAreaPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaDxgiManagerPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaDxgiManagerPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaEventBusPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaEventBusPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaExponentialBlurPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaExponentialBlurPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaFlowLayoutPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaFlowLayoutPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaGraphicsItemPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaGraphicsItemPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaGraphicsLineItemPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaGraphicsLineItemPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaGraphicsScenePrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaGraphicsScenePrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaGraphicsViewPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaGraphicsViewPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaIconButtonPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaIconButtonPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaImageCardPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaImageCardPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaInteractiveCardPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaInteractiveCardPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaKeyBinderPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaKeyBinderPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaLCDNumberPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaLCDNumberPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaLineEditPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaLineEditPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaListViewPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaListViewPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaLogPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaLogPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaMenuPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaMenuPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaMessageBarPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaMessageBarPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaMessageButtonPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaMessageButtonPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaMultiSelectComboBoxPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaMultiSelectComboBoxPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaNavigationBarPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaNavigationBarPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaNavigationRouterPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaNavigationRouterPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaPivotPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaPivotPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaPlainTextEditPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaPlainTextEditPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaPopularCardPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaPopularCardPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaProgressBarPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaProgressBarPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaProgressRingPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaProgressRingPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaPromotionCardPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaPromotionCardPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaPromotionViewPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaPromotionViewPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaPushButtonPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaPushButtonPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaRadioButtonPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaRadioButtonPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaReminderCardPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaReminderCardPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaRollerPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaRollerPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaScrollAreaPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaScrollAreaPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaScrollBarPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaScrollBarPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaScrollPageAreaPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaScrollPageAreaPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaScrollPagePrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaScrollPagePrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaSpinBoxPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaSpinBoxPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaSuggestBoxPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaSuggestBoxPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaTabBarPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaTabBarPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaTabWidgetPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaTabWidgetPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaTableViewPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaTableViewPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaTextPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaTextPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaThemePrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaThemePrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaToggleButtonPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaToggleButtonPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaToggleSwitchPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaToggleSwitchPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaToolBarPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaToolBarPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaToolButtonPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaToolButtonPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaToolTipPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaToolTipPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaTreeViewPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaTreeViewPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaWidgetPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaWidgetPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaWindowPrivate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private\ElaWindowPrivate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaBaseListView.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaBaseListView.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaBreadcrumbBarDelegate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaBreadcrumbBarDelegate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaBreadcrumbBarModel.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaBreadcrumbBarModel.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaCalendarDelegate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaCalendarDelegate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaCalendarModel.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaCalendarModel.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaCalendarPickerContainer.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaCalendarPickerContainer.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaCalendarTitleDelegate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaCalendarTitleDelegate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaCalendarTitleModel.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaCalendarTitleModel.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaCentralStackedWidget.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaCentralStackedWidget.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaCheckBoxStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaCheckBoxStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaColorDisplayDelegate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaColorDisplayDelegate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaColorDisplayModel.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaColorDisplayModel.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaColorPicker.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaColorPicker.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaColorPreview.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaColorPreview.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaColorValueSliderStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaColorValueSliderStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaComboBoxStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaComboBoxStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaComboBoxView.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaComboBoxView.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaCustomTabWidget.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaCustomTabWidget.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaCustomWidget.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaCustomWidget.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaDockWidgetTitleBar.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaDockWidgetTitleBar.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaDrawerContainer.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaDrawerContainer.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaDrawerHeader.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaDrawerHeader.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaDxgi.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaDxgi.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaFooterDelegate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaFooterDelegate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaFooterModel.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaFooterModel.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaIntValidator.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaIntValidator.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaKeyBinderContainer.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaKeyBinderContainer.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaLCDNumberStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaLCDNumberStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaLineEditStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaLineEditStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaListViewStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaListViewStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaMaskWidget.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaMaskWidget.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaMenuBarStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaMenuBarStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaMenuStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaMenuStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaMicaBaseInitObject.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaMicaBaseInitObject.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaNavigationModel.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaNavigationModel.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaNavigationNode.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaNavigationNode.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaNavigationStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaNavigationStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaNavigationView.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaNavigationView.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaPivotModel.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaPivotModel.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaPivotStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaPivotStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaPivotView.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaPivotView.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaPlainTextEditStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaPlainTextEditStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaPopularCardFloater.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaPopularCardFloater.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaProgressBarStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaProgressBarStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaRadioButtonStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaRadioButtonStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaScrollBarStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaScrollBarStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaSliderStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaSliderStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaSpinBoxStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaSpinBoxStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaStatusBarStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaStatusBarStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaSuggestBoxSearchViewContainer.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaSuggestBoxSearchViewContainer.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaSuggestDelegate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaSuggestDelegate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaSuggestModel.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaSuggestModel.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaTabBarStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaTabBarStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaTableViewStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaTableViewStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaThemeAnimationWidget.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaThemeAnimationWidget.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaToolBarStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaToolBarStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaToolButtonStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaToolButtonStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaTreeViewStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaTreeViewStyle.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaWinShadowHelper.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaWinShadowHelper.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaWindowStyle.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents\ElaWindowStyle.h" />
    <None Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include\ElaWidgetTools.qrc">
    </None>
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ddf_ElaWidgetTools.h" />
    <None Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\meta_types\qt6elawidgettools_metatypes.json.gen">
    </None>
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\6YEA5652QU_Debug\qrc_ElaWidgetTools.cpp">
      <ObjectFileName>$(IntDir)/ElaWidgetTools_autogen/6YEA5652QU_Debug/qrc_ElaWidgetTools.cpp.obj</ObjectFileName>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\mocs_compilation_Release.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\6YEA5652QU_Release\qrc_ElaWidgetTools.cpp">
      <ObjectFileName>$(IntDir)/ElaWidgetTools_autogen/6YEA5652QU_Release/qrc_ElaWidgetTools.cpp.obj</ObjectFileName>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\mocs_compilation_MinSizeRel.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\6YEA5652QU_MinSizeRel\qrc_ElaWidgetTools.cpp">
      <ObjectFileName>$(IntDir)/ElaWidgetTools_autogen/6YEA5652QU_MinSizeRel/qrc_ElaWidgetTools.cpp.obj</ObjectFileName>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_autogen\6YEA5652QU_RelWithDebInfo\qrc_ElaWidgetTools.cpp">
      <ObjectFileName>$(IntDir)/ElaWidgetTools_autogen/6YEA5652QU_RelWithDebInfo/qrc_ElaWidgetTools.cpp.obj</ObjectFileName>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\ZERO_CHECK.vcxproj">
      <Project>{442297F5-2886-3548-A699-3DC346C07EB5}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools_automoc_json_extraction.vcxproj">
      <Project>{2B00ACB5-DE28-3672-8636-F28ADBA637DD}</Project>
      <Name>ElaWidgetTools_automoc_json_extraction</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>