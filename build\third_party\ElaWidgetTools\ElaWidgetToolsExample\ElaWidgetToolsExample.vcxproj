﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{119E5808-FF94-3275-B96F-D93D7569C57B}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ElaWidgetToolsExample</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ElaWidgetToolsExample.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ElaWidgetToolsExample</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ElaWidgetToolsExample.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ElaWidgetToolsExample</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">ElaWidgetToolsExample.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">ElaWidgetToolsExample</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">ElaWidgetToolsExample.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">ElaWidgetToolsExample</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\ElaWidgetToolsExample_autogen\include_Debug;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtCore" /external:I "D:/Program/Qt/6.9.0/mingw_64/include" /external:I "D:/Program/Qt/6.9.0/mingw_64/mkspecs/win32-g++" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtWidgets" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtGui" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;QT_CORE_LIB;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_NEEDS_QMAIN;QT_WIDGETS_LIB;QT_GUI_LIB;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;QT_CORE_LIB;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_NEEDS_QMAIN;QT_WIDGETS_LIB;QT_GUI_LIB;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\ElaWidgetToolsExample_autogen\include_Debug;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\ElaWidgetToolsExample_autogen\include_Debug;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target ElaWidgetToolsExample</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autogen C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/CMakeFiles/ElaWidgetToolsExample_autogen.dir/AutogenInfo.json Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>..\ElaWidgetTools\Debug\ElaWidgetToolsd.lib;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Widgets.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Gui.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;mingw32.lib;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6EntryPoint.a;shell32.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;D3D11.lib;DXGI.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/Debug/ElaWidgetToolsExample.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/Debug/ElaWidgetToolsExample.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\ElaWidgetToolsExample_autogen\include_Release;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtCore" /external:I "D:/Program/Qt/6.9.0/mingw_64/include" /external:I "D:/Program/Qt/6.9.0/mingw_64/mkspecs/win32-g++" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtWidgets" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtGui" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_NEEDS_QMAIN;QT_WIDGETS_LIB;QT_GUI_LIB;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_NEEDS_QMAIN;QT_WIDGETS_LIB;QT_GUI_LIB;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\ElaWidgetToolsExample_autogen\include_Release;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\ElaWidgetToolsExample_autogen\include_Release;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target ElaWidgetToolsExample</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autogen C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/CMakeFiles/ElaWidgetToolsExample_autogen.dir/AutogenInfo.json Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>..\ElaWidgetTools\Release\ElaWidgetTools.lib;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Widgets.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Gui.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;mingw32.lib;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6EntryPoint.a;shell32.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;D3D11.lib;DXGI.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/Release/ElaWidgetToolsExample.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/Release/ElaWidgetToolsExample.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\ElaWidgetToolsExample_autogen\include_MinSizeRel;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtCore" /external:I "D:/Program/Qt/6.9.0/mingw_64/include" /external:I "D:/Program/Qt/6.9.0/mingw_64/mkspecs/win32-g++" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtWidgets" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtGui" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_NEEDS_QMAIN;QT_WIDGETS_LIB;QT_GUI_LIB;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_NEEDS_QMAIN;QT_WIDGETS_LIB;QT_GUI_LIB;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\ElaWidgetToolsExample_autogen\include_MinSizeRel;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\ElaWidgetToolsExample_autogen\include_MinSizeRel;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target ElaWidgetToolsExample</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autogen C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/CMakeFiles/ElaWidgetToolsExample_autogen.dir/AutogenInfo.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>..\ElaWidgetTools\MinSizeRel\ElaWidgetTools.lib;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Widgets.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Gui.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;mingw32.lib;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6EntryPoint.a;shell32.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;D3D11.lib;DXGI.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/MinSizeRel/ElaWidgetToolsExample.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/MinSizeRel/ElaWidgetToolsExample.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\ElaWidgetToolsExample_autogen\include_RelWithDebInfo;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtCore" /external:I "D:/Program/Qt/6.9.0/mingw_64/include" /external:I "D:/Program/Qt/6.9.0/mingw_64/mkspecs/win32-g++" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtWidgets" /external:I "D:/Program/Qt/6.9.0/mingw_64/include/QtGui" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_NEEDS_QMAIN;QT_WIDGETS_LIB;QT_GUI_LIB;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_NEEDS_QMAIN;QT_WIDGETS_LIB;QT_GUI_LIB;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\ElaWidgetToolsExample_autogen\include_RelWithDebInfo;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\ElaWidgetToolsExample_autogen\include_RelWithDebInfo;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\include;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\private;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetTools\DeveloperComponents;D:\Program\Qt\6.9.0\mingw_64\include\QtCore;D:\Program\Qt\6.9.0\mingw_64\include;D:\Program\Qt\6.9.0\mingw_64\mkspecs\win32-g++;D:\Program\Qt\6.9.0\mingw_64\include\QtWidgets;D:\Program\Qt\6.9.0\mingw_64\include\QtGui;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target ElaWidgetToolsExample</Message>
      <Command>setlocal
cd C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -E cmake_autogen C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/CMakeFiles/ElaWidgetToolsExample_autogen.dir/AutogenInfo.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>..\ElaWidgetTools\RelWithDebInfo\ElaWidgetTools.lib;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Widgets.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Gui.a;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;mingw32.lib;D:\Program\Qt\6.9.0\mingw_64\lib\libQt6EntryPoint.a;shell32.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;D3D11.lib;DXGI.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/RelWithDebInfo/ElaWidgetToolsExample.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/RelWithDebInfo/ElaWidgetToolsExample.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\CMakeFiles\44ecb90d143b2526e7b26ab7e1201cda\qrc_ElaWidgetToolsExample.cpp.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Running rcc for resource ElaWidgetToolsExample</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\Program\Qt\6.9.0\mingw_64\bin\rcc.exe --output C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/.qt/rcc/qrc_ElaWidgetToolsExample.cpp --name ElaWidgetToolsExample C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/.qt/rcc/ElaWidgetToolsExample.qrc --no-zstd
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\beach.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\classroom.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\dream.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\miku.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Cirno.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Home_Background.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_12+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_16+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_18+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_3+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_7+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\CollapsingWorld.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\DarkForest.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\LASTSTARDUST.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\Light.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\MaVieEnRose.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\NaightNavigationStar.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\RainMan.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\RunningInTheDark.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\STYXHELIX.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Moon.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Acrylic.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AnimatedIcon.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AnimatedVisualPlayer.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AnimationInterop.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AppBarButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AppBarSeparator.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AppBarToggleButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AutoSuggestBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AutomationProperties.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Border.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\BreadcrumbBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Button.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CalendarDatePicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CalendarView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Canvas.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Checkbox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Clipboard.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ColorPaletteResources.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ColorPicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ComboBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CommandBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CommandBarFlyout.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CompactSizing.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ConnectedAnimation.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ContentDialog.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CreateMultipleWindows.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\DataGrid.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\DatePicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\DropDownButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\EasingFunction.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Expander.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\FilePicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\FlipView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Flyout.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Grid.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\GridView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\HyperlinkButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\IconElement.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Image.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ImplicitTransition.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InfoBadge.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InfoBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InkCanvas.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InkToolbar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InputValidation.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ItemsRepeater.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Line.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ListBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ListView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\MediaPlayerElement.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\MenuBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\MenuFlyout.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\NavigationView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\NumberBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PageTransition.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ParallaxView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PasswordBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PersonPicture.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PipsPager.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Pivot.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ProgressBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ProgressRing.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PullToRefresh.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RadialGradientBrush.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RadioButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RadioButtons.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RatingControl.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RelativePanel.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RepeatButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RevealFocus.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RichEditBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RichTextBlock.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ScrollViewer.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SemanticZoom.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Shape.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Slider.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Sound.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SplitButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SplitView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\StackPanel.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\StandardUICommand.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SwipeControl.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TabView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TeachingTip.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TextBlock.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TextBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ThemeTransition.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TimePicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TitleBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToggleButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToggleSplitButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToggleSwitch.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToolTip.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TreeView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\VariableSizedWrapGrid.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Viewbox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\WebView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\XamlUICommand.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\github.png;C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\.qt\rcc\ElaWidgetToolsExample.qrc;D:\Program\Qt\6.9.0\mingw_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\.qt\rcc\qrc_ElaWidgetToolsExample.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Running rcc for resource ElaWidgetToolsExample</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\Program\Qt\6.9.0\mingw_64\bin\rcc.exe --output C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/.qt/rcc/qrc_ElaWidgetToolsExample.cpp --name ElaWidgetToolsExample C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/.qt/rcc/ElaWidgetToolsExample.qrc --no-zstd
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\beach.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\classroom.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\dream.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\miku.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Cirno.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Home_Background.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_12+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_16+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_18+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_3+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_7+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\CollapsingWorld.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\DarkForest.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\LASTSTARDUST.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\Light.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\MaVieEnRose.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\NaightNavigationStar.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\RainMan.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\RunningInTheDark.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\STYXHELIX.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Moon.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Acrylic.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AnimatedIcon.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AnimatedVisualPlayer.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AnimationInterop.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AppBarButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AppBarSeparator.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AppBarToggleButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AutoSuggestBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AutomationProperties.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Border.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\BreadcrumbBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Button.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CalendarDatePicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CalendarView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Canvas.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Checkbox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Clipboard.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ColorPaletteResources.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ColorPicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ComboBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CommandBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CommandBarFlyout.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CompactSizing.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ConnectedAnimation.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ContentDialog.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CreateMultipleWindows.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\DataGrid.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\DatePicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\DropDownButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\EasingFunction.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Expander.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\FilePicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\FlipView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Flyout.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Grid.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\GridView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\HyperlinkButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\IconElement.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Image.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ImplicitTransition.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InfoBadge.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InfoBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InkCanvas.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InkToolbar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InputValidation.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ItemsRepeater.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Line.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ListBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ListView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\MediaPlayerElement.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\MenuBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\MenuFlyout.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\NavigationView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\NumberBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PageTransition.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ParallaxView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PasswordBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PersonPicture.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PipsPager.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Pivot.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ProgressBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ProgressRing.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PullToRefresh.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RadialGradientBrush.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RadioButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RadioButtons.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RatingControl.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RelativePanel.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RepeatButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RevealFocus.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RichEditBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RichTextBlock.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ScrollViewer.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SemanticZoom.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Shape.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Slider.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Sound.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SplitButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SplitView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\StackPanel.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\StandardUICommand.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SwipeControl.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TabView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TeachingTip.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TextBlock.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TextBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ThemeTransition.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TimePicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TitleBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToggleButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToggleSplitButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToggleSwitch.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToolTip.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TreeView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\VariableSizedWrapGrid.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Viewbox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\WebView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\XamlUICommand.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\github.png;C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\.qt\rcc\ElaWidgetToolsExample.qrc;D:\Program\Qt\6.9.0\mingw_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\.qt\rcc\qrc_ElaWidgetToolsExample.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Running rcc for resource ElaWidgetToolsExample</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\Program\Qt\6.9.0\mingw_64\bin\rcc.exe --output C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/.qt/rcc/qrc_ElaWidgetToolsExample.cpp --name ElaWidgetToolsExample C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/.qt/rcc/ElaWidgetToolsExample.qrc --no-zstd
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\beach.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\classroom.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\dream.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\miku.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Cirno.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Home_Background.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_12+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_16+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_18+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_3+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_7+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\CollapsingWorld.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\DarkForest.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\LASTSTARDUST.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\Light.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\MaVieEnRose.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\NaightNavigationStar.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\RainMan.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\RunningInTheDark.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\STYXHELIX.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Moon.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Acrylic.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AnimatedIcon.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AnimatedVisualPlayer.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AnimationInterop.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AppBarButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AppBarSeparator.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AppBarToggleButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AutoSuggestBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AutomationProperties.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Border.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\BreadcrumbBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Button.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CalendarDatePicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CalendarView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Canvas.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Checkbox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Clipboard.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ColorPaletteResources.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ColorPicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ComboBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CommandBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CommandBarFlyout.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CompactSizing.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ConnectedAnimation.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ContentDialog.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CreateMultipleWindows.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\DataGrid.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\DatePicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\DropDownButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\EasingFunction.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Expander.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\FilePicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\FlipView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Flyout.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Grid.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\GridView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\HyperlinkButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\IconElement.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Image.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ImplicitTransition.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InfoBadge.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InfoBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InkCanvas.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InkToolbar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InputValidation.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ItemsRepeater.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Line.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ListBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ListView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\MediaPlayerElement.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\MenuBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\MenuFlyout.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\NavigationView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\NumberBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PageTransition.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ParallaxView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PasswordBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PersonPicture.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PipsPager.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Pivot.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ProgressBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ProgressRing.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PullToRefresh.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RadialGradientBrush.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RadioButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RadioButtons.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RatingControl.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RelativePanel.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RepeatButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RevealFocus.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RichEditBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RichTextBlock.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ScrollViewer.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SemanticZoom.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Shape.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Slider.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Sound.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SplitButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SplitView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\StackPanel.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\StandardUICommand.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SwipeControl.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TabView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TeachingTip.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TextBlock.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TextBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ThemeTransition.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TimePicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TitleBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToggleButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToggleSplitButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToggleSwitch.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToolTip.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TreeView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\VariableSizedWrapGrid.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Viewbox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\WebView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\XamlUICommand.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\github.png;C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\.qt\rcc\ElaWidgetToolsExample.qrc;D:\Program\Qt\6.9.0\mingw_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\.qt\rcc\qrc_ElaWidgetToolsExample.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Running rcc for resource ElaWidgetToolsExample</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\Program\Qt\6.9.0\mingw_64\bin\rcc.exe --output C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/.qt/rcc/qrc_ElaWidgetToolsExample.cpp --name ElaWidgetToolsExample C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/.qt/rcc/ElaWidgetToolsExample.qrc --no-zstd
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\beach.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\classroom.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\dream.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\miku.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Cirno.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Home_Background.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_12+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_16+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_18+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_3+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_7+.svg.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\CollapsingWorld.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\DarkForest.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\LASTSTARDUST.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\Light.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\MaVieEnRose.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\NaightNavigationStar.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\RainMan.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\RunningInTheDark.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\STYXHELIX.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Moon.jpg;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Acrylic.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AnimatedIcon.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AnimatedVisualPlayer.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AnimationInterop.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AppBarButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AppBarSeparator.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AppBarToggleButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AutoSuggestBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AutomationProperties.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Border.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\BreadcrumbBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Button.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CalendarDatePicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CalendarView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Canvas.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Checkbox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Clipboard.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ColorPaletteResources.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ColorPicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ComboBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CommandBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CommandBarFlyout.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CompactSizing.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ConnectedAnimation.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ContentDialog.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CreateMultipleWindows.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\DataGrid.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\DatePicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\DropDownButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\EasingFunction.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Expander.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\FilePicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\FlipView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Flyout.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Grid.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\GridView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\HyperlinkButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\IconElement.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Image.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ImplicitTransition.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InfoBadge.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InfoBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InkCanvas.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InkToolbar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InputValidation.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ItemsRepeater.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Line.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ListBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ListView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\MediaPlayerElement.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\MenuBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\MenuFlyout.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\NavigationView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\NumberBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PageTransition.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ParallaxView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PasswordBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PersonPicture.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PipsPager.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Pivot.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ProgressBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ProgressRing.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PullToRefresh.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RadialGradientBrush.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RadioButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RadioButtons.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RatingControl.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RelativePanel.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RepeatButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RevealFocus.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RichEditBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RichTextBlock.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ScrollViewer.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SemanticZoom.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Shape.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Slider.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Sound.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SplitButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SplitView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\StackPanel.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\StandardUICommand.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SwipeControl.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TabView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TeachingTip.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TextBlock.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TextBox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ThemeTransition.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TimePicker.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TitleBar.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToggleButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToggleSplitButton.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToggleSwitch.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToolTip.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TreeView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\VariableSizedWrapGrid.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Viewbox.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\WebView.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\XamlUICommand.png;C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\github.png;C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\.qt\rcc\ElaWidgetToolsExample.qrc;D:\Program\Qt\6.9.0\mingw_64\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\.qt\rcc\qrc_ElaWidgetToolsExample.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetToolsExample/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -SC:/Users/<USER>/Desktop/Civitai_IMG -BC:/Users/<USER>/Desktop/Civitai_IMG/build --check-stamp-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CheckCXXCompilerFlag.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\GNUInstallDirs.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Config.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Dependencies.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Targets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeature.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeatureCommon.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtInstallPaths.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfigureFileTemplate.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetToolsExample/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -SC:/Users/<USER>/Desktop/Civitai_IMG -BC:/Users/<USER>/Desktop/Civitai_IMG/build --check-stamp-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CheckCXXCompilerFlag.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\GNUInstallDirs.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Config.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Dependencies.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Targets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeature.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeatureCommon.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtInstallPaths.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfigureFileTemplate.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetToolsExample/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -SC:/Users/<USER>/Desktop/Civitai_IMG -BC:/Users/<USER>/Desktop/Civitai_IMG/build --check-stamp-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CheckCXXCompilerFlag.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\GNUInstallDirs.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Config.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Dependencies.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Targets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeature.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeatureCommon.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtInstallPaths.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfigureFileTemplate.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetToolsExample/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\bin\cmake.exe -SC:/Users/<USER>/Desktop/Civitai_IMG -BC:/Users/<USER>/Desktop/Civitai_IMG/build --check-stamp-file C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetToolsExample/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\CheckCXXCompilerFlag.cmake;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Lib\site-packages\cmake\data\share\cmake-3.30\Modules\GNUInstallDirs.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Config.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Dependencies.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6Targets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeature.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtFeatureCommon.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtInstallPaths.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;D:\Program\Qt\6.9.0\mingw_64\lib\cmake\Qt6Core\Qt6CoreConfigureFileTemplate.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\ElaWidgetToolsExample_autogen\mocs_compilation_Debug.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\main.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\mainwindow.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\mainwindow.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView\T_IconDelegate.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView\T_IconDelegate.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView\T_IconModel.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView\T_IconModel.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView\T_ListViewModel.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView\T_ListViewModel.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView\T_LogModel.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView\T_LogModel.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView\T_TableViewModel.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView\T_TableViewModel.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView\T_TreeItem.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView\T_TreeItem.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView\T_TreeViewModel.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ModelView\T_TreeViewModel.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_About.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_About.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_BaseComponents.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_BaseComponents.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_BasePage.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_BasePage.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_Card.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_Card.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_ElaPacketIO.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_ElaPacketIO.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_ElaScreen.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_ElaScreen.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_Graphics.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_Graphics.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_Home.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_Home.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_Icon.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_Icon.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_ListView.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_ListView.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_LogWidget.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_LogWidget.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_Navigation.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_Navigation.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_Popup.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_Popup.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_RecvScreen.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_RecvScreen.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_Setting.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_Setting.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_TableView.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_TableView.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_TreeView.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_TreeView.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_UpdateWidget.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\ExamplePage\T_UpdateWidget.h" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\.qt\rcc\qrc_ElaWidgetToolsExample.cpp">
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\.qt\rcc\ElaWidgetToolsExample.qrc" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\beach.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\classroom.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\dream.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Card\miku.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Cirno.jpg" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Home_Background.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_12+.svg.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_16+.svg.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_18+.svg.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_3+.svg.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\IARC\IARC_7+.svg.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\CollapsingWorld.jpg" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\DarkForest.jpg" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\LASTSTARDUST.jpg" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\Light.jpg" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\MaVieEnRose.jpg" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\NaightNavigationStar.jpg" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\RainMan.jpg" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\RunningInTheDark.jpg" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Model\STYXHELIX.jpg" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\Moon.jpg" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Acrylic.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AnimatedIcon.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AnimatedVisualPlayer.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AnimationInterop.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AppBarButton.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AppBarSeparator.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AppBarToggleButton.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AutoSuggestBox.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\AutomationProperties.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Border.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\BreadcrumbBar.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Button.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CalendarDatePicker.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CalendarView.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Canvas.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Checkbox.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Clipboard.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ColorPaletteResources.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ColorPicker.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ComboBox.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CommandBar.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CommandBarFlyout.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CompactSizing.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ConnectedAnimation.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ContentDialog.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\CreateMultipleWindows.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\DataGrid.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\DatePicker.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\DropDownButton.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\EasingFunction.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Expander.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\FilePicker.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\FlipView.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Flyout.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Grid.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\GridView.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\HyperlinkButton.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\IconElement.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Image.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ImplicitTransition.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InfoBadge.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InfoBar.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InkCanvas.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InkToolbar.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\InputValidation.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ItemsRepeater.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Line.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ListBox.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ListView.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\MediaPlayerElement.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\MenuBar.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\MenuFlyout.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\NavigationView.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\NumberBox.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PageTransition.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ParallaxView.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PasswordBox.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PersonPicture.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PipsPager.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Pivot.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ProgressBar.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ProgressRing.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\PullToRefresh.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RadialGradientBrush.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RadioButton.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RadioButtons.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RatingControl.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RelativePanel.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RepeatButton.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RevealFocus.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RichEditBox.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\RichTextBlock.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ScrollViewer.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SemanticZoom.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Shape.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Slider.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Sound.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SplitButton.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SplitView.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\StackPanel.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\StandardUICommand.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\SwipeControl.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TabView.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TeachingTip.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TextBlock.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TextBox.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ThemeTransition.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TimePicker.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TitleBar.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToggleButton.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToggleSplitButton.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToggleSwitch.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\ToolTip.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\TreeView.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\VariableSizedWrapGrid.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\Viewbox.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\WebView.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\control\XamlUICommand.png" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Civitai_IMG\third_party\ElaWidgetTools\ElaWidgetToolsExample\Resource\Image\github.png" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\ElaWidgetToolsExample_autogen\mocs_compilation_Release.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\ElaWidgetToolsExample_autogen\mocs_compilation_MinSizeRel.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetToolsExample\ElaWidgetToolsExample_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\ZERO_CHECK.vcxproj">
      <Project>{442297F5-2886-3548-A699-3DC346C07EB5}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\Civitai_IMG\build\third_party\ElaWidgetTools\ElaWidgetTools\ElaWidgetTools.vcxproj">
      <Project>{E373B025-08E8-3046-A048-3D2157436208}</Project>
      <Name>ElaWidgetTools</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>