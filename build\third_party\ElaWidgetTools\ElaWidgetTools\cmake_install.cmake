# Install script for directory: C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "C:/Users/<USER>/Desktop/Civitai_IMG/Install")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Release")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/lib" TYPE STATIC_LIBRARY OPTIONAL FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/Debug/ElaWidgetToolsd.lib")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/lib" TYPE STATIC_LIBRARY OPTIONAL FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/Release/ElaWidgetTools.lib")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/lib" TYPE STATIC_LIBRARY OPTIONAL FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/MinSizeRel/ElaWidgetTools.lib")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/lib" TYPE STATIC_LIBRARY OPTIONAL FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/RelWithDebInfo/ElaWidgetTools.lib")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/bin" TYPE SHARED_LIBRARY FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/Debug/ElaWidgetToolsd.dll")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/bin" TYPE SHARED_LIBRARY FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/Release/ElaWidgetTools.dll")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/bin" TYPE SHARED_LIBRARY FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/MinSizeRel/ElaWidgetTools.dll")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/bin" TYPE SHARED_LIBRARY FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/RelWithDebInfo/ElaWidgetTools.dll")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
     "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/ElaWidgetToolsd.dll")
    if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample" TYPE SHARED_LIBRARY FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/Debug/ElaWidgetToolsd.dll")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
     "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/ElaWidgetTools.dll")
    if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample" TYPE SHARED_LIBRARY FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/Release/ElaWidgetTools.dll")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
     "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/ElaWidgetTools.dll")
    if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample" TYPE SHARED_LIBRARY FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/MinSizeRel/ElaWidgetTools.dll")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
     "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/ElaWidgetTools.dll")
    if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample" TYPE SHARED_LIBRARY FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/RelWithDebInfo/ElaWidgetTools.dll")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
     "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/ElaWidgetToolsd.pdb")
    if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample" TYPE FILE OPTIONAL FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/Debug/ElaWidgetToolsd.pdb")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
     "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/ElaWidgetTools.pdb")
    if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample" TYPE FILE OPTIONAL FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/Release/ElaWidgetTools.pdb")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
     "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/ElaWidgetTools.pdb")
    if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample" TYPE FILE OPTIONAL FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/MinSizeRel/ElaWidgetTools.pdb")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
     "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample/ElaWidgetTools.pdb")
    if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
      message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
    endif()
    file(INSTALL DESTINATION "C:/Users/<USER>/Desktop/Civitai_IMG/Install/ElaWidgetToolsExample" TYPE FILE OPTIONAL FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/RelWithDebInfo/ElaWidgetTools.pdb")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/include" TYPE FILE FILES
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/Def.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaAcrylicUrlCard.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaAppBar.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaApplication.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaBreadcrumbBar.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaCalendar.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaCalendarPicker.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaCheckBox.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaColorDialog.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaComboBox.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaContentDialog.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaDockWidget.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaDoubleSpinBox.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaDrawerArea.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaDxgiManager.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaEventBus.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaExponentialBlur.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaFlowLayout.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaGraphicsItem.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaGraphicsLineItem.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaGraphicsScene.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaGraphicsView.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaIcon.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaIconButton.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaImageCard.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaInteractiveCard.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaKeyBinder.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaLCDNumber.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaLineEdit.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaListView.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaLog.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaMenu.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaMenuBar.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaMessageBar.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaMessageButton.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaMultiSelectComboBox.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaNavigationBar.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaNavigationRouter.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaPivot.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaPlainTextEdit.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaPopularCard.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaProgressBar.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaProgressRing.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaPromotionCard.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaPromotionView.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaPushButton.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaRadioButton.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaReminderCard.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaRoller.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaScrollArea.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaScrollBar.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaScrollPage.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaScrollPageArea.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaSlider.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaSpinBox.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaStatusBar.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaSuggestBox.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaTabBar.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaTabWidget.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaTableView.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaText.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaTheme.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaToggleButton.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaToggleSwitch.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaToolBar.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaToolButton.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaToolTip.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaTreeView.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaWidget.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/ElaWindow.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/singleton.h"
    "C:/Users/<USER>/Desktop/Civitai_IMG/third_party/ElaWidgetTools/ElaWidgetTools/include/stdafx.h"
    )
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/lib/cmake/ElaWidgetToolsTargets.cmake")
    file(DIFFERENT _cmake_export_file_changed FILES
         "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/lib/cmake/ElaWidgetToolsTargets.cmake"
         "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/Export/0a725064e637b157163c54398f042a49/ElaWidgetToolsTargets.cmake")
    if(_cmake_export_file_changed)
      file(GLOB _cmake_old_config_files "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/lib/cmake/ElaWidgetToolsTargets-*.cmake")
      if(_cmake_old_config_files)
        string(REPLACE ";" ", " _cmake_old_config_files_text "${_cmake_old_config_files}")
        message(STATUS "Old export file \"$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/lib/cmake/ElaWidgetToolsTargets.cmake\" will be replaced.  Removing files [${_cmake_old_config_files_text}].")
        unset(_cmake_old_config_files_text)
        file(REMOVE ${_cmake_old_config_files})
      endif()
      unset(_cmake_old_config_files)
    endif()
    unset(_cmake_export_file_changed)
  endif()
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/lib/cmake" TYPE FILE FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/Export/0a725064e637b157163c54398f042a49/ElaWidgetToolsTargets.cmake")
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/lib/cmake" TYPE FILE FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/Export/0a725064e637b157163c54398f042a49/ElaWidgetToolsTargets-debug.cmake")
  endif()
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/lib/cmake" TYPE FILE FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/Export/0a725064e637b157163c54398f042a49/ElaWidgetToolsTargets-minsizerel.cmake")
  endif()
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/lib/cmake" TYPE FILE FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/Export/0a725064e637b157163c54398f042a49/ElaWidgetToolsTargets-relwithdebinfo.cmake")
  endif()
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/lib/cmake" TYPE FILE FILES "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/CMakeFiles/Export/0a725064e637b157163c54398f042a49/ElaWidgetToolsTargets-release.cmake")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/ElaWidgetTools/lib/cmake" TYPE FILE FILES
    "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/ElaWidgetToolsConfig.cmake"
    "C:/Users/<USER>/Desktop/Civitai_IMG/build/third_party/ElaWidgetTools/ElaWidgetTools/ElaWidgetToolsConfigVersion.cmake"
    )
endif()

