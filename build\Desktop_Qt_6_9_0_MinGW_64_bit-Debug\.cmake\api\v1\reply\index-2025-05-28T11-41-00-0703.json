{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/Program/Qt/Tools/CMake_64/bin/cmake.exe", "cpack": "D:/Program/Qt/Tools/CMake_64/bin/cpack.exe", "ctest": "D:/Program/Qt/Tools/CMake_64/bin/ctest.exe", "root": "D:/Program/Qt/Tools/CMake_64/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 5, "string": "3.30.5", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-4d4cf1ff6317dce789a9.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-0d34503803b4e11bf24a.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-d5d53cacf4f9b38aeeae.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-0d34503803b4e11bf24a.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-d5d53cacf4f9b38aeeae.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-4d4cf1ff6317dce789a9.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}}}