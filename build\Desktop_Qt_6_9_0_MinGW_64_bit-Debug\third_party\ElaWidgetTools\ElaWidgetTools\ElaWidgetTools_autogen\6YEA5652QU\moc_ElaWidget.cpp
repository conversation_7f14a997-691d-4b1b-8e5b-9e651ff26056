/****************************************************************************
** Meta object code from reading C++ file 'ElaWidget.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaWidget.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN9ElaWidgetE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaWidget::qt_create_metaobjectdata<qt_meta_tag_ZN9ElaWidgetE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaWidget",
        "pIsStayTopChanged",
        "",
        "pIsFixedSizeChanged",
        "pIsDefaultClosedChanged",
        "pAppBarHeightChanged",
        "routeBackButtonClicked",
        "navigationButtonClicked",
        "themeChangeButtonClicked",
        "closeButtonClicked",
        "pIsStayTop",
        "pIsFixedSize",
        "pIsDefaultClosed",
        "pAppBarHeight"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pIsStayTopChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pIsFixedSizeChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pIsDefaultClosedChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pAppBarHeightChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'routeBackButtonClicked'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'navigationButtonClicked'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'themeChangeButtonClicked'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'closeButtonClicked'
        QtMocHelpers::SignalData<void()>(9, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pIsStayTop'
        QtMocHelpers::PropertyData<bool>(10, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pIsFixedSize'
        QtMocHelpers::PropertyData<bool>(11, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pIsDefaultClosed'
        QtMocHelpers::PropertyData<bool>(12, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pAppBarHeight'
        QtMocHelpers::PropertyData<int>(13, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 3),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaWidget, qt_meta_tag_ZN9ElaWidgetE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9ElaWidgetE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9ElaWidgetE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN9ElaWidgetE_t>.metaTypes,
    nullptr
} };

void ElaWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaWidget *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pIsStayTopChanged(); break;
        case 1: _t->pIsFixedSizeChanged(); break;
        case 2: _t->pIsDefaultClosedChanged(); break;
        case 3: _t->pAppBarHeightChanged(); break;
        case 4: _t->routeBackButtonClicked(); break;
        case 5: _t->navigationButtonClicked(); break;
        case 6: _t->themeChangeButtonClicked(); break;
        case 7: _t->closeButtonClicked(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaWidget::*)()>(_a, &ElaWidget::pIsStayTopChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWidget::*)()>(_a, &ElaWidget::pIsFixedSizeChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWidget::*)()>(_a, &ElaWidget::pIsDefaultClosedChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWidget::*)()>(_a, &ElaWidget::pAppBarHeightChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWidget::*)()>(_a, &ElaWidget::routeBackButtonClicked, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWidget::*)()>(_a, &ElaWidget::navigationButtonClicked, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWidget::*)()>(_a, &ElaWidget::themeChangeButtonClicked, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaWidget::*)()>(_a, &ElaWidget::closeButtonClicked, 7))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<bool*>(_v) = _t->getIsStayTop(); break;
        case 1: *reinterpret_cast<bool*>(_v) = _t->getIsFixedSize(); break;
        case 2: *reinterpret_cast<bool*>(_v) = _t->getIsDefaultClosed(); break;
        case 3: *reinterpret_cast<int*>(_v) = _t->getAppBarHeight(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setIsStayTop(*reinterpret_cast<bool*>(_v)); break;
        case 1: _t->setIsFixedSize(*reinterpret_cast<bool*>(_v)); break;
        case 2: _t->setIsDefaultClosed(*reinterpret_cast<bool*>(_v)); break;
        case 3: _t->setAppBarHeight(*reinterpret_cast<int*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9ElaWidgetE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ElaWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 8;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void ElaWidget::pIsStayTopChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaWidget::pIsFixedSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaWidget::pIsDefaultClosedChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaWidget::pAppBarHeightChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaWidget::routeBackButtonClicked()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ElaWidget::navigationButtonClicked()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void ElaWidget::themeChangeButtonClicked()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void ElaWidget::closeButtonClicked()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}
QT_WARNING_POP
