@echo off
echo ========================================
echo Civitai Image Viewer - MinGW Build
echo ========================================
echo.

REM 设置 Qt 路径（根据您的实际安装路径）
set QT_DIR=D:/Program/Qt/6.9.0/mingw_64
set CMAKE_PREFIX_PATH=%QT_DIR%

echo Qt Directory: %QT_DIR%
echo CMAKE_PREFIX_PATH: %CMAKE_PREFIX_PATH%
echo.

REM 检查 Qt 安装
if not exist "%QT_DIR%\bin\qmake.exe" (
    echo Error: Qt not found at %QT_DIR%
    echo Please check your Qt installation path
    pause
    exit /b 1
)
echo ✅ Qt found at: %QT_DIR%

REM 检查 MinGW
where gcc >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: MinGW GCC not found in PATH
    echo Please ensure MinGW is installed and in PATH
    echo You can add: %QT_DIR%\bin to PATH
    pause
    exit /b 1
)
echo ✅ MinGW GCC found

REM 检查 CMake
where cmake >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: CMake not found in PATH
    pause
    exit /b 1
)
echo ✅ CMake found

REM 清理并创建构建目录
if exist build rmdir /s /q build
mkdir build
cd build

echo.
echo Configuring project with MinGW...
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_PREFIX_PATH=%CMAKE_PREFIX_PATH% -G "MinGW Makefiles"

if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ CMake configuration failed!
    echo.
    echo Possible solutions:
    echo 1. Make sure Qt 6.9.0 MinGW is properly installed
    echo 2. Check if all Qt components are installed (Core, Widgets, Network)
    echo 3. Verify MinGW compiler is working: gcc --version
    echo 4. Try running from Qt Creator instead
    echo.
    pause
    exit /b 1
)

echo ✅ Configuration successful!
echo.
echo Building project...
cmake --build . --config Release --parallel

if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ Build failed!
    echo.
    echo Please check the error messages above.
    echo Common issues:
    echo 1. Missing Qt development components
    echo 2. ElaWidgetTools compilation errors
    echo 3. MinGW compiler issues
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎉 Build Successful!
echo ========================================
echo.

REM 查找可执行文件
echo Looking for executable...
for /r . %%i in (*.exe) do (
    if "%%~ni"=="CivitaiImageViewer" (
        echo Found executable: %%i
        set EXECUTABLE=%%i
        goto :found
    )
)

echo Executable not found, checking common locations...
if exist "CivitaiImageViewer.exe" (
    set EXECUTABLE=CivitaiImageViewer.exe
    goto :found
)

echo ❌ Executable not found!
echo Please check the build directory for CivitaiImageViewer.exe
pause
exit /b 1

:found
echo.
echo ✅ SUCCESS! 
echo Executable: %EXECUTABLE%
echo.
echo Next steps:
echo 1. Get your Civitai API key from: https://civitai.com/user/account
echo 2. Run the program and configure the API key in Settings
echo 3. Start browsing AI artwork!
echo.

set /p choice="Would you like to run the program now? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo Starting Civitai Image Viewer...
    start "" "%EXECUTABLE%"
    echo.
    echo ✅ Program launched!
    echo Please configure your Civitai API key in the Settings menu.
) else (
    echo.
    echo You can run the program later by executing:
    echo %CD%\%EXECUTABLE%
)

echo.
echo Build completed successfully!
pause
