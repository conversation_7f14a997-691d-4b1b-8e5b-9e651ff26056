{"classes": [{"className": "ElaPivot", "lineNumber": 8, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pTextPixelSize", "notify": "pTextPixelSizeChanged", "read": "getTextPixelSize", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTextPixelSize"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pCurrentIndex", "notify": "pCurrentIndexChanged", "read": "getCurrentIndex", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setCurrentIndex"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "pPivotSpacing", "notify": "pPivotSpacingChanged", "read": "getPivotSpacing", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setPivotSpacing"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "pMarkWidth", "notify": "pMarkWidthChanged", "read": "getMarkWidth", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMarkWidth"}], "qualifiedClassName": "ElaPivot", "signals": [{"access": "public", "index": 0, "name": "pTextPixelSizeChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pCurrentIndexChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "pPivotSpacingChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "pMarkWidthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 4, "name": "pivotClicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 5, "name": "pivotDoubleClicked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWidget"}]}], "inputFile": "ElaPivot.h", "outputRevision": 69}