# Build directories
build/
build-*/
out/
bin/
lib/

# CMake generated files
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile
*.cmake
!CMakeLists.txt

# Qt generated files
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
*.moc
moc_*.cpp
moc_*.h
qrc_*.cpp
ui_*.h
*.qm
.qmake.cache
.qmake.stash

# Visual Studio / MSVC
.vs/
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.sln
*.suo
*.user
*.aps
*.pch
*.vspscc
*_i.c
*_p.c
*.ncb
*.tlb
*.tlh
*.bak
*.cache
*.ilk
*.log
*.lib
*.sbr
*.sdf
*.opensdf
*.unsuccessfulbuild
ipch/
*.idb
*.pgc
*.pgd
*.rsp
*.tmp
*.tmp_proj
*.vssscc
*.pidb
*.svclog
*.scc

# Xcode
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata/
*.moved-aside
*.xccheckout
*.xcscmblueprint

# GCC/Clang
*.o
*.obj
*.so
*.dylib
*.dll
*.exe
*.out
*.app
*.dSYM/

# Debug files
*.pdb
*.idb

# Temporary files
*~
*.swp
*.swo
.DS_Store
Thumbs.db
*.tmp
*.temp

# IDE files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Application specific
config.ini
cache/
logs/
*.log

# Third party (if not using git submodules)
# third_party/

# Package files
*.zip
*.tar.gz
*.rar
*.7z

# Documentation build
docs/html/
docs/latex/

# Test results
Testing/
test_results/
*.gcov
*.gcda
*.gcno

# Profiling
*.prof
*.gprof

# Coverage
coverage/
*.coverage

# Backup files
*.bak
*.backup
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows specific
*.lnk
*.url

# Linux specific
*.desktop

# Application data
*.db
*.sqlite
*.sqlite3
