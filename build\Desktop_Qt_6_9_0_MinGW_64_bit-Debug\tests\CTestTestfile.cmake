# CMake generated Testfile for 
# Source directory: C:/Users/<USER>/Desktop/Civitai_IMG/tests
# Build directory: C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/tests
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(test_civitai_image_info "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/tests/test_civitai_image_info.exe")
set_tests_properties(test_civitai_image_info PROPERTIES  TIMEOUT "30" WORKING_DIRECTORY "C:/Users/<USER>/Desktop/Civitai_IMG/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/Civitai_IMG/tests/CMakeLists.txt;54;add_test;C:/Users/<USER>/Desktop/Civitai_IMG/tests/CMakeLists.txt;0;")
