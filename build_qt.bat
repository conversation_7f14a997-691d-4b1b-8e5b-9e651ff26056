@echo off
echo Building Civitai Image Viewer with Qt 6.9.0 MinGW...

set QT_DIR=D:/Program/Qt/6.9.0/mingw_64
set CMAKE_PREFIX_PATH=%QT_DIR%

echo Qt Directory: %QT_DIR%

if exist build rmdir /s /q build
mkdir build
cd build

echo Configuring with CMake...
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_PREFIX_PATH=%CMAKE_PREFIX_PATH% -G "MinGW Makefiles"

if %ERRORLEVEL% neq 0 (
    echo Configuration failed!
    pause
    exit /b 1
)

echo Building...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build successful!
dir /s /b *.exe

pause
