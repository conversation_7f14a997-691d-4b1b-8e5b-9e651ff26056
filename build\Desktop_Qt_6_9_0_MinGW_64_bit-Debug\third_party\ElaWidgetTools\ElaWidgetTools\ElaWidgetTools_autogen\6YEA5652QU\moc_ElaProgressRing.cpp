/****************************************************************************
** Meta object code from reading C++ file 'ElaProgressRing.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaProgressRing.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaProgressRing.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN15ElaProgressRingE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaProgressRing::qt_create_metaobjectdata<qt_meta_tag_ZN15ElaProgressRingE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaProgressRing",
        "pIsBusyingChanged",
        "",
        "pIsTransparentChanged",
        "pIsDisplayValueChanged",
        "pValueDisplayModeChanged",
        "pBusyingWidthChanged",
        "pBusyingDurationTimeChanged",
        "pMinimumChanged",
        "pMaximumChanged",
        "pValueChanged",
        "pValuePixelSizeChanged",
        "rangeChanged",
        "min",
        "max",
        "pIsBusying",
        "pIsTransparent",
        "pIsDisplayValue",
        "pValueDisplayMode",
        "ElaProgressRingType::ValueDisplayMode",
        "pBusyingWidth",
        "pBusyingDurationTime",
        "pMinimum",
        "pMaximum",
        "pValue",
        "pValuePixelSize"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pIsBusyingChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pIsTransparentChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pIsDisplayValueChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pValueDisplayModeChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pBusyingWidthChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pBusyingDurationTimeChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pMinimumChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pMaximumChanged'
        QtMocHelpers::SignalData<void()>(9, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pValueChanged'
        QtMocHelpers::SignalData<void()>(10, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pValuePixelSizeChanged'
        QtMocHelpers::SignalData<void()>(11, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'rangeChanged'
        QtMocHelpers::SignalData<void(int, int)>(12, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 13 }, { QMetaType::Int, 14 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pIsBusying'
        QtMocHelpers::PropertyData<bool>(15, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pIsTransparent'
        QtMocHelpers::PropertyData<bool>(16, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pIsDisplayValue'
        QtMocHelpers::PropertyData<bool>(17, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pValueDisplayMode'
        QtMocHelpers::PropertyData<ElaProgressRingType::ValueDisplayMode>(18, 0x80000000 | 19, QMC::DefaultPropertyFlags | QMC::Writable | QMC::EnumOrFlag, 3),
        // property 'pBusyingWidth'
        QtMocHelpers::PropertyData<int>(20, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 4),
        // property 'pBusyingDurationTime'
        QtMocHelpers::PropertyData<int>(21, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 5),
        // property 'pMinimum'
        QtMocHelpers::PropertyData<int>(22, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 6),
        // property 'pMaximum'
        QtMocHelpers::PropertyData<int>(23, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 7),
        // property 'pValue'
        QtMocHelpers::PropertyData<int>(24, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 8),
        // property 'pValuePixelSize'
        QtMocHelpers::PropertyData<int>(25, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 9),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaProgressRing, qt_meta_tag_ZN15ElaProgressRingE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT static const QMetaObject::SuperData qt_meta_extradata_ZN15ElaProgressRingE[] = {
    QMetaObject::SuperData::link<ElaProgressRingType::staticMetaObject>(),
    nullptr
};

Q_CONSTINIT const QMetaObject ElaProgressRing::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15ElaProgressRingE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15ElaProgressRingE_t>.data,
    qt_static_metacall,
    qt_meta_extradata_ZN15ElaProgressRingE,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN15ElaProgressRingE_t>.metaTypes,
    nullptr
} };

void ElaProgressRing::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaProgressRing *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pIsBusyingChanged(); break;
        case 1: _t->pIsTransparentChanged(); break;
        case 2: _t->pIsDisplayValueChanged(); break;
        case 3: _t->pValueDisplayModeChanged(); break;
        case 4: _t->pBusyingWidthChanged(); break;
        case 5: _t->pBusyingDurationTimeChanged(); break;
        case 6: _t->pMinimumChanged(); break;
        case 7: _t->pMaximumChanged(); break;
        case 8: _t->pValueChanged(); break;
        case 9: _t->pValuePixelSizeChanged(); break;
        case 10: _t->rangeChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaProgressRing::*)()>(_a, &ElaProgressRing::pIsBusyingChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaProgressRing::*)()>(_a, &ElaProgressRing::pIsTransparentChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaProgressRing::*)()>(_a, &ElaProgressRing::pIsDisplayValueChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaProgressRing::*)()>(_a, &ElaProgressRing::pValueDisplayModeChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaProgressRing::*)()>(_a, &ElaProgressRing::pBusyingWidthChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaProgressRing::*)()>(_a, &ElaProgressRing::pBusyingDurationTimeChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaProgressRing::*)()>(_a, &ElaProgressRing::pMinimumChanged, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaProgressRing::*)()>(_a, &ElaProgressRing::pMaximumChanged, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaProgressRing::*)()>(_a, &ElaProgressRing::pValueChanged, 8))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaProgressRing::*)()>(_a, &ElaProgressRing::pValuePixelSizeChanged, 9))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaProgressRing::*)(int , int )>(_a, &ElaProgressRing::rangeChanged, 10))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<bool*>(_v) = _t->getIsBusying(); break;
        case 1: *reinterpret_cast<bool*>(_v) = _t->getIsTransparent(); break;
        case 2: *reinterpret_cast<bool*>(_v) = _t->getIsDisplayValue(); break;
        case 3: *reinterpret_cast<ElaProgressRingType::ValueDisplayMode*>(_v) = _t->getValueDisplayMode(); break;
        case 4: *reinterpret_cast<int*>(_v) = _t->getBusyingWidth(); break;
        case 5: *reinterpret_cast<int*>(_v) = _t->getBusyingDurationTime(); break;
        case 6: *reinterpret_cast<int*>(_v) = _t->getMinimum(); break;
        case 7: *reinterpret_cast<int*>(_v) = _t->getMaximum(); break;
        case 8: *reinterpret_cast<int*>(_v) = _t->getValue(); break;
        case 9: *reinterpret_cast<int*>(_v) = _t->getValuePixelSize(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setIsBusying(*reinterpret_cast<bool*>(_v)); break;
        case 1: _t->setIsTransparent(*reinterpret_cast<bool*>(_v)); break;
        case 2: _t->setIsDisplayValue(*reinterpret_cast<bool*>(_v)); break;
        case 3: _t->setValueDisplayMode(*reinterpret_cast<ElaProgressRingType::ValueDisplayMode*>(_v)); break;
        case 4: _t->setBusyingWidth(*reinterpret_cast<int*>(_v)); break;
        case 5: _t->setBusyingDurationTime(*reinterpret_cast<int*>(_v)); break;
        case 6: _t->setMinimum(*reinterpret_cast<int*>(_v)); break;
        case 7: _t->setMaximum(*reinterpret_cast<int*>(_v)); break;
        case 8: _t->setValue(*reinterpret_cast<int*>(_v)); break;
        case 9: _t->setValuePixelSize(*reinterpret_cast<int*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaProgressRing::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaProgressRing::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15ElaProgressRingE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ElaProgressRing::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 11)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 11;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    }
    return _id;
}

// SIGNAL 0
void ElaProgressRing::pIsBusyingChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaProgressRing::pIsTransparentChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaProgressRing::pIsDisplayValueChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaProgressRing::pValueDisplayModeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaProgressRing::pBusyingWidthChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ElaProgressRing::pBusyingDurationTimeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void ElaProgressRing::pMinimumChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void ElaProgressRing::pMaximumChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}

// SIGNAL 8
void ElaProgressRing::pValueChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 8, nullptr);
}

// SIGNAL 9
void ElaProgressRing::pValuePixelSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 9, nullptr);
}

// SIGNAL 10
void ElaProgressRing::rangeChanged(int _t1, int _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 10, nullptr, _t1, _t2);
}
QT_WARNING_POP
