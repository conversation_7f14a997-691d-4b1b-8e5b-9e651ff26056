/****************************************************************************
** Meta object code from reading C++ file 'ElaReminderCard.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/ElaReminderCard.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaReminderCard.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN15ElaReminderCardE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaReminderCard::qt_create_metaobjectdata<qt_meta_tag_ZN15ElaReminderCardE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaReminderCard",
        "pBorderRadiusChanged",
        "",
        "pTitleChanged",
        "pSubTitleChanged",
        "pTitlePixelSizeChanged",
        "pSubTitlePixelSizeChanged",
        "pTitleSpacingChanged",
        "pCardPixmapChanged",
        "pCardPixmapSizeChanged",
        "pCardPixmapBorderRadiusChanged",
        "pCardPixModeChanged",
        "pBorderRadius",
        "pTitle",
        "pSubTitle",
        "pTitlePixelSize",
        "pSubTitlePixelSize",
        "pTitleSpacing",
        "pCardPixmap",
        "pCardPixmapSize",
        "pCardPixmapBorderRadius",
        "pCardPixMode",
        "ElaCardPixType::PixMode"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pBorderRadiusChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pTitleChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pSubTitleChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pTitlePixelSizeChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pSubTitlePixelSizeChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pTitleSpacingChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCardPixmapChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCardPixmapSizeChanged'
        QtMocHelpers::SignalData<void()>(9, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCardPixmapBorderRadiusChanged'
        QtMocHelpers::SignalData<void()>(10, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pCardPixModeChanged'
        QtMocHelpers::SignalData<void()>(11, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pBorderRadius'
        QtMocHelpers::PropertyData<int>(12, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pTitle'
        QtMocHelpers::PropertyData<QString>(13, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pSubTitle'
        QtMocHelpers::PropertyData<QString>(14, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable, 2),
        // property 'pTitlePixelSize'
        QtMocHelpers::PropertyData<int>(15, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 3),
        // property 'pSubTitlePixelSize'
        QtMocHelpers::PropertyData<int>(16, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 4),
        // property 'pTitleSpacing'
        QtMocHelpers::PropertyData<int>(17, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 5),
        // property 'pCardPixmap'
        QtMocHelpers::PropertyData<QPixmap>(18, QMetaType::QPixmap, QMC::DefaultPropertyFlags | QMC::Writable, 6),
        // property 'pCardPixmapSize'
        QtMocHelpers::PropertyData<QSize>(19, QMetaType::QSize, QMC::DefaultPropertyFlags | QMC::Writable, 7),
        // property 'pCardPixmapBorderRadius'
        QtMocHelpers::PropertyData<int>(20, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 8),
        // property 'pCardPixMode'
        QtMocHelpers::PropertyData<ElaCardPixType::PixMode>(21, 0x80000000 | 22, QMC::DefaultPropertyFlags | QMC::Writable | QMC::EnumOrFlag, 9),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaReminderCard, qt_meta_tag_ZN15ElaReminderCardE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT static const QMetaObject::SuperData qt_meta_extradata_ZN15ElaReminderCardE[] = {
    QMetaObject::SuperData::link<ElaCardPixType::staticMetaObject>(),
    nullptr
};

Q_CONSTINIT const QMetaObject ElaReminderCard::staticMetaObject = { {
    QMetaObject::SuperData::link<QPushButton::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15ElaReminderCardE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15ElaReminderCardE_t>.data,
    qt_static_metacall,
    qt_meta_extradata_ZN15ElaReminderCardE,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN15ElaReminderCardE_t>.metaTypes,
    nullptr
} };

void ElaReminderCard::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaReminderCard *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pBorderRadiusChanged(); break;
        case 1: _t->pTitleChanged(); break;
        case 2: _t->pSubTitleChanged(); break;
        case 3: _t->pTitlePixelSizeChanged(); break;
        case 4: _t->pSubTitlePixelSizeChanged(); break;
        case 5: _t->pTitleSpacingChanged(); break;
        case 6: _t->pCardPixmapChanged(); break;
        case 7: _t->pCardPixmapSizeChanged(); break;
        case 8: _t->pCardPixmapBorderRadiusChanged(); break;
        case 9: _t->pCardPixModeChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaReminderCard::*)()>(_a, &ElaReminderCard::pBorderRadiusChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaReminderCard::*)()>(_a, &ElaReminderCard::pTitleChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaReminderCard::*)()>(_a, &ElaReminderCard::pSubTitleChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaReminderCard::*)()>(_a, &ElaReminderCard::pTitlePixelSizeChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaReminderCard::*)()>(_a, &ElaReminderCard::pSubTitlePixelSizeChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaReminderCard::*)()>(_a, &ElaReminderCard::pTitleSpacingChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaReminderCard::*)()>(_a, &ElaReminderCard::pCardPixmapChanged, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaReminderCard::*)()>(_a, &ElaReminderCard::pCardPixmapSizeChanged, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaReminderCard::*)()>(_a, &ElaReminderCard::pCardPixmapBorderRadiusChanged, 8))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaReminderCard::*)()>(_a, &ElaReminderCard::pCardPixModeChanged, 9))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = _t->getBorderRadius(); break;
        case 1: *reinterpret_cast<QString*>(_v) = _t->getTitle(); break;
        case 2: *reinterpret_cast<QString*>(_v) = _t->getSubTitle(); break;
        case 3: *reinterpret_cast<int*>(_v) = _t->getTitlePixelSize(); break;
        case 4: *reinterpret_cast<int*>(_v) = _t->getSubTitlePixelSize(); break;
        case 5: *reinterpret_cast<int*>(_v) = _t->getTitleSpacing(); break;
        case 6: *reinterpret_cast<QPixmap*>(_v) = _t->getCardPixmap(); break;
        case 7: *reinterpret_cast<QSize*>(_v) = _t->getCardPixmapSize(); break;
        case 8: *reinterpret_cast<int*>(_v) = _t->getCardPixmapBorderRadius(); break;
        case 9: *reinterpret_cast<ElaCardPixType::PixMode*>(_v) = _t->getCardPixMode(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setBorderRadius(*reinterpret_cast<int*>(_v)); break;
        case 1: _t->setTitle(*reinterpret_cast<QString*>(_v)); break;
        case 2: _t->setSubTitle(*reinterpret_cast<QString*>(_v)); break;
        case 3: _t->setTitlePixelSize(*reinterpret_cast<int*>(_v)); break;
        case 4: _t->setSubTitlePixelSize(*reinterpret_cast<int*>(_v)); break;
        case 5: _t->setTitleSpacing(*reinterpret_cast<int*>(_v)); break;
        case 6: _t->setCardPixmap(*reinterpret_cast<QPixmap*>(_v)); break;
        case 7: _t->setCardPixmapSize(*reinterpret_cast<QSize*>(_v)); break;
        case 8: _t->setCardPixmapBorderRadius(*reinterpret_cast<int*>(_v)); break;
        case 9: _t->setCardPixMode(*reinterpret_cast<ElaCardPixType::PixMode*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ElaReminderCard::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaReminderCard::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15ElaReminderCardE_t>.strings))
        return static_cast<void*>(this);
    return QPushButton::qt_metacast(_clname);
}

int ElaReminderCard::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QPushButton::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 10)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 10;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    }
    return _id;
}

// SIGNAL 0
void ElaReminderCard::pBorderRadiusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaReminderCard::pTitleChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaReminderCard::pSubTitleChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ElaReminderCard::pTitlePixelSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ElaReminderCard::pSubTitlePixelSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ElaReminderCard::pTitleSpacingChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void ElaReminderCard::pCardPixmapChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void ElaReminderCard::pCardPixmapSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}

// SIGNAL 8
void ElaReminderCard::pCardPixmapBorderRadiusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 8, nullptr);
}

// SIGNAL 9
void ElaReminderCard::pCardPixModeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 9, nullptr);
}
QT_WARNING_POP
