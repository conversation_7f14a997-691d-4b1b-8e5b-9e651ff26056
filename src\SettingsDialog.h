#ifndef SETTINGSDIALOG_H
#define SETTINGSDIALOG_H

#include <QDialog>
#include <QTabWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QLineEdit>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QComboBox>
#include <QCheckBox>
#include <QPushButton>
#include <QGroupBox>
#include <QLabel>
#include <QSlider>
#include <QProgressBar>
#include <QFileDialog>
#include <QMessageBox>
#include <QTimer>

#include "ConfigManager.h"

#ifdef ELAWIDGETTOOLS_AVAILABLE
#include "ElaLineEdit.h"
#include "ElaComboBox.h"
#include "ElaCheckBox.h"
#include "ElaPushButton.h"
#include "ElaSpinBox.h"
#include "ElaSlider.h"
#include "ElaTabWidget.h"
#endif

/**
 * @brief 设置对话框类
 *
 * 提供用户配置应用程序各种设置的界面
 */
class SettingsDialog : public QDialog
{
    Q_OBJECT

public:
    explicit SettingsDialog(ConfigManager* configManager, QWidget *parent = nullptr);
    ~SettingsDialog();

public slots:
    void accept() override;
    void reject() override;

private slots:
    void onApiKeyChanged();
    void onCacheDirectoryBrowse();
    void onCacheClear();
    void onResetToDefaults();
    void onTestApiKey();
    void onImportSettings();
    void onExportSettings();

private:
    void initializeUI();
    void createGeneralTab();
    void createApiTab();
    void createCacheTab();
    void createNetworkTab();
    void createAdvancedTab();

    void loadSettings();
    void saveSettings();
    void validateSettings();

    // 工具方法
    void updateCacheUsageDisplay();
    void enableApiControls(bool enabled);
    QString formatFileSize(qint64 bytes) const;

private:
    ConfigManager* m_configManager;

    // UI 组件
    QTabWidget* m_tabWidget;

    // 通用设置
    QComboBox* m_themeComboBox;
    QComboBox* m_languageComboBox;
    QCheckBox* m_autoCheckUpdatesCheckBox;
    QCheckBox* m_debugModeCheckBox;

    // API 设置
#ifdef ELAWIDGETTOOLS_AVAILABLE
    ElaLineEdit* m_apiKeyLineEdit;
    ElaSpinBox* m_rateLimitSpinBox;
    ElaSpinBox* m_retryCountSpinBox;
#else
    QLineEdit* m_apiKeyLineEdit;
    QSpinBox* m_rateLimitSpinBox;
    QSpinBox* m_retryCountSpinBox;
#endif
    QPushButton* m_testApiButton;
    QLabel* m_apiStatusLabel;

    // 缓存设置
    QCheckBox* m_cacheEnabledCheckBox;
    QSpinBox* m_memoryCacheSizeSpinBox;
    QCheckBox* m_diskCacheEnabledCheckBox;
    QLineEdit* m_diskCacheDirectoryLineEdit;
    QPushButton* m_browseCacheDirectoryButton;
    QSpinBox* m_diskCacheMaxSizeSpinBox;
    QSpinBox* m_maxConcurrentDownloadsSpinBox;
    QPushButton* m_clearCacheButton;
    QLabel* m_cacheUsageLabel;
    QProgressBar* m_cacheUsageProgressBar;

    // 网络设置
    QSpinBox* m_downloadTimeoutSpinBox;
    QCheckBox* m_proxyEnabledCheckBox;
    QLineEdit* m_proxyHostLineEdit;
    QSpinBox* m_proxyPortSpinBox;
    QLineEdit* m_proxyUsernameLineEdit;
    QLineEdit* m_proxyPasswordLineEdit;

    // 高级设置
    QComboBox* m_logLevelComboBox;
    QSpinBox* m_imageQualitySpinBox;
    QSlider* m_imageQualitySlider;
    QCheckBox* m_autoLoadImagesCheckBox;
    QPushButton* m_resetToDefaultsButton;
    QPushButton* m_importSettingsButton;
    QPushButton* m_exportSettingsButton;

    // 按钮
    QPushButton* m_okButton;
    QPushButton* m_cancelButton;
    QPushButton* m_applyButton;

    // 状态
    bool m_settingsChanged;
};

#endif // SETTINGSDIALOG_H
