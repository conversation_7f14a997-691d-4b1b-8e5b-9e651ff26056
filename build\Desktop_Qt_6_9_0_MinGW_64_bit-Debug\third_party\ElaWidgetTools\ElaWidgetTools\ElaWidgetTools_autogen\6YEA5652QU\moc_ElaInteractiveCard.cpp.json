{"classes": [{"className": "ElaInteractiveCard", "lineNumber": 11, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "pBorderRadius", "notify": "pBorderRadiusChanged", "read": "getBorderRadius", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBorderRadius"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "p<PERSON>itle", "notify": "pTitleChanged", "read": "getTitle", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTitle"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "pSubTitle", "notify": "pSubTitleChanged", "read": "getSubTitle", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setSubTitle"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "pTitlePixelSize", "notify": "pTitlePixelSizeChanged", "read": "getTitlePixelSize", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTitlePixelSize"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "pSubTitlePixelSize", "notify": "pSubTitlePixelSizeChanged", "read": "getSubTitlePixelSize", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSubTitlePixelSize"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "pTitleSpacing", "notify": "pTitleSpacingChanged", "read": "getTitleSpacing", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTitleSpacing"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "pCardPixmap", "notify": "pCardPixmapChanged", "read": "getCardPixmap", "required": false, "scriptable": true, "stored": true, "type": "QPixmap", "user": false, "write": "setCardPixmap"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "pCardPixmapSize", "notify": "pCardPixmapSizeChanged", "read": "getCardPixmapSize", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setCardPixmapSize"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "pCardPixmapBorderRadius", "notify": "pCardPixmapBorderRadiusChanged", "read": "getCardPixmapBorderRadius", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setCardPixmapBorderRadius"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "pCardPixMode", "notify": "pCardPixModeChanged", "read": "getCardPixMode", "required": false, "scriptable": true, "stored": true, "type": "ElaCardPixType::PixMode", "user": false, "write": "setCardPixMode"}], "qualifiedClassName": "ElaInteractiveCard", "signals": [{"access": "public", "index": 0, "name": "pBorderRadiusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pTitleChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "pSubTitleChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "pTitlePixelSizeChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "pSubTitlePixelSizeChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "pTitleSpacingChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "pCardPixmapChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "pCardPixmapSizeChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "pCardPixmapBorderRadiusChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "pCardPixModeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPushButton"}]}], "inputFile": "ElaInteractiveCard.h", "outputRevision": 69}