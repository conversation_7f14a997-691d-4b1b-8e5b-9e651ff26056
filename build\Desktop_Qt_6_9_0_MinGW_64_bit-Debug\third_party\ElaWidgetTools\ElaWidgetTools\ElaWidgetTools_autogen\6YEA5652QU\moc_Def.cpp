/****************************************************************************
** Meta object code from reading C++ file 'Def.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/include/Def.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'Def.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN18ElaApplicationTypeE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaApplicationType::qt_create_metaobjectdata<qt_meta_tag_ZN18ElaApplicationTypeE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaApplicationType",
        "WindowDisplayMode",
        "Normal",
        "ElaMica",
        "Mica",
        "MicaAlt",
        "Acrylic",
        "DWMBlur"
    };

    QtMocHelpers::UintData qt_methods {
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
        // enum 'WindowDisplayMode'
        QtMocHelpers::EnumData<WindowDisplayMode>(1, 1, QMC::EnumFlags{}).add({
            {    2, WindowDisplayMode::Normal },
            {    3, WindowDisplayMode::ElaMica },
            {    4, WindowDisplayMode::Mica },
            {    5, WindowDisplayMode::MicaAlt },
            {    6, WindowDisplayMode::Acrylic },
            {    7, WindowDisplayMode::DWMBlur },
        }),
    };
    return QtMocHelpers::metaObjectData<void, qt_meta_tag_ZN18ElaApplicationTypeE_t>(QMC::PropertyAccessInStaticMetaCall, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}

static constexpr auto qt_staticMetaObjectContent_ZN18ElaApplicationTypeE =
    ElaApplicationType::qt_create_metaobjectdata<qt_meta_tag_ZN18ElaApplicationTypeE_t>();
static constexpr auto qt_staticMetaObjectStaticContent_ZN18ElaApplicationTypeE =
    qt_staticMetaObjectContent_ZN18ElaApplicationTypeE.staticData;
static constexpr auto qt_staticMetaObjectRelocatingContent_ZN18ElaApplicationTypeE =
    qt_staticMetaObjectContent_ZN18ElaApplicationTypeE.relocatingData;

Q_CONSTINIT const QMetaObject ElaApplicationType::staticMetaObject = { {
    nullptr,
    qt_staticMetaObjectStaticContent_ZN18ElaApplicationTypeE.stringdata,
    qt_staticMetaObjectStaticContent_ZN18ElaApplicationTypeE.data,
    nullptr,
    nullptr,
    qt_staticMetaObjectRelocatingContent_ZN18ElaApplicationTypeE.metaTypes,
    nullptr
} };

namespace {
struct qt_meta_tag_ZN12ElaThemeTypeE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaThemeType::qt_create_metaobjectdata<qt_meta_tag_ZN12ElaThemeTypeE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaThemeType",
        "ThemeMode",
        "Light",
        "Dark",
        "ThemeColor",
        "ScrollBarHandle",
        "ToggleSwitchNoToggledCenter",
        "WindowBase",
        "WindowCentralStackBase",
        "PrimaryNormal",
        "PrimaryHover",
        "PrimaryPress",
        "PopupBorder",
        "PopupBorderHover",
        "PopupBase",
        "PopupHover",
        "DialogBase",
        "DialogLayoutArea",
        "BasicText",
        "BasicTextInvert",
        "BasicDetailsText",
        "BasicTextNoFocus",
        "BasicTextDisable",
        "BasicTextPress",
        "BasicBorder",
        "BasicBorderDeep",
        "BasicBorderHover",
        "BasicBase",
        "BasicBaseDeep",
        "BasicDisable",
        "BasicHover",
        "BasicPress",
        "BasicSelectedHover",
        "BasicBaseLine",
        "BasicHemline",
        "BasicIndicator",
        "BasicChute",
        "BasicAlternating",
        "BasicBaseAlpha",
        "BasicBaseDeepAlpha",
        "BasicHoverAlpha",
        "BasicPressAlpha",
        "BasicSelectedAlpha",
        "BasicSelectedHoverAlpha",
        "StatusDanger"
    };

    QtMocHelpers::UintData qt_methods {
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
        // enum 'ThemeMode'
        QtMocHelpers::EnumData<ThemeMode>(1, 1, QMC::EnumFlags{}).add({
            {    2, ThemeMode::Light },
            {    3, ThemeMode::Dark },
        }),
        // enum 'ThemeColor'
        QtMocHelpers::EnumData<ThemeColor>(4, 4, QMC::EnumFlags{}).add({
            {    5, ThemeColor::ScrollBarHandle },
            {    6, ThemeColor::ToggleSwitchNoToggledCenter },
            {    7, ThemeColor::WindowBase },
            {    8, ThemeColor::WindowCentralStackBase },
            {    9, ThemeColor::PrimaryNormal },
            {   10, ThemeColor::PrimaryHover },
            {   11, ThemeColor::PrimaryPress },
            {   12, ThemeColor::PopupBorder },
            {   13, ThemeColor::PopupBorderHover },
            {   14, ThemeColor::PopupBase },
            {   15, ThemeColor::PopupHover },
            {   16, ThemeColor::DialogBase },
            {   17, ThemeColor::DialogLayoutArea },
            {   18, ThemeColor::BasicText },
            {   19, ThemeColor::BasicTextInvert },
            {   20, ThemeColor::BasicDetailsText },
            {   21, ThemeColor::BasicTextNoFocus },
            {   22, ThemeColor::BasicTextDisable },
            {   23, ThemeColor::BasicTextPress },
            {   24, ThemeColor::BasicBorder },
            {   25, ThemeColor::BasicBorderDeep },
            {   26, ThemeColor::BasicBorderHover },
            {   27, ThemeColor::BasicBase },
            {   28, ThemeColor::BasicBaseDeep },
            {   29, ThemeColor::BasicDisable },
            {   30, ThemeColor::BasicHover },
            {   31, ThemeColor::BasicPress },
            {   32, ThemeColor::BasicSelectedHover },
            {   33, ThemeColor::BasicBaseLine },
            {   34, ThemeColor::BasicHemline },
            {   35, ThemeColor::BasicIndicator },
            {   36, ThemeColor::BasicChute },
            {   37, ThemeColor::BasicAlternating },
            {   38, ThemeColor::BasicBaseAlpha },
            {   39, ThemeColor::BasicBaseDeepAlpha },
            {   40, ThemeColor::BasicHoverAlpha },
            {   41, ThemeColor::BasicPressAlpha },
            {   42, ThemeColor::BasicSelectedAlpha },
            {   43, ThemeColor::BasicSelectedHoverAlpha },
            {   44, ThemeColor::StatusDanger },
        }),
    };
    return QtMocHelpers::metaObjectData<void, qt_meta_tag_ZN12ElaThemeTypeE_t>(QMC::PropertyAccessInStaticMetaCall, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}

static constexpr auto qt_staticMetaObjectContent_ZN12ElaThemeTypeE =
    ElaThemeType::qt_create_metaobjectdata<qt_meta_tag_ZN12ElaThemeTypeE_t>();
static constexpr auto qt_staticMetaObjectStaticContent_ZN12ElaThemeTypeE =
    qt_staticMetaObjectContent_ZN12ElaThemeTypeE.staticData;
static constexpr auto qt_staticMetaObjectRelocatingContent_ZN12ElaThemeTypeE =
    qt_staticMetaObjectContent_ZN12ElaThemeTypeE.relocatingData;

Q_CONSTINIT const QMetaObject ElaThemeType::staticMetaObject = { {
    nullptr,
    qt_staticMetaObjectStaticContent_ZN12ElaThemeTypeE.stringdata,
    qt_staticMetaObjectStaticContent_ZN12ElaThemeTypeE.data,
    nullptr,
    nullptr,
    qt_staticMetaObjectRelocatingContent_ZN12ElaThemeTypeE.metaTypes,
    nullptr
} };

namespace {
struct qt_meta_tag_ZN13ElaAppBarTypeE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaAppBarType::qt_create_metaobjectdata<qt_meta_tag_ZN13ElaAppBarTypeE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaAppBarType",
        "ButtonType",
        "RouteBackButtonHint",
        "NavigationButtonHint",
        "StayTopButtonHint",
        "ThemeChangeButtonHint",
        "MinimizeButtonHint",
        "MaximizeButtonHint",
        "CloseButtonHint",
        "CustomArea",
        "LeftArea",
        "MiddleArea",
        "RightArea",
        "WMMouseActionType",
        "WMLBUTTONDOWN",
        "WMLBUTTONUP",
        "WMLBUTTONDBLCLK",
        "WMNCLBUTTONDOWN"
    };

    QtMocHelpers::UintData qt_methods {
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
        // enum 'ButtonType'
        QtMocHelpers::EnumData<ButtonType>(1, 1, QMC::EnumFlags{}).add({
            {    2, ButtonType::RouteBackButtonHint },
            {    3, ButtonType::NavigationButtonHint },
            {    4, ButtonType::StayTopButtonHint },
            {    5, ButtonType::ThemeChangeButtonHint },
            {    6, ButtonType::MinimizeButtonHint },
            {    7, ButtonType::MaximizeButtonHint },
            {    8, ButtonType::CloseButtonHint },
        }),
        // enum 'CustomArea'
        QtMocHelpers::EnumData<CustomArea>(9, 9, QMC::EnumFlags{}).add({
            {   10, CustomArea::LeftArea },
            {   11, CustomArea::MiddleArea },
            {   12, CustomArea::RightArea },
        }),
        // enum 'WMMouseActionType'
        QtMocHelpers::EnumData<WMMouseActionType>(13, 13, QMC::EnumFlags{}).add({
            {   14, WMMouseActionType::WMLBUTTONDOWN },
            {   15, WMMouseActionType::WMLBUTTONUP },
            {   16, WMMouseActionType::WMLBUTTONDBLCLK },
            {   17, WMMouseActionType::WMNCLBUTTONDOWN },
        }),
    };
    return QtMocHelpers::metaObjectData<void, qt_meta_tag_ZN13ElaAppBarTypeE_t>(QMC::PropertyAccessInStaticMetaCall, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}

static constexpr auto qt_staticMetaObjectContent_ZN13ElaAppBarTypeE =
    ElaAppBarType::qt_create_metaobjectdata<qt_meta_tag_ZN13ElaAppBarTypeE_t>();
static constexpr auto qt_staticMetaObjectStaticContent_ZN13ElaAppBarTypeE =
    qt_staticMetaObjectContent_ZN13ElaAppBarTypeE.staticData;
static constexpr auto qt_staticMetaObjectRelocatingContent_ZN13ElaAppBarTypeE =
    qt_staticMetaObjectContent_ZN13ElaAppBarTypeE.relocatingData;

Q_CONSTINIT const QMetaObject ElaAppBarType::staticMetaObject = { {
    nullptr,
    qt_staticMetaObjectStaticContent_ZN13ElaAppBarTypeE.stringdata,
    qt_staticMetaObjectStaticContent_ZN13ElaAppBarTypeE.data,
    nullptr,
    nullptr,
    qt_staticMetaObjectRelocatingContent_ZN13ElaAppBarTypeE.metaTypes,
    nullptr
} };

namespace {
struct qt_meta_tag_ZN11ElaTextTypeE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaTextType::qt_create_metaobjectdata<qt_meta_tag_ZN11ElaTextTypeE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaTextType",
        "TextStyle",
        "NoStyle",
        "Caption",
        "Body",
        "BodyStrong",
        "Subtitle",
        "Title",
        "TitleLarge",
        "Display"
    };

    QtMocHelpers::UintData qt_methods {
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
        // enum 'TextStyle'
        QtMocHelpers::EnumData<TextStyle>(1, 1, QMC::EnumFlags{}).add({
            {    2, TextStyle::NoStyle },
            {    3, TextStyle::Caption },
            {    4, TextStyle::Body },
            {    5, TextStyle::BodyStrong },
            {    6, TextStyle::Subtitle },
            {    7, TextStyle::Title },
            {    8, TextStyle::TitleLarge },
            {    9, TextStyle::Display },
        }),
    };
    return QtMocHelpers::metaObjectData<void, qt_meta_tag_ZN11ElaTextTypeE_t>(QMC::PropertyAccessInStaticMetaCall, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}

static constexpr auto qt_staticMetaObjectContent_ZN11ElaTextTypeE =
    ElaTextType::qt_create_metaobjectdata<qt_meta_tag_ZN11ElaTextTypeE_t>();
static constexpr auto qt_staticMetaObjectStaticContent_ZN11ElaTextTypeE =
    qt_staticMetaObjectContent_ZN11ElaTextTypeE.staticData;
static constexpr auto qt_staticMetaObjectRelocatingContent_ZN11ElaTextTypeE =
    qt_staticMetaObjectContent_ZN11ElaTextTypeE.relocatingData;

Q_CONSTINIT const QMetaObject ElaTextType::staticMetaObject = { {
    nullptr,
    qt_staticMetaObjectStaticContent_ZN11ElaTextTypeE.stringdata,
    qt_staticMetaObjectStaticContent_ZN11ElaTextTypeE.data,
    nullptr,
    nullptr,
    qt_staticMetaObjectRelocatingContent_ZN11ElaTextTypeE.metaTypes,
    nullptr
} };

namespace {
struct qt_meta_tag_ZN17ElaNavigationTypeE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaNavigationType::qt_create_metaobjectdata<qt_meta_tag_ZN17ElaNavigationTypeE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaNavigationType",
        "NodeOperateReturnType",
        "Success",
        "TargetNodeInvalid",
        "TargetNodeTypeError",
        "TargetNodeDepthLimit",
        "PageInvalid",
        "FooterUpperLimit",
        "NavigationDisplayMode",
        "Auto",
        "Minimal",
        "Compact",
        "Maximal",
        "NavigationNodeType",
        "PageNode",
        "FooterNode"
    };

    QtMocHelpers::UintData qt_methods {
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
        // enum 'NodeOperateReturnType'
        QtMocHelpers::EnumData<NodeOperateReturnType>(1, 1, QMC::EnumFlags{}).add({
            {    2, NodeOperateReturnType::Success },
            {    3, NodeOperateReturnType::TargetNodeInvalid },
            {    4, NodeOperateReturnType::TargetNodeTypeError },
            {    5, NodeOperateReturnType::TargetNodeDepthLimit },
            {    6, NodeOperateReturnType::PageInvalid },
            {    7, NodeOperateReturnType::FooterUpperLimit },
        }),
        // enum 'NavigationDisplayMode'
        QtMocHelpers::EnumData<NavigationDisplayMode>(8, 8, QMC::EnumFlags{}).add({
            {    9, NavigationDisplayMode::Auto },
            {   10, NavigationDisplayMode::Minimal },
            {   11, NavigationDisplayMode::Compact },
            {   12, NavigationDisplayMode::Maximal },
        }),
        // enum 'NavigationNodeType'
        QtMocHelpers::EnumData<NavigationNodeType>(13, 13, QMC::EnumFlags{}).add({
            {   14, NavigationNodeType::PageNode },
            {   15, NavigationNodeType::FooterNode },
        }),
    };
    return QtMocHelpers::metaObjectData<void, qt_meta_tag_ZN17ElaNavigationTypeE_t>(QMC::PropertyAccessInStaticMetaCall, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}

static constexpr auto qt_staticMetaObjectContent_ZN17ElaNavigationTypeE =
    ElaNavigationType::qt_create_metaobjectdata<qt_meta_tag_ZN17ElaNavigationTypeE_t>();
static constexpr auto qt_staticMetaObjectStaticContent_ZN17ElaNavigationTypeE =
    qt_staticMetaObjectContent_ZN17ElaNavigationTypeE.staticData;
static constexpr auto qt_staticMetaObjectRelocatingContent_ZN17ElaNavigationTypeE =
    qt_staticMetaObjectContent_ZN17ElaNavigationTypeE.relocatingData;

Q_CONSTINIT const QMetaObject ElaNavigationType::staticMetaObject = { {
    nullptr,
    qt_staticMetaObjectStaticContent_ZN17ElaNavigationTypeE.stringdata,
    qt_staticMetaObjectStaticContent_ZN17ElaNavigationTypeE.data,
    nullptr,
    nullptr,
    qt_staticMetaObjectRelocatingContent_ZN17ElaNavigationTypeE.metaTypes,
    nullptr
} };

namespace {
struct qt_meta_tag_ZN23ElaNavigationRouterTypeE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaNavigationRouterType::qt_create_metaobjectdata<qt_meta_tag_ZN23ElaNavigationRouterTypeE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaNavigationRouterType",
        "NavigationRouteType",
        "Success",
        "ObjectInvalid",
        "FunctionNameInvalid"
    };

    QtMocHelpers::UintData qt_methods {
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
        // enum 'NavigationRouteType'
        QtMocHelpers::EnumData<NavigationRouteType>(1, 1, QMC::EnumFlags{}).add({
            {    2, NavigationRouteType::Success },
            {    3, NavigationRouteType::ObjectInvalid },
            {    4, NavigationRouteType::FunctionNameInvalid },
        }),
    };
    return QtMocHelpers::metaObjectData<void, qt_meta_tag_ZN23ElaNavigationRouterTypeE_t>(QMC::PropertyAccessInStaticMetaCall, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}

static constexpr auto qt_staticMetaObjectContent_ZN23ElaNavigationRouterTypeE =
    ElaNavigationRouterType::qt_create_metaobjectdata<qt_meta_tag_ZN23ElaNavigationRouterTypeE_t>();
static constexpr auto qt_staticMetaObjectStaticContent_ZN23ElaNavigationRouterTypeE =
    qt_staticMetaObjectContent_ZN23ElaNavigationRouterTypeE.staticData;
static constexpr auto qt_staticMetaObjectRelocatingContent_ZN23ElaNavigationRouterTypeE =
    qt_staticMetaObjectContent_ZN23ElaNavigationRouterTypeE.relocatingData;

Q_CONSTINIT const QMetaObject ElaNavigationRouterType::staticMetaObject = { {
    nullptr,
    qt_staticMetaObjectStaticContent_ZN23ElaNavigationRouterTypeE.stringdata,
    qt_staticMetaObjectStaticContent_ZN23ElaNavigationRouterTypeE.data,
    nullptr,
    nullptr,
    qt_staticMetaObjectRelocatingContent_ZN23ElaNavigationRouterTypeE.metaTypes,
    nullptr
} };

namespace {
struct qt_meta_tag_ZN15ElaEventBusTypeE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaEventBusType::qt_create_metaobjectdata<qt_meta_tag_ZN15ElaEventBusTypeE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaEventBusType",
        "EventBusReturnType",
        "Success",
        "EventInvalid",
        "EventNameInvalid"
    };

    QtMocHelpers::UintData qt_methods {
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
        // enum 'EventBusReturnType'
        QtMocHelpers::EnumData<EventBusReturnType>(1, 1, QMC::EnumFlags{}).add({
            {    2, EventBusReturnType::Success },
            {    3, EventBusReturnType::EventInvalid },
            {    4, EventBusReturnType::EventNameInvalid },
        }),
    };
    return QtMocHelpers::metaObjectData<void, qt_meta_tag_ZN15ElaEventBusTypeE_t>(QMC::PropertyAccessInStaticMetaCall, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}

static constexpr auto qt_staticMetaObjectContent_ZN15ElaEventBusTypeE =
    ElaEventBusType::qt_create_metaobjectdata<qt_meta_tag_ZN15ElaEventBusTypeE_t>();
static constexpr auto qt_staticMetaObjectStaticContent_ZN15ElaEventBusTypeE =
    qt_staticMetaObjectContent_ZN15ElaEventBusTypeE.staticData;
static constexpr auto qt_staticMetaObjectRelocatingContent_ZN15ElaEventBusTypeE =
    qt_staticMetaObjectContent_ZN15ElaEventBusTypeE.relocatingData;

Q_CONSTINIT const QMetaObject ElaEventBusType::staticMetaObject = { {
    nullptr,
    qt_staticMetaObjectStaticContent_ZN15ElaEventBusTypeE.stringdata,
    qt_staticMetaObjectStaticContent_ZN15ElaEventBusTypeE.data,
    nullptr,
    nullptr,
    qt_staticMetaObjectRelocatingContent_ZN15ElaEventBusTypeE.metaTypes,
    nullptr
} };

namespace {
struct qt_meta_tag_ZN14ElaCardPixTypeE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaCardPixType::qt_create_metaobjectdata<qt_meta_tag_ZN14ElaCardPixTypeE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaCardPixType",
        "PixMode",
        "Default",
        "RoundedRect",
        "Ellipse"
    };

    QtMocHelpers::UintData qt_methods {
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
        // enum 'PixMode'
        QtMocHelpers::EnumData<PixMode>(1, 1, QMC::EnumFlags{}).add({
            {    2, PixMode::Default },
            {    3, PixMode::RoundedRect },
            {    4, PixMode::Ellipse },
        }),
    };
    return QtMocHelpers::metaObjectData<void, qt_meta_tag_ZN14ElaCardPixTypeE_t>(QMC::PropertyAccessInStaticMetaCall, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}

static constexpr auto qt_staticMetaObjectContent_ZN14ElaCardPixTypeE =
    ElaCardPixType::qt_create_metaobjectdata<qt_meta_tag_ZN14ElaCardPixTypeE_t>();
static constexpr auto qt_staticMetaObjectStaticContent_ZN14ElaCardPixTypeE =
    qt_staticMetaObjectContent_ZN14ElaCardPixTypeE.staticData;
static constexpr auto qt_staticMetaObjectRelocatingContent_ZN14ElaCardPixTypeE =
    qt_staticMetaObjectContent_ZN14ElaCardPixTypeE.relocatingData;

Q_CONSTINIT const QMetaObject ElaCardPixType::staticMetaObject = { {
    nullptr,
    qt_staticMetaObjectStaticContent_ZN14ElaCardPixTypeE.stringdata,
    qt_staticMetaObjectStaticContent_ZN14ElaCardPixTypeE.data,
    nullptr,
    nullptr,
    qt_staticMetaObjectRelocatingContent_ZN14ElaCardPixTypeE.metaTypes,
    nullptr
} };

namespace {
struct qt_meta_tag_ZN20ElaGraphicsSceneTypeE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaGraphicsSceneType::qt_create_metaobjectdata<qt_meta_tag_ZN20ElaGraphicsSceneTypeE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaGraphicsSceneType",
        "SceneMode",
        "Default",
        "DragMove",
        "MultiSelect",
        "ItemLink"
    };

    QtMocHelpers::UintData qt_methods {
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
        // enum 'SceneMode'
        QtMocHelpers::EnumData<SceneMode>(1, 1, QMC::EnumFlags{}).add({
            {    2, SceneMode::Default },
            {    3, SceneMode::DragMove },
            {    4, SceneMode::MultiSelect },
            {    5, SceneMode::ItemLink },
        }),
    };
    return QtMocHelpers::metaObjectData<void, qt_meta_tag_ZN20ElaGraphicsSceneTypeE_t>(QMC::PropertyAccessInStaticMetaCall, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}

static constexpr auto qt_staticMetaObjectContent_ZN20ElaGraphicsSceneTypeE =
    ElaGraphicsSceneType::qt_create_metaobjectdata<qt_meta_tag_ZN20ElaGraphicsSceneTypeE_t>();
static constexpr auto qt_staticMetaObjectStaticContent_ZN20ElaGraphicsSceneTypeE =
    qt_staticMetaObjectContent_ZN20ElaGraphicsSceneTypeE.staticData;
static constexpr auto qt_staticMetaObjectRelocatingContent_ZN20ElaGraphicsSceneTypeE =
    qt_staticMetaObjectContent_ZN20ElaGraphicsSceneTypeE.relocatingData;

Q_CONSTINIT const QMetaObject ElaGraphicsSceneType::staticMetaObject = { {
    nullptr,
    qt_staticMetaObjectStaticContent_ZN20ElaGraphicsSceneTypeE.stringdata,
    qt_staticMetaObjectStaticContent_ZN20ElaGraphicsSceneTypeE.data,
    nullptr,
    nullptr,
    qt_staticMetaObjectRelocatingContent_ZN20ElaGraphicsSceneTypeE.metaTypes,
    nullptr
} };

namespace {
struct qt_meta_tag_ZN17ElaMessageBarTypeE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaMessageBarType::qt_create_metaobjectdata<qt_meta_tag_ZN17ElaMessageBarTypeE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaMessageBarType",
        "PositionPolicy",
        "Top",
        "Left",
        "Bottom",
        "Right",
        "TopRight",
        "TopLeft",
        "BottomRight",
        "BottomLeft",
        "MessageMode",
        "Success",
        "Warning",
        "Information",
        "Error"
    };

    QtMocHelpers::UintData qt_methods {
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
        // enum 'PositionPolicy'
        QtMocHelpers::EnumData<PositionPolicy>(1, 1, QMC::EnumFlags{}).add({
            {    2, PositionPolicy::Top },
            {    3, PositionPolicy::Left },
            {    4, PositionPolicy::Bottom },
            {    5, PositionPolicy::Right },
            {    6, PositionPolicy::TopRight },
            {    7, PositionPolicy::TopLeft },
            {    8, PositionPolicy::BottomRight },
            {    9, PositionPolicy::BottomLeft },
        }),
        // enum 'MessageMode'
        QtMocHelpers::EnumData<MessageMode>(10, 10, QMC::EnumFlags{}).add({
            {   11, MessageMode::Success },
            {   12, MessageMode::Warning },
            {   13, MessageMode::Information },
            {   14, MessageMode::Error },
        }),
    };
    return QtMocHelpers::metaObjectData<void, qt_meta_tag_ZN17ElaMessageBarTypeE_t>(QMC::PropertyAccessInStaticMetaCall, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}

static constexpr auto qt_staticMetaObjectContent_ZN17ElaMessageBarTypeE =
    ElaMessageBarType::qt_create_metaobjectdata<qt_meta_tag_ZN17ElaMessageBarTypeE_t>();
static constexpr auto qt_staticMetaObjectStaticContent_ZN17ElaMessageBarTypeE =
    qt_staticMetaObjectContent_ZN17ElaMessageBarTypeE.staticData;
static constexpr auto qt_staticMetaObjectRelocatingContent_ZN17ElaMessageBarTypeE =
    qt_staticMetaObjectContent_ZN17ElaMessageBarTypeE.relocatingData;

Q_CONSTINIT const QMetaObject ElaMessageBarType::staticMetaObject = { {
    nullptr,
    qt_staticMetaObjectStaticContent_ZN17ElaMessageBarTypeE.stringdata,
    qt_staticMetaObjectStaticContent_ZN17ElaMessageBarTypeE.data,
    nullptr,
    nullptr,
    qt_staticMetaObjectRelocatingContent_ZN17ElaMessageBarTypeE.metaTypes,
    nullptr
} };

namespace {
struct qt_meta_tag_ZN19ElaProgressRingTypeE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaProgressRingType::qt_create_metaobjectdata<qt_meta_tag_ZN19ElaProgressRingTypeE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaProgressRingType",
        "ValueDisplayMode",
        "Actual",
        "Percent"
    };

    QtMocHelpers::UintData qt_methods {
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
        // enum 'ValueDisplayMode'
        QtMocHelpers::EnumData<ValueDisplayMode>(1, 1, QMC::EnumFlags{}).add({
            {    2, ValueDisplayMode::Actual },
            {    3, ValueDisplayMode::Percent },
        }),
    };
    return QtMocHelpers::metaObjectData<void, qt_meta_tag_ZN19ElaProgressRingTypeE_t>(QMC::PropertyAccessInStaticMetaCall, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}

static constexpr auto qt_staticMetaObjectContent_ZN19ElaProgressRingTypeE =
    ElaProgressRingType::qt_create_metaobjectdata<qt_meta_tag_ZN19ElaProgressRingTypeE_t>();
static constexpr auto qt_staticMetaObjectStaticContent_ZN19ElaProgressRingTypeE =
    qt_staticMetaObjectContent_ZN19ElaProgressRingTypeE.staticData;
static constexpr auto qt_staticMetaObjectRelocatingContent_ZN19ElaProgressRingTypeE =
    qt_staticMetaObjectContent_ZN19ElaProgressRingTypeE.relocatingData;

Q_CONSTINIT const QMetaObject ElaProgressRingType::staticMetaObject = { {
    nullptr,
    qt_staticMetaObjectStaticContent_ZN19ElaProgressRingTypeE.stringdata,
    qt_staticMetaObjectStaticContent_ZN19ElaProgressRingTypeE.data,
    nullptr,
    nullptr,
    qt_staticMetaObjectRelocatingContent_ZN19ElaProgressRingTypeE.metaTypes,
    nullptr
} };

namespace {
struct qt_meta_tag_ZN11ElaIconTypeE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaIconType::qt_create_metaobjectdata<qt_meta_tag_ZN11ElaIconTypeE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaIconType",
        "IconName",
        "None",
        "Broom",
        "Number00",
        "Numbe0",
        "Numbe1",
        "Numbe2",
        "Numbe3",
        "Numbe4",
        "Numbe5",
        "Numbe6",
        "Numbe7",
        "Numbe8",
        "Numbe9",
        "Degrees360",
        "A",
        "Abacus",
        "AccentGrave",
        "Acorn",
        "AddressBook",
        "AddressCard",
        "AirConditioner",
        "Airplay",
        "AlarmClock",
        "AlarmExclamation",
        "AlarmPlus",
        "Album",
        "AlbumCirclePlus",
        "AlbumCircleUser",
        "AlbumCollection",
        "AlbumCollectionCirclePlus",
        "AlarmSnooze",
        "AlbumCollectionCircleUser",
        "Alicorn",
        "Alien8bit",
        "AlignCenter",
        "AlignLeft",
        "AlignRight",
        "Ampersand",
        "Alt",
        "AnchorCircleExclamation",
        "AlignSlash",
        "Apartment",
        "Ankh",
        "AngleUp",
        "AnglesUpDown",
        "AnglesRight",
        "AnglesUp",
        "AnglesDown",
        "AnglesLeft",
        "AngleRight",
        "AngleDown",
        "AnchorCircleCheck",
        "Alien",
        "Angle90",
        "Angel",
        "Angle",
        "AnchorCircleXmark",
        "AnchorLock",
        "Anchor",
        "AlignJustify",
        "AmpGuitar",
        "Aperture",
        "Apostrophe",
        "AppleCore",
        "AngleLeft",
        "AppleWhole",
        "Archway",
        "ArrowDown",
        "ArrowDown19",
        "ArrowDownUpAcrossLine",
        "ArrowDown91",
        "ArrowDownArrowUp",
        "ArrowDownAZ",
        "ArrowDownBigSmall",
        "ArrowDownFromArc",
        "ArrowDownFromDottedLine",
        "ArrowDownFromLine",
        "ArrowDownLeft",
        "ArrowDownLeftAndArrowUpRightToCenter",
        "ArrowDownLong",
        "ArrowDownRight",
        "ArrowDownShortWide",
        "ArrowDownUpLock",
        "ArrowDownWideShort",
        "ArrowDownZA",
        "ArrowLeft",
        "ArrowLeftFromArc",
        "ArrowLeftFromLine",
        "ArrowLeftLong",
        "ArrowLeftLongToLine",
        "ArrowLeftToArc",
        "ArrowPointer",
        "ArrowDownToSquare",
        "ArrowLeftToLine",
        "ArrowRightToArc",
        "ArrowRightToBracket",
        "ArrowRightToCity",
        "ArrowRightToLine",
        "ArrowRotateLeft",
        "ArrowRotateRight",
        "ArrowsCross",
        "ArrowsDownToLine",
        "ArrowsDownToPeople",
        "ArrowsFromDottedLine",
        "ArrowsFromLine",
        "ArrowsLeftRight",
        "ArrowsSpin",
        "ArrowsSplitUpAndLeft",
        "ArrowsToCircle",
        "ArrowsToDot",
        "ArrowsToDottedLine",
        "ArrowsToEye",
        "ArrowsToLine",
        "ArrowsTurnRight",
        "ArrowsTurnToDots",
        "ArrowsUpDown",
        "ArrowsUpDownLeftRight",
        "ArrowsUpToLine",
        "ArrowTurnRight",
        "ArrowTurnUp",
        "ArrowUp",
        "ArrowUp19",
        "ArrowUp91",
        "ArrowUpArrowDown",
        "ArrowUpAZ",
        "ArrowUpBigSmall",
        "ArrowUpFromArc",
        "ArrowUpFromBracket",
        "ArrowUpFromDottedLine",
        "ArrowUpFromGroundWater",
        "ArrowUpRightDots",
        "ArrowUpRightFromSquare",
        "ArrowUpShortWide",
        "ArrowUpSmallBig",
        "ArrowUpSquareTriangle",
        "ArrowUpToArc",
        "ArrowUpToDottedLine",
        "ArrowUpToLine",
        "ArrowUpTriangleSquare",
        "ArrowUpWideShort",
        "ArrowUpZA",
        "Asterisk",
        "AwardSimple",
        "Axe",
        "AxeBattle",
        "B",
        "Baby",
        "BabyCarriage",
        "Backpack",
        "Backward",
        "BackwardFast",
        "BackwardStep",
        "Bacon",
        "Bacteria",
        "Bagel",
        "BagSeedling",
        "BagShopping",
        "BagShoppingMinus",
        "BagShoppingPlus",
        "BagsShopping",
        "Baguette",
        "Bahai",
        "BahtSign",
        "Balloon",
        "Balloons",
        "Ballot",
        "ArrowDownSquareTriangle",
        "ArrowDownSmallBig",
        "ArrowDownToArc",
        "ArrowDownToBracket",
        "ArrowDownToDottedLine",
        "ArrowDownToLine",
        "ArrowDownTriangleSquare",
        "ArrowProgress",
        "ArrowRight",
        "ArrowRightArrowLeft",
        "ArrowRightFromArc",
        "ArrowRightFromBracket",
        "ArrowRightFromLine",
        "ArrowRightLong",
        "ArrowRightLongToLine",
        "ArrowsLeftRightToLine",
        "ArrowsMaximize",
        "ArrowsMinimize",
        "ArrowsRepeat",
        "ArrowsRepeat1",
        "ArrowsRetweet",
        "ArrowsRotate",
        "ArrowsRotateReverse",
        "ArrowTrendDown",
        "ArrowTrendUp",
        "ArrowTurnDown",
        "ArrowTurnDownLeft",
        "ArrowTurnDownRight",
        "ArrowTurnLeft",
        "ArrowTurnLeftDown",
        "ArrowTurnLeftUp",
        "ArrowUpFromLine",
        "ArrowUpFromSquare",
        "ArrowUpFromWaterPump",
        "ArrowUpLeft",
        "ArrowUpLeftFromCircle",
        "ArrowUpLong",
        "ArrowUpRight",
        "ArrowUpRightAndArrowDownLeftFromCenter",
        "At",
        "Atom",
        "AtomSimple",
        "AudioDescription",
        "AudioDescriptionSlash",
        "AustralSign",
        "Avocado",
        "Award",
        "Badge",
        "Bacterium",
        "BadgeCheck",
        "BadgeDollar",
        "BadgePercent",
        "BadgerHoney",
        "BadgeSheriff",
        "Badminton",
        "BallotCheck",
        "BallPile",
        "Ban",
        "Banana",
        "BanBug",
        "Bandage",
        "BangladeshiTakaSign",
        "Banjo",
        "BanParking",
        "BanSmoking",
        "Barcode",
        "BarcodeRead",
        "BarcodeScan",
        "Bars",
        "BarsFilter",
        "BarsProgress",
        "BarsSort",
        "BarsStaggered",
        "Baseball",
        "BaseballBatBall",
        "Basketball",
        "BasketballHoop",
        "BasketShopping",
        "BasketShoppingMinus",
        "BasketShoppingPlus",
        "BasketShoppingSimple",
        "Bat",
        "Bath",
        "BatteryBolt",
        "BatteryEmpty",
        "BatteryExclamation",
        "BatteryFull",
        "BatteryHalf",
        "BatteryLow",
        "BatteryQuarter",
        "BatterySlash",
        "BatteryThreeQuarters",
        "Bed",
        "BedBunk",
        "BedEmpty",
        "BedFront",
        "BedPulse",
        "Bee",
        "BeerMug",
        "BeerMugEmpty",
        "Bell",
        "BellConcierge",
        "BellExclamation",
        "BellOn",
        "BellPlus",
        "BellRing",
        "Bells",
        "BellSchool",
        "BellSchoolSlash",
        "BellSlash",
        "BenchTree",
        "BezierCurve",
        "Bicycle",
        "Billboard",
        "Binary",
        "BinaryCircleCheck",
        "BinaryLock",
        "BinarySlash",
        "BinBottles",
        "BinBottlesRecycle",
        "Binoculars",
        "BinRecycle",
        "Biohazard",
        "Bird",
        "BitcoinSign",
        "Blanket",
        "BlanketFire",
        "Blender",
        "BlenderPhone",
        "Blinds",
        "BlindsOpen",
        "BlindsRaised",
        "Block",
        "BlockBrick",
        "BlockBrickFire",
        "BlockQuestion",
        "BlockQuote",
        "Blog",
        "Blueberries",
        "Bluetooth",
        "Bold",
        "Bolt",
        "BoltAuto",
        "BoltLightning",
        "BoltSlash",
        "Bomb",
        "Bone",
        "BoneBreak",
        "Bong",
        "Book",
        "BookArrowRight",
        "BookArrowUp",
        "BookAtlas",
        "BookBible",
        "BookBlank",
        "BookBookmark",
        "BookCircleArrowRight",
        "BookCircleArrowUp",
        "BookCopy",
        "BookFont",
        "BookHeart",
        "BookJournalWhills",
        "Bookmark",
        "BookmarkSlash",
        "BookMedical",
        "BookOpen",
        "BookOpenCover",
        "BookOpenReader",
        "BookQuran",
        "Books",
        "BookSection",
        "BookSkull",
        "BooksMedical",
        "BookSparkles",
        "BookTanakh",
        "BookUser",
        "Boombox",
        "Boot",
        "BoothCurtain",
        "BootHeeled",
        "BorderAll",
        "BorderBottom",
        "BorderBottomRight",
        "BorderCenterH",
        "BorderCenterV",
        "BorderInner",
        "BorderLeft",
        "BorderNone",
        "BorderOuter",
        "BorderRight",
        "BorderTop",
        "BorderTopLeft",
        "BoreHole",
        "BottleDroplet",
        "BottleWater",
        "BowArrow",
        "BowlChopsticks",
        "BowlChopsticksNoodles",
        "BowlFood",
        "BowlHot",
        "BowlingBall",
        "BowlingBallPin",
        "BowlingPins",
        "BowlRice",
        "BowlScoop",
        "BowlScoops",
        "BowlSoftServe",
        "BowlSpoon",
        "Box",
        "BoxArchive",
        "BoxBallot",
        "BoxCheck",
        "BoxCircleCheck",
        "BoxDollar",
        "BoxesPacking",
        "BoxesStacked",
        "BoxHeart",
        "BoxingGlove",
        "BoxOpen",
        "BoxOpenFull",
        "BoxTaped",
        "BoxTissue",
        "BracketCurly",
        "BracketCurlyRight",
        "BracketRound",
        "BracketRoundRight",
        "BracketsCurly",
        "BracketSquare",
        "BracketSquareRight",
        "BracketsRound",
        "BracketsSquare",
        "Braille",
        "Brain",
        "BrainArrowCurvedRight",
        "BrainCircuit",
        "BrakeWarning",
        "BrazilianRealSign",
        "BreadLoaf",
        "BreadSlice",
        "BreadSliceButter",
        "Bridge",
        "BridgeCircleCheck",
        "BridgeCircleExclamation",
        "BridgeCircleXmark",
        "BridgeLock",
        "BridgeSuspension",
        "BridgeWater",
        "Briefcase",
        "BriefcaseArrowRight",
        "BriefcaseBlank",
        "BriefcaseMedical",
        "Brightness",
        "BrightnessLow",
        "BringForward",
        "BringFront",
        "Broccoli",
        "Clover",
        "BroomBall",
        "BroomWide",
        "Browser",
        "Browsers",
        "Brush",
        "Bucket",
        "Bug",
        "Bugs",
        "BugSlash",
        "Building",
        "BuildingCircleArrowRight",
        "BuildingCircleCheck",
        "BuildingCircleExclamation",
        "BuildingCircleXmark",
        "BuildingColumns",
        "BuildingFlag",
        "BuildingLock",
        "BuildingMagnifyingGlass",
        "BuildingMemo",
        "BuildingNgo",
        "Buildings",
        "BuildingShield",
        "BuildingUn",
        "BuildingUser",
        "BuildingWheat",
        "Bulldozer",
        "Bullhorn",
        "Bullseye",
        "BullseyeArrow",
        "BullseyePointer",
        "Buoy",
        "BuoyMooring",
        "Burger",
        "BurgerCheese",
        "BurgerFries",
        "BurgerGlass",
        "BurgerLettuce",
        "BurgerSoda",
        "Burrito",
        "Burst",
        "Bus",
        "BusinessTime",
        "BusSchool",
        "BusSimple",
        "Butter",
        "C",
        "Cabin",
        "CabinetFiling",
        "CableCar",
        "Cactus",
        "CakeCandles",
        "CakeSlice",
        "Calculator",
        "CalculatorSimple",
        "Calendar",
        "CalendarArrowDown",
        "CalendarArrowUp",
        "CalendarCheck",
        "CalendarCircleExclamation",
        "CalendarCircleMinus",
        "CalendarCirclePlus",
        "CalendarCircleUser",
        "CalendarClock",
        "CalendarDay",
        "CalendarDays",
        "CalendarExclamation",
        "CalendarImage",
        "CalendarLines",
        "CalendarMinus",
        "CalendarHeart",
        "CalendarLinesPen",
        "CalendarPlus",
        "Calendars",
        "CalendarPen",
        "CalendarStar",
        "CalendarWeek",
        "CalendarXmark",
        "CameraCctv",
        "CalendarUsers",
        "CameraMovie",
        "Camcorder",
        "CameraRetro",
        "CameraRotate",
        "CameraSecurity",
        "CameraPolaroid",
        "CameraSlash",
        "CalendarRange",
        "CameraViewfinder",
        "Camera",
        "CameraWeb",
        "Campground",
        "CandleHolder",
        "CameraWebSlash",
        "Campfire",
        "Candy",
        "CandyCane",
        "CandyBar",
        "CanFood",
        "CandyCorn",
        "Cannabis",
        "Cannon",
        "Capsules",
        "Car",
        "Caravan",
        "CaravanSimple",
        "CarBattery",
        "CarBolt",
        "CarBuilding",
        "CarBump",
        "CarBurst",
        "CarBus",
        "CarCircleBolt",
        "CardClub",
        "CardDiamond",
        "CardHeart",
        "Cards",
        "CardsBlank",
        "CardSpade",
        "CaretDown",
        "CaretLeft",
        "CaretRight",
        "CaretUp",
        "CarGarage",
        "CarMirrors",
        "CarOn",
        "CarRear",
        "Carrot",
        "Cars",
        "CarSide",
        "CarSideBolt",
        "CartArrowDown",
        "CartArrowUp",
        "CartCircleArrowDown",
        "CartCircleArrowUp",
        "CartCircleCheck",
        "CartCircleExclamation",
        "CartCirclePlus",
        "CartCircleXmark",
        "CartFlatbed",
        "CartFlatbedBoxes",
        "CartFlatbedEmpty",
        "CartFlatbedSuitcase",
        "CarTilt",
        "CartMinus",
        "CartShopping",
        "CartPlus",
        "CartShoppingFast",
        "CarTunnel",
        "CartXmark",
        "CarWash",
        "CarWrench",
        "CashRegister",
        "CassetteBetamax",
        "CassetteTape",
        "CassetteVhs",
        "Castle",
        "Cat",
        "CatSpace",
        "Cauldron",
        "CediSign",
        "CentSign",
        "Certificate",
        "Chair",
        "ChairOffice",
        "Chalkboard",
        "ChalkboardUser",
        "ChampagneGlass",
        "ChampagneGlasses",
        "ChargingStation",
        "ChartArea",
        "ChartBar",
        "ChartBullet",
        "ChartCandlestick",
        "ChartColumn",
        "ChartGantt",
        "ChartKanban",
        "ChartLine",
        "ChartLineDown",
        "ChartLineUp",
        "ChartLineUpDown",
        "ChartMixed",
        "ChartMixedUpCircleCurrency",
        "ChartMixedUpCircleDollar",
        "ChartNetwork",
        "ChartPie",
        "ChartPieSimple",
        "ChartPieSimpleCircleCurrency",
        "ChartPieSimpleCircleDollar",
        "ChartPyramid",
        "ChartRadar",
        "ChartScatter",
        "ChartScatter3d",
        "ChartScatterBubble",
        "ChartSimple",
        "ChartSimpleHorizontal",
        "ChartTreeMap",
        "ChartUser",
        "ChartWaterfall",
        "Check",
        "CheckDouble",
        "CheckToSlot",
        "Cheese",
        "CheeseSwiss",
        "Cherries",
        "Chess",
        "ChessBishop",
        "ChessBishopPiece",
        "ChessBoard",
        "ChessClock",
        "ChessClockFlip",
        "ChessKing",
        "ChessKingPiece",
        "ChessKnight",
        "ChessKnightPiece",
        "ChessPawn",
        "ChessPawnPiece",
        "ChessQueen",
        "ChessQueenPiece",
        "ChessRook",
        "ChessRookPiece",
        "Chestnut",
        "ChevronDown",
        "ChevronLeft",
        "ChevronRight",
        "ChevronsDown",
        "ChevronsLeft",
        "ChevronsRight",
        "ChevronsUp",
        "ChevronUp",
        "ChfSign",
        "Child",
        "ChildCombatant",
        "ChildDress",
        "ChildReaching",
        "Children",
        "Chimney",
        "Chopsticks",
        "Church",
        "Circle",
        "Circle0",
        "Circle1",
        "Circle2",
        "Circle3",
        "Circle4",
        "Circle5",
        "Circle6",
        "Circle7",
        "Circle8",
        "Circle9",
        "CircleA",
        "CircleAmpersand",
        "CircleArrowDown",
        "CircleArrowDownLeft",
        "CircleArrowDownRight",
        "CircleArrowLeft",
        "CircleArrowRight",
        "CircleArrowUp",
        "CircleArrowUpLeft",
        "CircleArrowUpRight",
        "CircleB",
        "CircleBolt",
        "CircleBookmark",
        "CircleBookOpen",
        "CircleC",
        "CircleCalendar",
        "CircleCamera",
        "CircleCaretDown",
        "CircleCaretLeft",
        "CircleCaretRight",
        "CircleCaretUp",
        "CircleCheck",
        "CircleChevronDown",
        "CircleChevronLeft",
        "CircleChevronRight",
        "CircleChevronUp",
        "CircleD",
        "CircleDashed",
        "CircleDivide",
        "CircleDollar",
        "CircleDollarToSlot",
        "CircleDot",
        "CircleDown",
        "CircleDownLeft",
        "CircleDownRight",
        "CircleE",
        "CircleEllipsis",
        "CircleEllipsisVertical",
        "CircleEnvelope",
        "CircleEuro",
        "CircleExclamation",
        "CircleExclamationCheck",
        "CircleF",
        "CircleG",
        "CircleH",
        "CircleHalf",
        "CircleHalfStroke",
        "CircleHeart",
        "CircleI",
        "CircleInfo",
        "CircleJ",
        "CircleK",
        "CircleL",
        "CircleLeft",
        "CircleLocationArrow",
        "CircleM",
        "CircleMicrophone",
        "CircleMicrophoneLines",
        "CircleMinus",
        "CircleN",
        "CircleNodes",
        "CircleNotch",
        "CircleO",
        "CircleP",
        "CircleParking",
        "CirclePause",
        "CirclePhone",
        "CirclePhoneFlip",
        "CirclePhoneHangup",
        "CirclePlay",
        "CirclePlus",
        "CircleQ",
        "CircleQuarter",
        "CircleQuarters",
        "CircleQuarterStroke",
        "CircleQuestion",
        "CircleR",
        "CircleRadiation",
        "CircleRight",
        "CircleS",
        "CircleSmall",
        "CircleSort",
        "CircleSortDown",
        "CircleSortUp",
        "CirclesOverlap",
        "CircleStar",
        "CircleSterling",
        "CircleStop",
        "CircleT",
        "CircleThreeQuarters",
        "CircleThreeQuartersStroke",
        "CircleTrash",
        "CircleU",
        "CircleUp",
        "CircleUpLeft",
        "CircleUpRight",
        "CircleUser",
        "CircleV",
        "CircleVideo",
        "CircleW",
        "CircleWaveformLines",
        "CircleX",
        "CircleXmark",
        "CircleY",
        "CircleYen",
        "CircleZ",
        "Citrus",
        "CitrusSlice",
        "City",
        "Clapperboard",
        "ClapperboardPlay",
        "Clarinet",
        "ClawMarks",
        "Clipboard",
        "ClipboardCheck",
        "ClipboardList",
        "ClipboardListCheck",
        "ClipboardMedical",
        "ClipboardPrescription",
        "ClipboardQuestion",
        "ClipboardUser",
        "Clock",
        "ClockDesk",
        "ClockEight",
        "ClockEightThirty",
        "ClockEleven",
        "ClockElevenThirty",
        "ClockFive",
        "ClockFiveThirty",
        "ClockFourThirty",
        "ClockNine",
        "ClockNineThirty",
        "ClockOne",
        "ClockOneThirty",
        "ClockRotateLeft",
        "ClockSeven",
        "ClockSevenThirty",
        "ClockSix",
        "ClockSixThirty",
        "ClockTen",
        "ClockTenThirty",
        "ClockThree",
        "ClockThreeThirty",
        "ClockTwelve",
        "ClockTwelveThirty",
        "ClockTwo",
        "ClockTwoThirty",
        "Clone",
        "ClosedCaptioning",
        "ClosedCaptioningSlash",
        "ClothesHanger",
        "Cloud",
        "CloudArrowDown",
        "CloudArrowUp",
        "CloudBinary",
        "CloudBolt",
        "CloudBoltMoon",
        "CloudBoltSun",
        "CloudCheck",
        "CloudDrizzle",
        "CloudExclamation",
        "CloudFog",
        "CloudHail",
        "CloudHailMixed",
        "CloudMeatball",
        "CloudMinus",
        "CloudMoon",
        "CloudMoonRain",
        "CloudMusic",
        "CloudPlus",
        "CloudQuestion",
        "CloudRain",
        "CloudRainbow",
        "Clouds",
        "CloudShowers",
        "CloudShowersHeavy",
        "CloudShowersWater",
        "CloudSlash",
        "CloudSleet",
        "CloudsMoon",
        "CloudSnow",
        "CloudsSun",
        "CloudSun",
        "CloudSunRain",
        "CloudWord",
        "CloudXmark",
        "FaceSaluting",
        "Club",
        "Coconut",
        "Code",
        "CodeBranch",
        "CodeCommit",
        "CodeCompare",
        "CodeFork",
        "CodePullRequest",
        "CodePullRequestClosed",
        "CodeMerge",
        "CodePullRequestDraft",
        "CodeSimple",
        "CoffeeBean",
        "CoffeeBeans",
        "CoffeePot",
        "CoffinCross",
        "Coffin",
        "Coin",
        "CoinBlank",
        "CoinVertical",
        "CoinFront",
        "Colon",
        "Coins",
        "ColonSign",
        "Columns3",
        "Comet",
        "Comma",
        "Comment",
        "CommentArrowDown",
        "CommentArrowUp",
        "CommentArrowUpRight",
        "CommentCaptions",
        "CommentCheck",
        "CommentDollar",
        "CommentDots",
        "CommentCode",
        "Command",
        "CommentImage",
        "CommentHeart",
        "CommentExclamation",
        "CommentLines",
        "CommentMedical",
        "CommentMiddle",
        "CommentMiddleTop",
        "CommentMinus",
        "CommentMusic",
        "CommentPen",
        "CommentPlus",
        "CommentQuestion",
        "CommentQuote",
        "Comments",
        "CommentsDollar",
        "CommentSlash",
        "CommentSmile",
        "CommentSms",
        "CommentsQuestion",
        "CommentsQuestionCheck",
        "CommentText",
        "CommentXmark",
        "CompactDisc",
        "Compass",
        "CompassDrafting",
        "CompassSlash",
        "Compress",
        "CompressWide",
        "ComputerClassic",
        "ComputerMouse",
        "ComputerMouseScrollwheel",
        "ComputerSpeaker",
        "ContainerStorage",
        "ConveyorBelt",
        "ConveyorBeltBoxes",
        "ConveyorBeltArm",
        "CookieBite",
        "Computer",
        "Copyright",
        "Cookie",
        "Corn",
        "Corner",
        "Copy",
        "CourtSport",
        "Cow",
        "Couch",
        "CowbellCirclePlus",
        "Cowbell",
        "CrateEmpty",
        "CreditCardFront",
        "Crab",
        "CreditCardBlank",
        "CreditCard",
        "CrateApple",
        "ConveyorBeltEmpty",
        "Crop",
        "Crosshairs",
        "CropSimple",
        "Cross",
        "Croissant",
        "CricketBatBall",
        "CrosshairsSimple",
        "Crow",
        "Crown",
        "Crutch",
        "Crutches",
        "CruzeiroSign",
        "CrystalBall",
        "Cube",
        "Cubes",
        "CubesStacked",
        "Cucumber",
        "Cupcake",
        "CupStraw",
        "CupStrawSwoosh",
        "CupTogo",
        "CurlingStone",
        "Custard",
        "D",
        "Dagger",
        "Dash",
        "Database",
        "Deer",
        "DeerRudolph",
        "DeleteLeft",
        "DeleteRight",
        "Desktop",
        "Democrat",
        "DesktopArrowDown",
        "Dharmachakra",
        "DiagramCells",
        "DiagramLeanCanvas",
        "DiagramNested",
        "DiagramNext",
        "DiagramPredecessor",
        "DiagramPrevious",
        "DiagramProject",
        "DiagramSankey",
        "DiagramSubtask",
        "DiagramSuccessor",
        "DiagramVenn",
        "Dial",
        "DialHigh",
        "DialLow",
        "DialMax",
        "DialMed",
        "DialMedLow",
        "DialMin",
        "DialOff",
        "Diamond",
        "DiamondExclamation",
        "DiamondHalf",
        "DiamondHalfStroke",
        "DiamondTurnRight",
        "Dice",
        "DiceD4",
        "DiceD6",
        "DiceD8",
        "DiceD10",
        "DiceD12",
        "DiceD20",
        "DiceFive",
        "DiceFour",
        "DiceOne",
        "DiceSix",
        "DiceThree",
        "DiceTwo",
        "Dinosaur",
        "Diploma",
        "DiscDrive",
        "Disease",
        "Display",
        "DisplayArrowDown",
        "DisplayChartUp",
        "DisplayChartUpCircleCurrency",
        "DisplayChartUpCircleDollar",
        "DisplayCode",
        "DisplayMedical",
        "DisplaySlash",
        "DistributeSpacingHorizontal",
        "DistributeSpacingVertical",
        "Ditto",
        "Divide",
        "Dna",
        "Dog",
        "DogLeashed",
        "DollarSign",
        "Dolly",
        "DollyEmpty",
        "Dolphin",
        "DongSign",
        "DoNotEnter",
        "Donut",
        "DoorClosed",
        "DoorOpen",
        "Dove",
        "Down",
        "DownFromDottedLine",
        "DownFromLine",
        "DownLeft",
        "DownLeftAndUpRightToCenter",
        "Download",
        "DownLong",
        "DownRight",
        "DownToBracket",
        "DownToDottedLine",
        "DownToLine",
        "Dragon",
        "DrawCircle",
        "DrawPolygon",
        "DrawSquare",
        "Dreidel",
        "Drone",
        "DroneFront",
        "Droplet",
        "DropletDegree",
        "DropletPercent",
        "DropletSlash",
        "Drum",
        "DrumSteelpan",
        "Drumstick",
        "DrumstickBite",
        "Dryer",
        "DryerHeat",
        "Duck",
        "Dumbbell",
        "Dumpster",
        "DumpsterFire",
        "Dungeon",
        "E",
        "Ear",
        "EarDeaf",
        "EarListen",
        "EarMuffs",
        "EarthAfrica",
        "EarthAmericas",
        "EarthAsia",
        "EarthEurope",
        "EarthOceania",
        "Eclipse",
        "Egg",
        "EggFried",
        "Eggplant",
        "Eject",
        "Elephant",
        "Elevator",
        "Ellipsis",
        "EllipsisStroke",
        "EllipsisStrokeVertical",
        "EllipsisVertical",
        "EmptySet",
        "Engine",
        "EngineWarning",
        "Envelope",
        "EnvelopeCircleCheck",
        "EnvelopeDot",
        "EnvelopeOpen",
        "EnvelopeOpenDollar",
        "EnvelopeOpenText",
        "Envelopes",
        "EnvelopesBulk",
        "Equals",
        "Eraser",
        "Escalator",
        "Ethernet",
        "EuroSign",
        "Excavator",
        "Exclamation",
        "Expand",
        "ExpandWide",
        "Explosion",
        "Eye",
        "EyeDropper",
        "EyeDropperFull",
        "EyeDropperHalf",
        "EyeEvil",
        "EyeLowVision",
        "Eyes",
        "EyeSlash",
        "F",
        "FaceAngry",
        "FaceAngryHorns",
        "FaceAnguished",
        "FaceAnxiousSweat",
        "FaceAstonished",
        "FaceAwesome",
        "FaceBeamHandOverMouth",
        "FaceClouds",
        "FaceConfounded",
        "FaceConfused",
        "FaceCowboyHat",
        "FaceDiagonalMouth",
        "FaceDisappointed",
        "FaceDisguise",
        "FaceDizzy",
        "FaceDotted",
        "FaceDowncastSweat",
        "FaceDrooling",
        "FaceExhaling",
        "FaceExplode",
        "FaceExpressionless",
        "FaceEyesXmarks",
        "FaceFearful",
        "FaceFlushed",
        "FaceFrown",
        "FaceFrownOpen",
        "FaceFrownSlight",
        "FaceGlasses",
        "FaceGrimace",
        "FaceGrin",
        "FaceGrinBeam",
        "FaceGrinBeamSweat",
        "FaceGrinHearts",
        "FaceGrinSquint",
        "FaceGrinSquintTears",
        "FaceGrinStars",
        "FaceGrinTears",
        "FaceGrinTongue",
        "FaceGrinTongueSquint",
        "FaceGrinTongueWink",
        "FaceGrinWide",
        "FaceGrinWink",
        "FaceHandOverMouth",
        "FaceHandPeeking",
        "FaceHandYawn",
        "FaceHeadBandage",
        "FaceHoldingBackTears",
        "FaceHushed",
        "FaceIcicles",
        "FaceKiss",
        "FaceKissBeam",
        "FaceKissClosedEyes",
        "FaceKissWinkHeart",
        "FaceLaugh",
        "FaceLaughBeam",
        "FaceLaughSquint",
        "FaceLaughWink",
        "FaceLying",
        "FaceMask",
        "FaceMeh",
        "FaceMehBlank",
        "FaceMelting",
        "FaceMonocle",
        "FaceNauseated",
        "FaceNoseSteam",
        "FaceParty",
        "FacePensive",
        "FacePersevering",
        "FacePleading",
        "FacePouting",
        "FaceRaisedEyebrow",
        "FaceRelieved",
        "FaceRollingEyes",
        "FaceSadCry",
        "FaceSadSweat",
        "FaceSadTear",
        "Hotdog",
        "FaceScream",
        "FaceShush",
        "FaceSleeping",
        "FaceSleepy",
        "FaceSmileBeam",
        "FaceSmile",
        "FaceSmileHalo",
        "FaceSmileHearts",
        "FaceSmileHorns",
        "FaceSmilePlus",
        "FaceSmileRelaxed",
        "FaceSmileTear",
        "FaceSmileTongue",
        "FaceSmileUpsideDown",
        "FaceSmileWink",
        "FaceSmilingHands",
        "FaceSpiralEyes",
        "FaceSmirking",
        "FaceSunglasses",
        "FaceSurprise",
        "FaceSwear",
        "FaceThermometer",
        "FaceThinking",
        "FaceTired",
        "FaceTissue",
        "FaceTongueMoney",
        "FaceTongueSweat",
        "FaceUnamused",
        "FaceViewfinder",
        "FaceVomit",
        "FaceWeary",
        "FaceWoozy",
        "FaceWorried",
        "FaceZany",
        "FaceZipper",
        "Falafel",
        "Family",
        "FamilyDress",
        "FamilyPants",
        "Fan",
        "FanTable",
        "Farm",
        "Faucet",
        "FaucetDrip",
        "Fax",
        "Feather",
        "FeatherPointed",
        "Fence",
        "FerrisWheel",
        "Ferry",
        "FieldHockeyStickBall",
        "File",
        "FileArrowDown",
        "FileArrowUp",
        "FileAudio",
        "FileBinary",
        "FileCertificate",
        "FileChartColumn",
        "FileChartPie",
        "FileCheck",
        "FileCircleCheck",
        "FileCircleExclamation",
        "FileCircleInfo",
        "FileCircleMinus",
        "FileCirclePlus",
        "FileCircleQuestion",
        "FileCircleXmark",
        "FileCode",
        "FileContract",
        "FileCsv",
        "FileDashedLine",
        "FileDoc",
        "FileEps",
        "FileExclamation",
        "FileGif",
        "FileExport",
        "FileImport",
        "FileExcel",
        "FileHeart",
        "FileInvoiceDollar",
        "FileImage",
        "FileInvoice",
        "FileJpg",
        "FileLines",
        "FileMagnifyingGlass",
        "FileLock",
        "FileMedical",
        "FileMinus",
        "FileMov",
        "FileMp4",
        "FileMp3",
        "FilePdf",
        "FileMusic",
        "FilePen",
        "FilePlusMinus",
        "FilePrescription",
        "FilePlus",
        "FilePowerpoint",
        "FilePpt",
        "Files",
        "FileShield",
        "FileSignature",
        "FileSlash",
        "FilesMedical",
        "FileSpreadsheet",
        "FileSvg",
        "FileUser",
        "FileVector",
        "FileVideo",
        "FileWaveform",
        "FileWord",
        "FileXls",
        "FileXmark",
        "FileXml",
        "FileZip",
        "FileZipper",
        "Fill",
        "FillDrip",
        "Film",
        "FilmCanister",
        "Films",
        "FilmSimple",
        "FilmSlash",
        "Filter",
        "FilterCircleDollar",
        "FilterCircleXmark",
        "FilterList",
        "Filters",
        "FilterSlash",
        "Fingerprint",
        "Fire",
        "FireBurner",
        "FireExtinguisher",
        "FireFlame",
        "FireFlameCurved",
        "FireFlameSimple",
        "FireHydrant",
        "Fireplace",
        "FireSmoke",
        "Fish",
        "FishBones",
        "FishCooked",
        "FishFins",
        "FishingRod",
        "Flag",
        "FlagCheckered",
        "FlagPennant",
        "FlagSwallowtail",
        "FlagUsa",
        "Flashlight",
        "Flask",
        "FlaskGear",
        "FlaskRoundPoison",
        "FlaskRoundPotion",
        "FlaskVial",
        "Flatbread",
        "FlatbreadStuffed",
        "FloppyDisk",
        "FloppyDiskCircleArrowRight",
        "FloppyDiskCircleXmark",
        "FloppyDiskPen",
        "FloppyDisks",
        "FlorinSign",
        "Flower",
        "FlowerDaffodil",
        "FlowerTulip",
        "Flute",
        "FluxCapacitor",
        "FlyingDisc",
        "Folder",
        "FolderArrowDown",
        "FolderArrowUp",
        "FolderBookmark",
        "FolderCheck",
        "FolderClosed",
        "FolderGear",
        "FolderGrid",
        "FolderHeart",
        "FolderImage",
        "FolderMagnifyingGlass",
        "FolderMedical",
        "FolderMinus",
        "FolderMusic",
        "FolderOpen",
        "FolderPlus",
        "Folders",
        "FolderTree",
        "FolderUser",
        "FolderXmark",
        "FonduePot",
        "Font",
        "FontAwesome",
        "FontCase",
        "Football",
        "FootballHelmet",
        "Fork",
        "ForkKnife",
        "Forklift",
        "Fort",
        "Forward",
        "ForwardFast",
        "ForwardStep",
        "Frame",
        "FrancSign",
        "FrenchFries",
        "Frog",
        "Function",
        "Futbol",
        "G",
        "Galaxy",
        "GalleryThumbnails",
        "GameBoard",
        "GameBoardSimple",
        "GameConsoleHandheld",
        "GameConsoleHandheldCrank",
        "Gamepad",
        "GamepadModern",
        "Garage",
        "GarageCar",
        "GarageOpen",
        "Garlic",
        "GasPump",
        "GasPumpSlash",
        "Gauge",
        "GaugeCircleBolt",
        "GaugeCircleMinus",
        "GaugeCirclePlus",
        "GaugeHigh",
        "GaugeLow",
        "GaugeMax",
        "GaugeMin",
        "GaugeSimple",
        "GaugeSimpleHigh",
        "GaugeSimpleLow",
        "GaugeSimpleMax",
        "GaugeSimpleMin",
        "Gavel",
        "Gear",
        "GearCode",
        "GearComplex",
        "GearComplexCode",
        "Gears",
        "Gem",
        "Genderless",
        "Ghost",
        "Gif",
        "Gift",
        "GiftCard",
        "Gifts",
        "GingerbreadMan",
        "Glass",
        "GlassCitrus",
        "GlassEmpty",
        "Glasses",
        "GlassesRound",
        "GlassHalf",
        "GlassWater",
        "GlassWaterDroplet",
        "Globe",
        "GlobePointer",
        "GlobeSnow",
        "GlobeStand",
        "GoalNet",
        "GolfBallTee",
        "GolfClub",
        "GolfFlagHole",
        "Gopuram",
        "GraduationCap",
        "Gramophone",
        "Grapes",
        "Grate",
        "GrateDroplet",
        "GreaterThan",
        "GreaterThanEqual",
        "Grid",
        "Grid2",
        "Grid2Plus",
        "Grid4",
        "Grid5",
        "GridDividers",
        "GridHorizontal",
        "GridRound",
        "GridRound2",
        "GridRound2Plus",
        "GridRound4",
        "GridRound5",
        "Grill",
        "GrillFire",
        "GrillHot",
        "Grip",
        "GripDots",
        "GripDotsVertical",
        "GripLines",
        "GripVertical",
        "GripLinesVertical",
        "GroupArrowsRotate",
        "Gun",
        "GuitarElectric",
        "Guitars",
        "Guitar",
        "GuaraniSign",
        "GunSlash",
        "GunSquirt",
        "H",
        "H1",
        "H2",
        "H3",
        "H4",
        "H5",
        "H6",
        "Hammer",
        "HammerBrush",
        "HammerCrash",
        "HammerWar",
        "Hamsa",
        "Hand",
        "HandBackFist",
        "HandBackPointDown",
        "HandBackPointLeft",
        "HandBackPointRibbon",
        "HandBackPointRight",
        "HandBackPointUp",
        "Handcuffs",
        "HandDots",
        "HandFingersCrossed",
        "HandFist",
        "HandHeart",
        "HandHolding",
        "HandHoldingBox",
        "HandHoldingCircleDollar",
        "HandHoldingDollar",
        "HandHoldingDroplet",
        "HandHoldingHand",
        "HandHoldingHeart",
        "HandHoldingMagic",
        "HandHoldingMedical",
        "HandHoldingSeedling",
        "HandHoldingSkull",
        "HandHorns",
        "HandLizard",
        "HandLove",
        "HandMiddleFinger",
        "HandPeace",
        "HandPointDown",
        "HandPointer",
        "HandPointLeft",
        "HandPointRibbon",
        "HandPointRight",
        "HandPointUp",
        "Hands",
        "HandsAslInterpreting",
        "HandsBound",
        "HandsBubbles",
        "HandScissors",
        "HandsClapping",
        "Handshake",
        "HandshakeAngle",
        "HandshakeSimple",
        "HandshakeSimpleSlash",
        "HandshakeSlash",
        "HandsHolding",
        "HandsHoldingChild",
        "HandsHoldingCircle",
        "HandsHoldingDiamond",
        "HandsHoldingDollar",
        "HandsHoldingHeart",
        "HandSparkles",
        "HandSpock",
        "HandsPraying",
        "HandWave",
        "Hanukiah",
        "HardDrive",
        "Hashtag",
        "HashtagLock",
        "HatBeach",
        "HatChef",
        "HatCowboy",
        "HatCowboySide",
        "HatSanta",
        "HatWinter",
        "HatWitch",
        "HatWizard",
        "Heading",
        "Headphones",
        "HeadphonesSimple",
        "Headset",
        "HeadSide",
        "HeadSideBrain",
        "HeadSideCough",
        "HeadSideCoughSlash",
        "HeadSideGear",
        "HeadSideGoggles",
        "HeadSideHeadphones",
        "HeadSideHeart",
        "HeadSideMask",
        "HeadSideMedical",
        "HeadSideVirus",
        "Heart",
        "HeartCircleBolt",
        "HeartCircleCheck",
        "HeartCircleExclamation",
        "HeartCircleMinus",
        "HeartCirclePlus",
        "HeartCircleXmark",
        "HeartCrack",
        "HeartHalf",
        "HeartHalfStroke",
        "HeartPulse",
        "Heat",
        "Helicopter",
        "HelicopterSymbol",
        "HelmetBattle",
        "HelmetSafety",
        "HelmetUn",
        "Hexagon",
        "HexagonCheck",
        "HexagonDivide",
        "HexagonExclamation",
        "HexagonImage",
        "HexagonMinus",
        "HexagonPlus",
        "HexagonVerticalNft",
        "HexagonVerticalNftSlanted",
        "HexagonXmark",
        "HighDefinition",
        "Highlighter",
        "HighlighterLine",
        "HillAvalanche",
        "HillRockslide",
        "Hippo",
        "HockeyMask",
        "HockeyPuck",
        "HockeyStickPuck",
        "HockeySticks",
        "HollyBerry",
        "HoneyPot",
        "HoodCloak",
        "HorizontalRule",
        "Horse",
        "HorseHead",
        "HorseSaddle",
        "Hose",
        "HoseReel",
        "Hospital",
        "Hospitals",
        "HospitalUser",
        "P",
        "Hotel",
        "HotTubPerson",
        "Hourglass",
        "HourglassClock",
        "HourglassEnd",
        "HourglassHalf",
        "HourglassStart",
        "House",
        "HouseBlank",
        "HouseBuilding",
        "HouseChimney",
        "HouseChimneyBlank",
        "HouseChimneyCrack",
        "HouseChimneyHeart",
        "HouseChimneyUser",
        "HouseChimneyWindow",
        "HouseChimneyMedical",
        "HouseCircleCheck",
        "HouseCircleExclamation",
        "HouseCircleXmark",
        "HouseDay",
        "HouseCrack",
        "HouseFire",
        "HouseFlag",
        "HouseFloodWater",
        "HouseFloodWaterCircleArrowRight",
        "HouseHeart",
        "HouseLaptop",
        "HouseLock",
        "HouseMedical",
        "HouseMedicalCircleCheck",
        "HouseMedicalCircleExclamation",
        "HouseMedicalCircleXmark",
        "HouseMedicalFlag",
        "HouseNight",
        "HousePersonLeave",
        "HousePersonReturn",
        "HouseSignal",
        "HouseTree",
        "HouseTsunami",
        "HouseTurret",
        "HouseUser",
        "HouseWater",
        "HouseWindow",
        "HryvniaSign",
        "HundredPoints",
        "Hurricane",
        "Hyphen",
        "I",
        "IceCream",
        "IceSkate",
        "Icicles",
        "Icons",
        "ICursor",
        "IdBadge",
        "IdCard",
        "IdCardClip",
        "Igloo",
        "Image",
        "ImageLandscape",
        "ImagePolaroid",
        "ImagePolaroidUser",
        "ImagePortrait",
        "Images",
        "ImageSlash",
        "ImagesUser",
        "ImageUser",
        "Inboxes",
        "InboxFull",
        "InboxIn",
        "Inbox",
        "IndianRupeeSign",
        "Industry",
        "InboxOut",
        "IndustryWindows",
        "Infinity",
        "Indent",
        "Info",
        "Inhaler",
        "InputNumeric",
        "InputPipe",
        "InputText",
        "Integral",
        "Interrobang",
        "Intersection",
        "J",
        "JackOLantern",
        "Jar",
        "Italic",
        "IslandTropical",
        "JarWheat",
        "Jedi",
        "JetFighter",
        "Joystick",
        "Jug",
        "JetFighterUp",
        "Joint",
        "JugDetergent",
        "JugBottle",
        "K",
        "Kaaba",
        "Kazoo",
        "Kerning",
        "Key",
        "Keyboard",
        "KeyboardBrightness",
        "KeyboardBrightnessLow",
        "KeyboardDown",
        "KeyboardLeft",
        "Keynote",
        "KeySkeleton",
        "KeySkeletonLeftRight",
        "Khanda",
        "Kidneys",
        "KipSign",
        "KitchenSet",
        "Kite",
        "KitMedical",
        "KiwiBird",
        "KiwiFruit",
        "Knife",
        "KnifeKitchen",
        "L",
        "LacrosseStick",
        "LacrosseStickBall",
        "Lambda",
        "Lamp",
        "LampDesk",
        "LampFloor",
        "LampStreet",
        "Landmark",
        "LandmarkDome",
        "LandmarkFlag",
        "LandmarkMagnifyingGlass",
        "LandMineOn",
        "Language",
        "Laptop",
        "LaptopArrowDown",
        "LaptopBinary",
        "LaptopCode",
        "LaptopFile",
        "LaptopMedical",
        "LaptopMobile",
        "LaptopSlash",
        "LariSign",
        "Lasso",
        "LassoSparkles",
        "LayerGroup",
        "LayerMinus",
        "LayerPlus",
        "Leaf",
        "LeafHeart",
        "LeafMaple",
        "LeafOak",
        "LeafyGreen",
        "Left",
        "LeftFromLine",
        "LeftLong",
        "LeftLongToLine",
        "LeftRight",
        "LeftToLine",
        "Lemon",
        "LessThan",
        "LessThanEqual",
        "LifeRing",
        "Lightbulb",
        "LightbulbCfl",
        "LightbulbCflOn",
        "LightbulbDollar",
        "LightbulbExclamation",
        "LightbulbExclamationOn",
        "LightbulbGear",
        "LightbulbOn",
        "LightbulbSlash",
        "LightCeiling",
        "LightEmergency",
        "LightEmergencyOn",
        "Lighthouse",
        "LightsHoliday",
        "LightSwitch",
        "LightSwitchOff",
        "LightSwitchOn",
        "LineColumns",
        "LineHeight",
        "LinesLeaning",
        "Link",
        "LinkHorizontal",
        "LinkHorizontalSlash",
        "LinkSimple",
        "LinkSimpleSlash",
        "LinkSlash",
        "Lips",
        "LiraSign",
        "List",
        "ListCheck",
        "ListDropdown",
        "ListMusic",
        "ListOl",
        "ListRadio",
        "ListTimeline",
        "ListTree",
        "ListUl",
        "LitecoinSign",
        "Loader",
        "Lobster",
        "LocationArrow",
        "LocationArrowUp",
        "LocationCheck",
        "LocationCrosshairs",
        "LocationCrosshairsSlash",
        "LocationDot",
        "LocationDotSlash",
        "LocationExclamation",
        "LocationMinus",
        "LocationPen",
        "LocationPin",
        "LocationPinLock",
        "LocationPinSlash",
        "LocationPlus",
        "LocationQuestion",
        "LocationSmile",
        "LocationXmark",
        "Lock",
        "LockA",
        "LockHashtag",
        "LockKeyhole",
        "LockKeyholeOpen",
        "LockOpen",
        "Locust",
        "Lollipop",
        "Loveseat",
        "LuchadorMask",
        "Lungs",
        "LungsVirus",
        "M",
        "Mace",
        "Magnet",
        "MagnifyingGlass",
        "MagnifyingGlassArrowRight",
        "MagnifyingGlassArrowsRotate",
        "MagnifyingGlassChart",
        "MagnifyingGlassDollar",
        "MagnifyingGlassLocation",
        "MagnifyingGlassMinus",
        "MagnifyingGlassMusic",
        "MagnifyingGlassPlay",
        "MagnifyingGlassPlus",
        "MagnifyingGlassWaveform",
        "Mailbox",
        "MailboxFlagUp",
        "ManatSign",
        "Mandolin",
        "Mango",
        "Manhole",
        "Map",
        "MapLocation",
        "MapLocationDot",
        "MapPin",
        "Marker",
        "Mars",
        "MarsAndVenus",
        "MarsAndVenusBurst",
        "MarsDouble",
        "MarsStroke",
        "MarsStrokeRight",
        "MarsStrokeUp",
        "MartiniGlass",
        "MartiniGlassCitrus",
        "MartiniGlassEmpty",
        "Mask",
        "MaskFace",
        "MaskSnorkel",
        "MasksTheater",
        "MaskVentilator",
        "MattressPillow",
        "Maximize",
        "Meat",
        "Medal",
        "Megaphone",
        "Melon",
        "MelonSlice",
        "Memo",
        "MemoCircleCheck",
        "MemoCircleInfo",
        "MemoPad",
        "Memory",
        "Menorah",
        "Mercury",
        "Merge",
        "Message",
        "MessageArrowDown",
        "MessageArrowUp",
        "MessageArrowUpRight",
        "MessageBot",
        "MessageCaptions",
        "MessageCheck",
        "MessageCode",
        "MessageDollar",
        "MessageDots",
        "MessageExclamation",
        "MessageHeart",
        "MessageImage",
        "MessageLines",
        "MessageMedical",
        "MessageMiddle",
        "MessageMiddleTop",
        "MessageMinus",
        "MessageMusic",
        "MessagePen",
        "MessagePlus",
        "MessageQuestion",
        "MessageQuote",
        "Messages",
        "MessagesDollar",
        "MessageSlash",
        "MessageSmile",
        "MessageSms",
        "MessagesQuestion",
        "MessageText",
        "MessageXmark",
        "Meteor",
        "Meter",
        "MeterBolt",
        "MeterDroplet",
        "MeterFire",
        "Microchip",
        "MicrochipAi",
        "Microphone",
        "MicrophoneLines",
        "MicrophoneLinesSlash",
        "MicrophoneSlash",
        "MicrophoneStand",
        "Microscope",
        "Microwave",
        "MillSign",
        "Minimize",
        "Minus",
        "Mistletoe",
        "Mitten",
        "Mobile",
        "MobileButton",
        "MobileNotch",
        "MobileRetro",
        "MobileScreen",
        "MobileScreenButton",
        "MobileSignal",
        "MobileSignalOut",
        "MoneyBill",
        "MoneyBill1",
        "MoneyBill1Wave",
        "MoneyBills",
        "MoneyBillSimple",
        "MoneyBillSimpleWave",
        "MoneyBillsSimple",
        "MoneyBillTransfer",
        "MoneyBillTrendUp",
        "MoneyBillWave",
        "MoneyBillWheat",
        "MoneyCheck",
        "MoneyCheckDollar",
        "MoneyCheckDollarPen",
        "MoneyCheckPen",
        "MoneyFromBracket",
        "MoneySimpleFromBracket",
        "MonitorWaveform",
        "Monkey",
        "Monument",
        "Moon",
        "MoonCloud",
        "MoonOverSun",
        "MoonStars",
        "Moped",
        "MortarPestle",
        "Mosque",
        "Mosquito",
        "MosquitoNet",
        "Motorcycle",
        "Mound",
        "Mountain",
        "MountainCity",
        "Mountains",
        "MountainSun",
        "MouseField",
        "Mp3Player",
        "Mug",
        "MugHot",
        "MugMarshmallows",
        "MugSaucer",
        "MugTea",
        "MugTeaSaucer",
        "Mushroom",
        "Music",
        "MusicMagnifyingGlass",
        "MusicNote",
        "MusicNoteSlash",
        "MusicSlash",
        "Mustache",
        "N",
        "NairaSign",
        "Narwhal",
        "NestingDolls",
        "NetworkWired",
        "Neuter",
        "Newspaper",
        "Nfc",
        "NfcLock",
        "NfcMagnifyingGlass",
        "NfcPen",
        "NfcSignal",
        "NfcSlash",
        "NfcSymbol",
        "NfcTrash",
        "Nose",
        "Notdef",
        "Note",
        "Notebook",
        "NoteMedical",
        "NotEqual",
        "Notes",
        "NotesMedical",
        "NoteSticky",
        "O",
        "ObjectExclude",
        "ObjectGroup",
        "ObjectIntersect",
        "ObjectsAlignBottom",
        "ObjectsAlignCenterHorizontal",
        "ObjectsAlignCenterVertical",
        "ObjectsAlignLeft",
        "ObjectsAlignRight",
        "ObjectsAlignTop",
        "ObjectsColumn",
        "ObjectSubtract",
        "ObjectUngroup",
        "ObjectUnion",
        "Octagon",
        "OctagonCheck",
        "OctagonDivide",
        "OctagonExclamation",
        "OctagonMinus",
        "OctagonPlus",
        "OctagonXmark",
        "OilCan",
        "OilCanDrip",
        "OilTemperature",
        "OilWell",
        "Olive",
        "OliveBranch",
        "Om",
        "Omega",
        "Onion",
        "Option",
        "Ornament",
        "Otter",
        "Outdent",
        "Outlet",
        "Oven",
        "Overline",
        "SignalSlash",
        "PageCaretDown",
        "Page",
        "PageCaretUp",
        "Pager",
        "Paintbrush",
        "PaintbrushFine",
        "PaintbrushPencil",
        "PaintRoller",
        "Pallet",
        "Palette",
        "PalletBox",
        "PalletBoxes",
        "Pancakes",
        "PanelEws",
        "PanelFire",
        "PanFood",
        "PanFrying",
        "Panorama",
        "Paperclip",
        "PaperclipVertical",
        "PaperPlane",
        "PaperPlaneTop",
        "ParachuteBox",
        "Paragraph",
        "PartyBell",
        "ParagraphLeft",
        "PartyHorn",
        "Passport",
        "Paste",
        "Pause",
        "Paw",
        "PawClaws",
        "PawSimple",
        "Peace",
        "Peach",
        "Peanut",
        "Peanuts",
        "Peapod",
        "Pear",
        "Pedestal",
        "Pegasus",
        "Pen",
        "Pencil",
        "PencilMechanical",
        "PencilSlash",
        "PenCircle",
        "PenClip",
        "PenClipSlash",
        "PenFancy",
        "PenFancySlash",
        "PenField",
        "PenNib",
        "PenLine",
        "PenNibSlash",
        "PenPaintbrush",
        "PenRuler",
        "PenSlash",
        "PenSwirl",
        "PenToSquare",
        "People",
        "PeopleArrows",
        "PeopleCarryBox",
        "PeopleDress",
        "PeopleDressSimple",
        "PeopleGroup",
        "PeopleLine",
        "PeoplePants",
        "PeoplePantsSimple",
        "PeoplePulling",
        "PeopleRobbery",
        "PeopleRoof",
        "PeopleSimple",
        "Pepper",
        "PepperHot",
        "Person",
        "Percent",
        "Period",
        "PersonArrowDownToLine",
        "PersonBiking",
        "PersonArrowUpFromLine",
        "PersonBikingMountain",
        "PersonBooth",
        "PersonBreastfeeding",
        "PersonBurst",
        "PersonCane",
        "PersonCarryBox",
        "PersonChalkboard",
        "PersonCircleCheck",
        "PersonCircleExclamation",
        "PersonCircleMinus",
        "PersonCirclePlus",
        "PersonCircleQuestion",
        "PersonCircleXmark",
        "PersonDigging",
        "PersonDollyEmpty",
        "PersonDotsFromLine",
        "PersonDressBurst",
        "PersonDress",
        "PersonDolly",
        "PersonDressFairy",
        "PersonDressSimple",
        "PersonDrowning",
        "PersonFairy",
        "PersonFalling",
        "PersonFallingBurst",
        "PersonFromPortal",
        "PersonHalfDress",
        "PersonHarassing",
        "PersonHiking",
        "PersonMilitaryPointing",
        "PersonMilitaryRifle",
        "PersonMilitaryToPerson",
        "PersonPinball",
        "PersonPraying",
        "PersonPregnant",
        "PersonRays",
        "PersonRifle",
        "PersonRunning",
        "PersonRunningFast",
        "PersonSeat",
        "PersonSeatReclined",
        "PersonShelter",
        "PersonSign",
        "PersonSimple",
        "PersonSkating",
        "PersonSkiing",
        "PersonSkiingNordic",
        "PersonSkiJumping",
        "PersonSkiLift",
        "PersonSledding",
        "PersonSnowboarding",
        "PersonSnowmobiling",
        "PersonSwimming",
        "PersonThroughWindow",
        "PersonToDoor",
        "PersonToPortal",
        "PersonWalking",
        "PersonWalkingArrowLoopLeft",
        "PersonWalkingArrowRight",
        "PersonWalkingDashedLineArrowRight",
        "PersonWalkingLuggage",
        "PersonWalkingWithCane",
        "PesetaSign",
        "PesoSign",
        "Phone",
        "PhoneArrowDownLeft",
        "PhoneArrowRight",
        "PhoneArrowUpRight",
        "PhoneFlip",
        "PhoneHangup",
        "PhoneIntercom",
        "PhoneMissed",
        "PhoneOffice",
        "PhonePlus",
        "PhoneRotary",
        "PhoneSlash",
        "PhoneVolume",
        "PhoneXmark",
        "PhotoFilm",
        "PhotoFilmMusic",
        "Pi",
        "Piano",
        "PiggyBank",
        "Pig",
        "PianoKeyboard",
        "Pickaxe",
        "Pinata",
        "Pinball",
        "Pie",
        "Pills",
        "Pickleball",
        "Pineapple",
        "Pipe",
        "PipeCircleCheck",
        "PipeCollar",
        "PipeSection",
        "PipeSmoking",
        "PipeValve",
        "Pizza",
        "PizzaSlice",
        "PlaceOfWorship",
        "Plane",
        "PlaneArrival",
        "PlaneCircleCheck",
        "PlaneCircleExclamation",
        "PlaneCircleXmark",
        "PlaneDeparture",
        "PlaneEngines",
        "PlaneLock",
        "PlaneProp",
        "PlaneSlash",
        "PlaneTail",
        "PlanetMoon",
        "PlanetRinged",
        "PlaneUp",
        "PlaneUpSlash",
        "PlantWilt",
        "PlateUtensils",
        "PlateWheat",
        "Play",
        "PlayPause",
        "Plug",
        "PlugCircleBolt",
        "PlugCircleCheck",
        "PlugCircleExclamation",
        "PlugCircleMinus",
        "PlugCirclePlus",
        "PlugCircleXmark",
        "Plus",
        "PlusLarge",
        "PlusMinus",
        "Podcast",
        "Podium",
        "PodiumStar",
        "PoliceBox",
        "PollPeople",
        "Pompebled",
        "Poo",
        "Pool8Ball",
        "Poop",
        "PooStorm",
        "Popcorn",
        "Popsicle",
        "Potato",
        "PotFood",
        "PowerOff",
        "Prescription",
        "PrescriptionBottle",
        "PrescriptionBottleMedical",
        "PrescriptionBottlePill",
        "PresentationScreen",
        "Pretzel",
        "Print",
        "PrintMagnifyingGlass",
        "PrintSlash",
        "Projector",
        "Pump",
        "Pumpkin",
        "PumpMedical",
        "PumpSoap",
        "Puzzle",
        "PuzzlePiece",
        "PuzzlePieceSimple",
        "Q",
        "Qrcode",
        "Question",
        "QuoteLeft",
        "QuoteRight",
        "Quotes",
        "R",
        "Rabbit",
        "RabbitRunning",
        "Raccoon",
        "Racquet",
        "Radar",
        "Radiation",
        "Radio",
        "RadioTuner",
        "Rainbow",
        "Raindrops",
        "Ram",
        "RampLoading",
        "RankingStar",
        "Raygun",
        "Receipt",
        "RecordVinyl",
        "Rectangle",
        "RectangleAd",
        "RectangleBarcode",
        "RectangleCode",
        "RectangleHistory",
        "RectangleHistoryCirclePlus",
        "RectangleHistoryCircleUser",
        "RectangleList",
        "RectanglePro",
        "RectanglesMixed",
        "RectangleTerminal",
        "RectangleVertical",
        "RectangleVerticalHistory",
        "RectangleWide",
        "RectangleXmark",
        "Recycle",
        "Reel",
        "ReflectHorizontal",
        "ReflectVertical",
        "Refrigerator",
        "Registered",
        "Repeat",
        "Repeat1",
        "Reply",
        "ReplyAll",
        "ReplyClock",
        "Republican",
        "Restroom",
        "RestroomSimple",
        "Retweet",
        "Rhombus",
        "Ribbon",
        "Right",
        "RightFromBracket",
        "RightFromLine",
        "RightLeft",
        "RightLeftLarge",
        "RightLong",
        "RightLongToLine",
        "RightToBracket",
        "RightToLine",
        "Ring",
        "RingDiamond",
        "RingsWedding",
        "Road",
        "RoadBarrier",
        "RoadBridge",
        "RoadCircleCheck",
        "RoadCircleExclamation",
        "RoadCircleXmark",
        "RoadLock",
        "RoadSpikes",
        "Robot",
        "RobotAstromech",
        "Rocket",
        "RocketLaunch",
        "RollerCoaster",
        "Rotate",
        "RotateExclamation",
        "RotateLeft",
        "RotateReverse",
        "RotateRight",
        "Route",
        "RouteHighway",
        "RouteInterstate",
        "Router",
        "Rss",
        "RubleSign",
        "Rug",
        "RugbyBall",
        "Ruler",
        "RulerCombined",
        "RulerHorizontal",
        "RulerTriangle",
        "RulerVertical",
        "RupeeSign",
        "RupiahSign",
        "Rv",
        "S",
        "Sack",
        "SackDollar",
        "SackXmark",
        "Sailboat",
        "Salad",
        "SaltShaker",
        "Sandwich",
        "Satellite",
        "SatelliteDish",
        "Sausage",
        "Saxophone",
        "SaxophoneFire",
        "ScaleBalanced",
        "ScaleUnbalanced",
        "ScaleUnbalancedFlip",
        "Scalpel",
        "ScalpelLineDashed",
        "ScannerGun",
        "ScannerImage",
        "ScannerKeyboard",
        "ScannerTouchscreen",
        "Scarecrow",
        "Scarf",
        "School",
        "SchoolCircleCheck",
        "SchoolCircleExclamation",
        "SchoolCircleXmark",
        "SchoolFlag",
        "SchoolLock",
        "Scissors",
        "Screencast",
        "ScreenUsers",
        "Screwdriver",
        "ScrewdriverWrench",
        "Scribble",
        "Scroll",
        "ScrollOld",
        "ScrollTorah",
        "Scrubber",
        "Scythe",
        "SdCard",
        "SdCards",
        "Seal",
        "SealExclamation",
        "SealQuestion",
        "SeatAirline",
        "Section",
        "Seedling",
        "Semicolon",
        "SendBack",
        "SendBackward",
        "Sensor",
        "SensorCloud",
        "SensorFire",
        "SensorOn",
        "SensorTriangleExclamation",
        "Server",
        "Shapes",
        "Share",
        "ShareAll",
        "ShareFromSquare",
        "ShareNodes",
        "Sheep",
        "SheetPlastic",
        "ShekelSign",
        "Shelves",
        "ShelvesEmpty",
        "Shield",
        "ShieldCat",
        "ShieldCheck",
        "ShieldCross",
        "ShieldDog",
        "ShieldExclamation",
        "ShieldHalved",
        "ShieldHeart",
        "ShieldKeyhole",
        "ShieldMinus",
        "ShieldPlus",
        "ShieldQuartered",
        "ShieldSlash",
        "ShieldVirus",
        "ShieldXmark",
        "Ship",
        "Shirt",
        "ShirtLongSleeve",
        "ShirtRunning",
        "ShirtTankTop",
        "ShishKebab",
        "ShoePrints",
        "Shop",
        "ShopLock",
        "ShopSlash",
        "Shovel",
        "ShovelSnow",
        "Shower",
        "ShowerDown",
        "Shredder",
        "Shrimp",
        "Shuffle",
        "Shutters",
        "Shuttlecock",
        "ShuttleSpace",
        "Sickle",
        "Sidebar",
        "SidebarFlip",
        "Sigma",
        "Signal",
        "SignalBars",
        "SignalBarsFair",
        "SignalBarsGood",
        "SignalBarsSlash",
        "SignalBarsWeak",
        "SignalFair",
        "SignalGood",
        "UserHairBuns",
        "SignalStream",
        "SignalStreamSlash",
        "SignalStrong",
        "SignalWeak",
        "Signature",
        "SignatureLock",
        "SignatureSlash",
        "SignHanging",
        "SignPost",
        "SignPosts",
        "SignPostsWrench",
        "SignsPost",
        "SimCard",
        "SimCards",
        "Sink",
        "Siren",
        "SirenOn",
        "Sitemap",
        "Skeleton",
        "SkeletonRibs",
        "SkiBoot",
        "SkiBootSki",
        "Skull",
        "SkullCow",
        "SkullCrossbones",
        "Slash",
        "SlashBack",
        "SlashForward",
        "Sleigh",
        "Slider",
        "Sliders",
        "SlidersSimple",
        "SlidersUp",
        "SlotMachine",
        "Smog",
        "Smoke",
        "Smoking",
        "Snake",
        "Snooze",
        "SnowBlowing",
        "Snowflake",
        "SnowflakeDroplets",
        "Snowflakes",
        "Snowman",
        "SnowmanHead",
        "Snowplow",
        "Soap",
        "Socks",
        "SoftServe",
        "SolarPanel",
        "SolarSystem",
        "Sort",
        "SortDown",
        "SortUp",
        "Spa",
        "SpaceStationMoon",
        "SpaceStationMoonConstruction",
        "Spade",
        "SpaghettiMonsterFlying",
        "Sparkles",
        "Speaker",
        "Speakers",
        "SpiderBlackWidow",
        "SpiderWeb",
        "Sparkle",
        "Spinner",
        "Spider",
        "SpinnerScale",
        "SpinnerThird",
        "Splotch",
        "Split",
        "SprayCan",
        "SprayCanSparkles",
        "Sportsball",
        "Sprinkler",
        "SprinklerCeiling",
        "Square",
        "Square3",
        "Square0",
        "Square1",
        "SpellCheck",
        "Square4",
        "Square5",
        "Spoon",
        "Square2",
        "Square7",
        "Square8",
        "Square6",
        "Square9",
        "SquareALock",
        "SquareA",
        "SquareArrowDown",
        "SquareAmpersand",
        "SquareArrowDownRight",
        "SquareArrowDownLeft",
        "SquareArrowLeft",
        "SquareArrowRight",
        "SquareArrowUp",
        "SquareArrowUpLeft",
        "SquareArrowUpRight",
        "SquareB",
        "SquareBolt",
        "SquareC",
        "SquareCaretDown",
        "SquareCaretLeft",
        "SquareCaretRight",
        "SquareCaretUp",
        "SquareCheck",
        "SquareChevronDown",
        "SquareChevronLeft",
        "SquareChevronRight",
        "SquareChevronUp",
        "SquareCode",
        "SquareD",
        "SquareDashed",
        "SquareDashedCirclePlus",
        "SquareDivide",
        "SquareDollar",
        "SquareDown",
        "SquareDownLeft",
        "SquareDownRight",
        "SquareE",
        "SquareEllipsis",
        "SquareEllipsisVertical",
        "SquareEnvelope",
        "SquareExclamation",
        "SquareF",
        "SquareFragile",
        "SquareFull",
        "SquareG",
        "SquareH",
        "SquareHeart",
        "SquareI",
        "SquareInfo",
        "SquareJ",
        "SquareK",
        "SquareKanban",
        "SquareL",
        "SquareLeft",
        "SquareList",
        "SquareM",
        "SquareMinus",
        "SquareN",
        "SquareNfi",
        "SquareO",
        "SquareP",
        "SquareParking",
        "SquareParkingSlash",
        "SquarePen",
        "SquarePersonConfined",
        "SquarePhone",
        "SquarePhoneFlip",
        "SquarePhoneHangup",
        "SquarePlus",
        "SquarePollHorizontal",
        "SquarePollVertical",
        "SquareQ",
        "SquareQuarters",
        "SquareQuestion",
        "SquareQuote",
        "SquareR",
        "SquareRight",
        "SquareRing",
        "SquareRoot",
        "SquareRootVariable",
        "SquareRss",
        "SquareS",
        "SquareShareNodes",
        "SquareSliders",
        "SquareSlidersVertical",
        "SquareSmall",
        "SquareStar",
        "SquareT",
        "SquareTerminal",
        "SquareThisWayUp",
        "SquareU",
        "SquareUp",
        "SquareUpLeft",
        "SquareUpRight",
        "SquareUser",
        "SquareV",
        "SquareVirus",
        "SquareW",
        "SquareX",
        "SquareXmark",
        "SquareY",
        "SquareZ",
        "Squid",
        "Squirrel",
        "Staff",
        "StaffSnake",
        "Stairs",
        "Stamp",
        "StandardDefinition",
        "Stapler",
        "Star",
        "StarAndCrescent",
        "StarChristmas",
        "StarExclamation",
        "Starfighter",
        "StarfighterTwinIonEngine",
        "StarfighterTwinIonEngineAdvanced",
        "StarHalf",
        "StarHalfStroke",
        "StarOfDavid",
        "StarOfLife",
        "Stars",
        "StarSharp",
        "StarSharpHalf",
        "StarSharpHalfStroke",
        "Starship",
        "StarshipFreighter",
        "StarShooting",
        "Steak",
        "SteeringWheel",
        "SterlingSign",
        "Stethoscope",
        "Stocking",
        "Stomach",
        "Stop",
        "Stopwatch",
        "Stopwatch20",
        "Store",
        "StoreLock",
        "StoreSlash",
        "Strawberry",
        "StreetView",
        "Stretcher",
        "Strikethrough",
        "Stroopwafel",
        "Subscript",
        "Subtitles",
        "SubtitlesSlash",
        "Suitcase",
        "SuitcaseMedical",
        "SuitcaseRolling",
        "Sun",
        "SunBright",
        "SunCloud",
        "SunDust",
        "Sunglasses",
        "SunHaze",
        "SunPlantWilt",
        "Sunrise",
        "Sunset",
        "Superscript",
        "Sushi",
        "SushiRoll",
        "Swap",
        "SwapArrows",
        "Swatchbook",
        "Sword",
        "SwordLaser",
        "SwordLaserAlt",
        "Swords",
        "SwordsLaser",
        "Symbols",
        "Synagogue",
        "Syringe",
        "T",
        "Table",
        "TableCells",
        "TableCellsLarge",
        "TableColumns",
        "TableLayout",
        "TableList",
        "TablePicnic",
        "TablePivot",
        "TableRows",
        "Tablet",
        "TabletButton",
        "TableTennisPaddleBall",
        "TableTree",
        "TabletRugged",
        "Tablets",
        "TabletScreen",
        "TabletScreenButton",
        "TachographDigital",
        "Taco",
        "Tag",
        "Tags",
        "Tally",
        "Tally1",
        "Tally2",
        "Tally3",
        "Tally4",
        "Tamale",
        "TankWater",
        "Tape",
        "Tarp",
        "TarpDroplet",
        "Taxi",
        "TaxiBus",
        "TeddyBear",
        "Teeth",
        "TeethOpen",
        "Telescope",
        "TemperatureArrowDown",
        "TemperatureArrowUp",
        "TemperatureEmpty",
        "TemperatureFull",
        "TemperatureHalf",
        "TemperatureHigh",
        "TemperatureList",
        "TemperatureLow",
        "TemperatureQuarter",
        "TemperatureSnow",
        "TemperatureSun",
        "TemperatureThreeQuarters",
        "TengeSign",
        "TennisBall",
        "Tent",
        "TentArrowDownToLine",
        "TentArrowLeftRight",
        "TentArrowsDown",
        "TentArrowTurnLeft",
        "TentDoublePeak",
        "Tents",
        "Terminal",
        "Text",
        "TextHeight",
        "TextSize",
        "TextSlash",
        "TextWidth",
        "Thermometer",
        "Theta",
        "ThoughtBubble",
        "ThumbsDown",
        "ThumbsUp",
        "Thumbtack",
        "Tick",
        "Ticket",
        "TicketAirline",
        "TicketPerforated",
        "Tickets",
        "TicketsAirline",
        "TicketSimple",
        "TicketsPerforated",
        "TicketsSimple",
        "Tilde",
        "Timeline",
        "TimelineArrow",
        "Timer",
        "Tire",
        "TireFlat",
        "TirePressureWarning",
        "TireRugged",
        "ToggleLargeOff",
        "ToggleLargeOn",
        "ToggleOff",
        "ToggleOn",
        "Toilet",
        "ToiletPaper",
        "ToiletPaperBlank",
        "ToiletPaperBlankUnder",
        "ToiletPaperCheck",
        "ToiletPaperSlash",
        "ToiletPaperUnder",
        "ToiletPaperUnderSlash",
        "ToiletPaperXmark",
        "ToiletPortable",
        "ToiletsPortable",
        "Tomato",
        "Tombstone",
        "TombstoneBlank",
        "Toolbox",
        "Tooth",
        "Toothbrush",
        "ToriiGate",
        "Tornado",
        "TowerBroadcast",
        "TowerCell",
        "TowerControl",
        "TowerObservation",
        "Tractor",
        "Trademark",
        "TrafficCone",
        "TrafficLight",
        "TrafficLightGo",
        "TrafficLightSlow",
        "TrafficLightStop",
        "Trailer",
        "Train",
        "TrainSubway",
        "TrainSubwayTunnel",
        "TrainTrack",
        "TrainTram",
        "TrainTunnel",
        "TransformerBolt",
        "Transgender",
        "Transporter",
        "Transporter1",
        "Transporter2",
        "Transporter3",
        "Transporter4",
        "Transporter5",
        "Transporter6",
        "Transporter7",
        "TransporterEmpty",
        "Trash",
        "TrashArrowUp",
        "TrashCan",
        "TrashCanArrowUp",
        "TrashCanCheck",
        "TrashCanClock",
        "TrashCanList",
        "TrashCanPlus",
        "TrashCanSlash",
        "TrashCanUndo",
        "TrashCanXmark",
        "TrashCheck",
        "TrashClock",
        "TrashList",
        "TrashPlus",
        "TrashSlash",
        "TrashUndo",
        "TrashXmark",
        "TreasureChest",
        "Tree",
        "TreeChristmas",
        "TreeCity",
        "TreeDeciduous",
        "TreeDecorated",
        "TreeLarge",
        "TreePalm",
        "Trees",
        "TRex",
        "Triangle",
        "TriangleExclamation",
        "TriangleInstrument",
        "TrianglePersonDigging",
        "Tricycle",
        "TricycleAdult",
        "Trillium",
        "Trophy",
        "TrophyStar",
        "Trowel",
        "TrowelBricks",
        "Truck",
        "TruckArrowRight",
        "TruckBolt",
        "TruckClock",
        "TruckContainer",
        "TruckContainerEmpty",
        "TruckDroplet",
        "TruckFast",
        "TruckField",
        "TruckFieldUn",
        "TruckFire",
        "TruckFlatbed",
        "TruckFront",
        "TruckLadder",
        "TruckMedical",
        "TruckMonster",
        "TruckMoving",
        "TruckPickup",
        "TruckPlane",
        "TruckPlow",
        "TruckRamp",
        "TruckRampBox",
        "TruckRampCouch",
        "TruckTow",
        "TruckUtensils",
        "Trumpet",
        "Tty",
        "TtyAnswer",
        "TugrikSign",
        "Turkey",
        "TurkishLiraSign",
        "TurnDown",
        "TurnDownLeft",
        "TurnDownRight",
        "TurnLeft",
        "TurnLeftDown",
        "TurnLeftUp",
        "TurnRight",
        "Turntable",
        "TurnUp",
        "Turtle",
        "Tv",
        "TvMusic",
        "TvRetro",
        "Typewriter",
        "U",
        "Ufo",
        "UfoBeam",
        "Umbrella",
        "UmbrellaBeach",
        "UmbrellaSimple",
        "Underline",
        "Unicorn",
        "UniformMartialArts",
        "Union",
        "UniversalAccess",
        "Unlock",
        "UnlockKeyhole",
        "Up",
        "UpDown",
        "UpDownLeftRight",
        "UpFromBracket",
        "UpFromDottedLine",
        "UpFromLine",
        "UpLeft",
        "Upload",
        "UpLong",
        "UpRight",
        "UpRightAndDownLeftFromCenter",
        "UpRightFromSquare",
        "UpToDottedLine",
        "UpToLine",
        "UsbDrive",
        "User",
        "UserAlien",
        "UserAstronaut",
        "UserBountyHunter",
        "UserCheck",
        "UserChef",
        "UserClock",
        "UserCowboy",
        "UserCrown",
        "UserDoctor",
        "UserDoctorHair",
        "UserDoctorHairLong",
        "UserDoctorMessage",
        "UserGear",
        "UserGraduate",
        "UserGroup",
        "UserGroupCrown",
        "UserGroupSimple",
        "UserHair",
        "XmarksLines",
        "XmarkToSlot",
        "XRay",
        "Y",
        "YenSign",
        "YinYang",
        "Z",
        "UserHairLong",
        "UserHairMullet",
        "UserHeadset",
        "UserHelmetSafety",
        "UserInjured",
        "UserLarge",
        "UserLargeSlash",
        "UserLock",
        "UserMagnifyingGlass",
        "UserMinus",
        "UserMusic",
        "UserNinja",
        "UserNurse",
        "UserNurseHair",
        "UserNurseHairLong",
        "UserPen",
        "UserPilot",
        "UserPilotTie",
        "UserPlus",
        "UserPolice",
        "UserPoliceTie",
        "UserRobot",
        "UserRobotXmarks",
        "Users",
        "UsersBetweenLines",
        "UserSecret",
        "UsersGear",
        "UserShakespeare",
        "UserShield",
        "UserSlash",
        "UsersLine",
        "UsersMedical",
        "UsersRectangle",
        "UsersSlash",
        "UsersViewfinder",
        "UserTag",
        "UserTie",
        "UserUnlock",
        "UserTieHairLong",
        "UserVisor",
        "UserVneck",
        "UserTieHair",
        "UserVneckHair",
        "UserVneckHairLong",
        "UsersRays",
        "UserXmark",
        "Utensils",
        "UtensilsSlash",
        "UtilityPole",
        "UtilityPoleDouble",
        "V",
        "Vacuum",
        "VacuumRobot",
        "ValueAbsolute",
        "VanShuttle",
        "VectorCircle",
        "Vault",
        "VectorPolygon",
        "VectorSquare",
        "Venus",
        "VenusDouble",
        "VenusMars",
        "VestPatches",
        "Vial",
        "VialCircleCheck",
        "Vials",
        "Vest",
        "Video",
        "VideoArrowDownLeft",
        "VideoSlash",
        "VideoPlus",
        "Violin",
        "VirusCovidSlash",
        "Viruses",
        "VirusSlash",
        "Virus",
        "Vihara",
        "VirusCovid",
        "Volcano",
        "VentDamper",
        "VolumeSlash",
        "VolumeXmark",
        "VrCardboard",
        "Volume",
        "VolumeLow",
        "Volleyball",
        "VolumeOff",
        "Waffle",
        "VialVirus",
        "VolumeHigh",
        "Voicemail",
        "W",
        "VideoArrowUpRight",
        "WagonCovered",
        "Walker",
        "WalkieTalkie",
        "Wallet",
        "Wand",
        "WandMagic",
        "WandMagicSparkles",
        "WandSparkles",
        "Warehouse",
        "WarehouseFull",
        "WashingMachine",
        "Watch",
        "WatchApple",
        "WatchCalculator",
        "WatchFitness",
        "WatchSmart",
        "Water",
        "WaterArrowDown",
        "WaterArrowUp",
        "WaterLadder",
        "WatermelonSlice",
        "Wave",
        "Waveform",
        "WaveformLines",
        "WavePulse",
        "WaveSine",
        "WaveSquare",
        "WavesSine",
        "WaveTriangle",
        "Webhook",
        "WeightHanging",
        "WeightScale",
        "Whale",
        "Wheat",
        "WheatAwn",
        "WheatAwnCircleExclamation",
        "WheatAwnSlash",
        "WheatSlash",
        "Wheelchair",
        "WheelchairMove",
        "WhiskeyGlass",
        "WhiskeyGlassIce",
        "Whistle",
        "Wifi",
        "WifiExclamation",
        "WifiFair",
        "WifiSlash",
        "WifiWeak",
        "Wind",
        "Window",
        "WindowFlip",
        "WindowFrame",
        "WindowFrameOpen",
        "WindowMaximize",
        "WindowMinimize",
        "WindowRestore",
        "Windsock",
        "WindTurbine",
        "WindWarning",
        "WineBottle",
        "WineGlass",
        "WineGlassCrack",
        "WineGlassEmpty",
        "WonSign",
        "Worm",
        "Wreath",
        "WreathLaurel",
        "Wrench",
        "WrenchSimple",
        "X",
        "Xmark",
        "XmarkLarge"
    };

    QtMocHelpers::UintData qt_methods {
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
        // enum 'IconName'
        QtMocHelpers::EnumData<IconName>(1, 1, QMC::EnumFlags{}).add({
            {    2, IconName::None },
            {    3, IconName::Broom },
            {    4, IconName::Number00 },
            {    5, IconName::Numbe0 },
            {    6, IconName::Numbe1 },
            {    7, IconName::Numbe2 },
            {    8, IconName::Numbe3 },
            {    9, IconName::Numbe4 },
            {   10, IconName::Numbe5 },
            {   11, IconName::Numbe6 },
            {   12, IconName::Numbe7 },
            {   13, IconName::Numbe8 },
            {   14, IconName::Numbe9 },
            {   15, IconName::Degrees360 },
            {   16, IconName::A },
            {   17, IconName::Abacus },
            {   18, IconName::AccentGrave },
            {   19, IconName::Acorn },
            {   20, IconName::AddressBook },
            {   21, IconName::AddressCard },
            {   22, IconName::AirConditioner },
            {   23, IconName::Airplay },
            {   24, IconName::AlarmClock },
            {   25, IconName::AlarmExclamation },
            {   26, IconName::AlarmPlus },
            {   27, IconName::Album },
            {   28, IconName::AlbumCirclePlus },
            {   29, IconName::AlbumCircleUser },
            {   30, IconName::AlbumCollection },
            {   31, IconName::AlbumCollectionCirclePlus },
            {   32, IconName::AlarmSnooze },
            {   33, IconName::AlbumCollectionCircleUser },
            {   34, IconName::Alicorn },
            {   35, IconName::Alien8bit },
            {   36, IconName::AlignCenter },
            {   37, IconName::AlignLeft },
            {   38, IconName::AlignRight },
            {   39, IconName::Ampersand },
            {   40, IconName::Alt },
            {   41, IconName::AnchorCircleExclamation },
            {   42, IconName::AlignSlash },
            {   43, IconName::Apartment },
            {   44, IconName::Ankh },
            {   45, IconName::AngleUp },
            {   46, IconName::AnglesUpDown },
            {   47, IconName::AnglesRight },
            {   48, IconName::AnglesUp },
            {   49, IconName::AnglesDown },
            {   50, IconName::AnglesLeft },
            {   51, IconName::AngleRight },
            {   52, IconName::AngleDown },
            {   53, IconName::AnchorCircleCheck },
            {   54, IconName::Alien },
            {   55, IconName::Angle90 },
            {   56, IconName::Angel },
            {   57, IconName::Angle },
            {   58, IconName::AnchorCircleXmark },
            {   59, IconName::AnchorLock },
            {   60, IconName::Anchor },
            {   61, IconName::AlignJustify },
            {   62, IconName::AmpGuitar },
            {   63, IconName::Aperture },
            {   64, IconName::Apostrophe },
            {   65, IconName::AppleCore },
            {   66, IconName::AngleLeft },
            {   67, IconName::AppleWhole },
            {   68, IconName::Archway },
            {   69, IconName::ArrowDown },
            {   70, IconName::ArrowDown19 },
            {   71, IconName::ArrowDownUpAcrossLine },
            {   72, IconName::ArrowDown91 },
            {   73, IconName::ArrowDownArrowUp },
            {   74, IconName::ArrowDownAZ },
            {   75, IconName::ArrowDownBigSmall },
            {   76, IconName::ArrowDownFromArc },
            {   77, IconName::ArrowDownFromDottedLine },
            {   78, IconName::ArrowDownFromLine },
            {   79, IconName::ArrowDownLeft },
            {   80, IconName::ArrowDownLeftAndArrowUpRightToCenter },
            {   81, IconName::ArrowDownLong },
            {   82, IconName::ArrowDownRight },
            {   83, IconName::ArrowDownShortWide },
            {   84, IconName::ArrowDownUpLock },
            {   85, IconName::ArrowDownWideShort },
            {   86, IconName::ArrowDownZA },
            {   87, IconName::ArrowLeft },
            {   88, IconName::ArrowLeftFromArc },
            {   89, IconName::ArrowLeftFromLine },
            {   90, IconName::ArrowLeftLong },
            {   91, IconName::ArrowLeftLongToLine },
            {   92, IconName::ArrowLeftToArc },
            {   93, IconName::ArrowPointer },
            {   94, IconName::ArrowDownToSquare },
            {   95, IconName::ArrowLeftToLine },
            {   96, IconName::ArrowRightToArc },
            {   97, IconName::ArrowRightToBracket },
            {   98, IconName::ArrowRightToCity },
            {   99, IconName::ArrowRightToLine },
            {  100, IconName::ArrowRotateLeft },
            {  101, IconName::ArrowRotateRight },
            {  102, IconName::ArrowsCross },
            {  103, IconName::ArrowsDownToLine },
            {  104, IconName::ArrowsDownToPeople },
            {  105, IconName::ArrowsFromDottedLine },
            {  106, IconName::ArrowsFromLine },
            {  107, IconName::ArrowsLeftRight },
            {  108, IconName::ArrowsSpin },
            {  109, IconName::ArrowsSplitUpAndLeft },
            {  110, IconName::ArrowsToCircle },
            {  111, IconName::ArrowsToDot },
            {  112, IconName::ArrowsToDottedLine },
            {  113, IconName::ArrowsToEye },
            {  114, IconName::ArrowsToLine },
            {  115, IconName::ArrowsTurnRight },
            {  116, IconName::ArrowsTurnToDots },
            {  117, IconName::ArrowsUpDown },
            {  118, IconName::ArrowsUpDownLeftRight },
            {  119, IconName::ArrowsUpToLine },
            {  120, IconName::ArrowTurnRight },
            {  121, IconName::ArrowTurnUp },
            {  122, IconName::ArrowUp },
            {  123, IconName::ArrowUp19 },
            {  124, IconName::ArrowUp91 },
            {  125, IconName::ArrowUpArrowDown },
            {  126, IconName::ArrowUpAZ },
            {  127, IconName::ArrowUpBigSmall },
            {  128, IconName::ArrowUpFromArc },
            {  129, IconName::ArrowUpFromBracket },
            {  130, IconName::ArrowUpFromDottedLine },
            {  131, IconName::ArrowUpFromGroundWater },
            {  132, IconName::ArrowUpRightDots },
            {  133, IconName::ArrowUpRightFromSquare },
            {  134, IconName::ArrowUpShortWide },
            {  135, IconName::ArrowUpSmallBig },
            {  136, IconName::ArrowUpSquareTriangle },
            {  137, IconName::ArrowUpToArc },
            {  138, IconName::ArrowUpToDottedLine },
            {  139, IconName::ArrowUpToLine },
            {  140, IconName::ArrowUpTriangleSquare },
            {  141, IconName::ArrowUpWideShort },
            {  142, IconName::ArrowUpZA },
            {  143, IconName::Asterisk },
            {  144, IconName::AwardSimple },
            {  145, IconName::Axe },
            {  146, IconName::AxeBattle },
            {  147, IconName::B },
            {  148, IconName::Baby },
            {  149, IconName::BabyCarriage },
            {  150, IconName::Backpack },
            {  151, IconName::Backward },
            {  152, IconName::BackwardFast },
            {  153, IconName::BackwardStep },
            {  154, IconName::Bacon },
            {  155, IconName::Bacteria },
            {  156, IconName::Bagel },
            {  157, IconName::BagSeedling },
            {  158, IconName::BagShopping },
            {  159, IconName::BagShoppingMinus },
            {  160, IconName::BagShoppingPlus },
            {  161, IconName::BagsShopping },
            {  162, IconName::Baguette },
            {  163, IconName::Bahai },
            {  164, IconName::BahtSign },
            {  165, IconName::Balloon },
            {  166, IconName::Balloons },
            {  167, IconName::Ballot },
            {  168, IconName::ArrowDownSquareTriangle },
            {  169, IconName::ArrowDownSmallBig },
            {  170, IconName::ArrowDownToArc },
            {  171, IconName::ArrowDownToBracket },
            {  172, IconName::ArrowDownToDottedLine },
            {  173, IconName::ArrowDownToLine },
            {  174, IconName::ArrowDownTriangleSquare },
            {  175, IconName::ArrowProgress },
            {  176, IconName::ArrowRight },
            {  177, IconName::ArrowRightArrowLeft },
            {  178, IconName::ArrowRightFromArc },
            {  179, IconName::ArrowRightFromBracket },
            {  180, IconName::ArrowRightFromLine },
            {  181, IconName::ArrowRightLong },
            {  182, IconName::ArrowRightLongToLine },
            {  183, IconName::ArrowsLeftRightToLine },
            {  184, IconName::ArrowsMaximize },
            {  185, IconName::ArrowsMinimize },
            {  186, IconName::ArrowsRepeat },
            {  187, IconName::ArrowsRepeat1 },
            {  188, IconName::ArrowsRetweet },
            {  189, IconName::ArrowsRotate },
            {  190, IconName::ArrowsRotateReverse },
            {  191, IconName::ArrowTrendDown },
            {  192, IconName::ArrowTrendUp },
            {  193, IconName::ArrowTurnDown },
            {  194, IconName::ArrowTurnDownLeft },
            {  195, IconName::ArrowTurnDownRight },
            {  196, IconName::ArrowTurnLeft },
            {  197, IconName::ArrowTurnLeftDown },
            {  198, IconName::ArrowTurnLeftUp },
            {  199, IconName::ArrowUpFromLine },
            {  200, IconName::ArrowUpFromSquare },
            {  201, IconName::ArrowUpFromWaterPump },
            {  202, IconName::ArrowUpLeft },
            {  203, IconName::ArrowUpLeftFromCircle },
            {  204, IconName::ArrowUpLong },
            {  205, IconName::ArrowUpRight },
            {  206, IconName::ArrowUpRightAndArrowDownLeftFromCenter },
            {  207, IconName::At },
            {  208, IconName::Atom },
            {  209, IconName::AtomSimple },
            {  210, IconName::AudioDescription },
            {  211, IconName::AudioDescriptionSlash },
            {  212, IconName::AustralSign },
            {  213, IconName::Avocado },
            {  214, IconName::Award },
            {  215, IconName::Badge },
            {  216, IconName::Bacterium },
            {  217, IconName::BadgeCheck },
            {  218, IconName::BadgeDollar },
            {  219, IconName::BadgePercent },
            {  220, IconName::BadgerHoney },
            {  221, IconName::BadgeSheriff },
            {  222, IconName::Badminton },
            {  223, IconName::BallotCheck },
            {  224, IconName::BallPile },
            {  225, IconName::Ban },
            {  226, IconName::Banana },
            {  227, IconName::BanBug },
            {  228, IconName::Bandage },
            {  229, IconName::BangladeshiTakaSign },
            {  230, IconName::Banjo },
            {  231, IconName::BanParking },
            {  232, IconName::BanSmoking },
            {  233, IconName::Barcode },
            {  234, IconName::BarcodeRead },
            {  235, IconName::BarcodeScan },
            {  236, IconName::Bars },
            {  237, IconName::BarsFilter },
            {  238, IconName::BarsProgress },
            {  239, IconName::BarsSort },
            {  240, IconName::BarsStaggered },
            {  241, IconName::Baseball },
            {  242, IconName::BaseballBatBall },
            {  243, IconName::Basketball },
            {  244, IconName::BasketballHoop },
            {  245, IconName::BasketShopping },
            {  246, IconName::BasketShoppingMinus },
            {  247, IconName::BasketShoppingPlus },
            {  248, IconName::BasketShoppingSimple },
            {  249, IconName::Bat },
            {  250, IconName::Bath },
            {  251, IconName::BatteryBolt },
            {  252, IconName::BatteryEmpty },
            {  253, IconName::BatteryExclamation },
            {  254, IconName::BatteryFull },
            {  255, IconName::BatteryHalf },
            {  256, IconName::BatteryLow },
            {  257, IconName::BatteryQuarter },
            {  258, IconName::BatterySlash },
            {  259, IconName::BatteryThreeQuarters },
            {  260, IconName::Bed },
            {  261, IconName::BedBunk },
            {  262, IconName::BedEmpty },
            {  263, IconName::BedFront },
            {  264, IconName::BedPulse },
            {  265, IconName::Bee },
            {  266, IconName::BeerMug },
            {  267, IconName::BeerMugEmpty },
            {  268, IconName::Bell },
            {  269, IconName::BellConcierge },
            {  270, IconName::BellExclamation },
            {  271, IconName::BellOn },
            {  272, IconName::BellPlus },
            {  273, IconName::BellRing },
            {  274, IconName::Bells },
            {  275, IconName::BellSchool },
            {  276, IconName::BellSchoolSlash },
            {  277, IconName::BellSlash },
            {  278, IconName::BenchTree },
            {  279, IconName::BezierCurve },
            {  280, IconName::Bicycle },
            {  281, IconName::Billboard },
            {  282, IconName::Binary },
            {  283, IconName::BinaryCircleCheck },
            {  284, IconName::BinaryLock },
            {  285, IconName::BinarySlash },
            {  286, IconName::BinBottles },
            {  287, IconName::BinBottlesRecycle },
            {  288, IconName::Binoculars },
            {  289, IconName::BinRecycle },
            {  290, IconName::Biohazard },
            {  291, IconName::Bird },
            {  292, IconName::BitcoinSign },
            {  293, IconName::Blanket },
            {  294, IconName::BlanketFire },
            {  295, IconName::Blender },
            {  296, IconName::BlenderPhone },
            {  297, IconName::Blinds },
            {  298, IconName::BlindsOpen },
            {  299, IconName::BlindsRaised },
            {  300, IconName::Block },
            {  301, IconName::BlockBrick },
            {  302, IconName::BlockBrickFire },
            {  303, IconName::BlockQuestion },
            {  304, IconName::BlockQuote },
            {  305, IconName::Blog },
            {  306, IconName::Blueberries },
            {  307, IconName::Bluetooth },
            {  308, IconName::Bold },
            {  309, IconName::Bolt },
            {  310, IconName::BoltAuto },
            {  311, IconName::BoltLightning },
            {  312, IconName::BoltSlash },
            {  313, IconName::Bomb },
            {  314, IconName::Bone },
            {  315, IconName::BoneBreak },
            {  316, IconName::Bong },
            {  317, IconName::Book },
            {  318, IconName::BookArrowRight },
            {  319, IconName::BookArrowUp },
            {  320, IconName::BookAtlas },
            {  321, IconName::BookBible },
            {  322, IconName::BookBlank },
            {  323, IconName::BookBookmark },
            {  324, IconName::BookCircleArrowRight },
            {  325, IconName::BookCircleArrowUp },
            {  326, IconName::BookCopy },
            {  327, IconName::BookFont },
            {  328, IconName::BookHeart },
            {  329, IconName::BookJournalWhills },
            {  330, IconName::Bookmark },
            {  331, IconName::BookmarkSlash },
            {  332, IconName::BookMedical },
            {  333, IconName::BookOpen },
            {  334, IconName::BookOpenCover },
            {  335, IconName::BookOpenReader },
            {  336, IconName::BookQuran },
            {  337, IconName::Books },
            {  338, IconName::BookSection },
            {  339, IconName::BookSkull },
            {  340, IconName::BooksMedical },
            {  341, IconName::BookSparkles },
            {  342, IconName::BookTanakh },
            {  343, IconName::BookUser },
            {  344, IconName::Boombox },
            {  345, IconName::Boot },
            {  346, IconName::BoothCurtain },
            {  347, IconName::BootHeeled },
            {  348, IconName::BorderAll },
            {  349, IconName::BorderBottom },
            {  350, IconName::BorderBottomRight },
            {  351, IconName::BorderCenterH },
            {  352, IconName::BorderCenterV },
            {  353, IconName::BorderInner },
            {  354, IconName::BorderLeft },
            {  355, IconName::BorderNone },
            {  356, IconName::BorderOuter },
            {  357, IconName::BorderRight },
            {  358, IconName::BorderTop },
            {  359, IconName::BorderTopLeft },
            {  360, IconName::BoreHole },
            {  361, IconName::BottleDroplet },
            {  362, IconName::BottleWater },
            {  363, IconName::BowArrow },
            {  364, IconName::BowlChopsticks },
            {  365, IconName::BowlChopsticksNoodles },
            {  366, IconName::BowlFood },
            {  367, IconName::BowlHot },
            {  368, IconName::BowlingBall },
            {  369, IconName::BowlingBallPin },
            {  370, IconName::BowlingPins },
            {  371, IconName::BowlRice },
            {  372, IconName::BowlScoop },
            {  373, IconName::BowlScoops },
            {  374, IconName::BowlSoftServe },
            {  375, IconName::BowlSpoon },
            {  376, IconName::Box },
            {  377, IconName::BoxArchive },
            {  378, IconName::BoxBallot },
            {  379, IconName::BoxCheck },
            {  380, IconName::BoxCircleCheck },
            {  381, IconName::BoxDollar },
            {  382, IconName::BoxesPacking },
            {  383, IconName::BoxesStacked },
            {  384, IconName::BoxHeart },
            {  385, IconName::BoxingGlove },
            {  386, IconName::BoxOpen },
            {  387, IconName::BoxOpenFull },
            {  388, IconName::BoxTaped },
            {  389, IconName::BoxTissue },
            {  390, IconName::BracketCurly },
            {  391, IconName::BracketCurlyRight },
            {  392, IconName::BracketRound },
            {  393, IconName::BracketRoundRight },
            {  394, IconName::BracketsCurly },
            {  395, IconName::BracketSquare },
            {  396, IconName::BracketSquareRight },
            {  397, IconName::BracketsRound },
            {  398, IconName::BracketsSquare },
            {  399, IconName::Braille },
            {  400, IconName::Brain },
            {  401, IconName::BrainArrowCurvedRight },
            {  402, IconName::BrainCircuit },
            {  403, IconName::BrakeWarning },
            {  404, IconName::BrazilianRealSign },
            {  405, IconName::BreadLoaf },
            {  406, IconName::BreadSlice },
            {  407, IconName::BreadSliceButter },
            {  408, IconName::Bridge },
            {  409, IconName::BridgeCircleCheck },
            {  410, IconName::BridgeCircleExclamation },
            {  411, IconName::BridgeCircleXmark },
            {  412, IconName::BridgeLock },
            {  413, IconName::BridgeSuspension },
            {  414, IconName::BridgeWater },
            {  415, IconName::Briefcase },
            {  416, IconName::BriefcaseArrowRight },
            {  417, IconName::BriefcaseBlank },
            {  418, IconName::BriefcaseMedical },
            {  419, IconName::Brightness },
            {  420, IconName::BrightnessLow },
            {  421, IconName::BringForward },
            {  422, IconName::BringFront },
            {  423, IconName::Broccoli },
            {  424, IconName::Clover },
            {  425, IconName::BroomBall },
            {  426, IconName::BroomWide },
            {  427, IconName::Browser },
            {  428, IconName::Browsers },
            {  429, IconName::Brush },
            {  430, IconName::Bucket },
            {  431, IconName::Bug },
            {  432, IconName::Bugs },
            {  433, IconName::BugSlash },
            {  434, IconName::Building },
            {  435, IconName::BuildingCircleArrowRight },
            {  436, IconName::BuildingCircleCheck },
            {  437, IconName::BuildingCircleExclamation },
            {  438, IconName::BuildingCircleXmark },
            {  439, IconName::BuildingColumns },
            {  440, IconName::BuildingFlag },
            {  441, IconName::BuildingLock },
            {  442, IconName::BuildingMagnifyingGlass },
            {  443, IconName::BuildingMemo },
            {  444, IconName::BuildingNgo },
            {  445, IconName::Buildings },
            {  446, IconName::BuildingShield },
            {  447, IconName::BuildingUn },
            {  448, IconName::BuildingUser },
            {  449, IconName::BuildingWheat },
            {  450, IconName::Bulldozer },
            {  451, IconName::Bullhorn },
            {  452, IconName::Bullseye },
            {  453, IconName::BullseyeArrow },
            {  454, IconName::BullseyePointer },
            {  455, IconName::Buoy },
            {  456, IconName::BuoyMooring },
            {  457, IconName::Burger },
            {  458, IconName::BurgerCheese },
            {  459, IconName::BurgerFries },
            {  460, IconName::BurgerGlass },
            {  461, IconName::BurgerLettuce },
            {  462, IconName::BurgerSoda },
            {  463, IconName::Burrito },
            {  464, IconName::Burst },
            {  465, IconName::Bus },
            {  466, IconName::BusinessTime },
            {  467, IconName::BusSchool },
            {  468, IconName::BusSimple },
            {  469, IconName::Butter },
            {  470, IconName::C },
            {  471, IconName::Cabin },
            {  472, IconName::CabinetFiling },
            {  473, IconName::CableCar },
            {  474, IconName::Cactus },
            {  475, IconName::CakeCandles },
            {  476, IconName::CakeSlice },
            {  477, IconName::Calculator },
            {  478, IconName::CalculatorSimple },
            {  479, IconName::Calendar },
            {  480, IconName::CalendarArrowDown },
            {  481, IconName::CalendarArrowUp },
            {  482, IconName::CalendarCheck },
            {  483, IconName::CalendarCircleExclamation },
            {  484, IconName::CalendarCircleMinus },
            {  485, IconName::CalendarCirclePlus },
            {  486, IconName::CalendarCircleUser },
            {  487, IconName::CalendarClock },
            {  488, IconName::CalendarDay },
            {  489, IconName::CalendarDays },
            {  490, IconName::CalendarExclamation },
            {  491, IconName::CalendarImage },
            {  492, IconName::CalendarLines },
            {  493, IconName::CalendarMinus },
            {  494, IconName::CalendarHeart },
            {  495, IconName::CalendarLinesPen },
            {  496, IconName::CalendarPlus },
            {  497, IconName::Calendars },
            {  498, IconName::CalendarPen },
            {  499, IconName::CalendarStar },
            {  500, IconName::CalendarWeek },
            {  501, IconName::CalendarXmark },
            {  502, IconName::CameraCctv },
            {  503, IconName::CalendarUsers },
            {  504, IconName::CameraMovie },
            {  505, IconName::Camcorder },
            {  506, IconName::CameraRetro },
            {  507, IconName::CameraRotate },
            {  508, IconName::CameraSecurity },
            {  509, IconName::CameraPolaroid },
            {  510, IconName::CameraSlash },
            {  511, IconName::CalendarRange },
            {  512, IconName::CameraViewfinder },
            {  513, IconName::Camera },
            {  514, IconName::CameraWeb },
            {  515, IconName::Campground },
            {  516, IconName::CandleHolder },
            {  517, IconName::CameraWebSlash },
            {  518, IconName::Campfire },
            {  519, IconName::Candy },
            {  520, IconName::CandyCane },
            {  521, IconName::CandyBar },
            {  522, IconName::CanFood },
            {  523, IconName::CandyCorn },
            {  524, IconName::Cannabis },
            {  525, IconName::Cannon },
            {  526, IconName::Capsules },
            {  527, IconName::Car },
            {  528, IconName::Caravan },
            {  529, IconName::CaravanSimple },
            {  530, IconName::CarBattery },
            {  531, IconName::CarBolt },
            {  532, IconName::CarBuilding },
            {  533, IconName::CarBump },
            {  534, IconName::CarBurst },
            {  535, IconName::CarBus },
            {  536, IconName::CarCircleBolt },
            {  537, IconName::CardClub },
            {  538, IconName::CardDiamond },
            {  539, IconName::CardHeart },
            {  540, IconName::Cards },
            {  541, IconName::CardsBlank },
            {  542, IconName::CardSpade },
            {  543, IconName::CaretDown },
            {  544, IconName::CaretLeft },
            {  545, IconName::CaretRight },
            {  546, IconName::CaretUp },
            {  547, IconName::CarGarage },
            {  548, IconName::CarMirrors },
            {  549, IconName::CarOn },
            {  550, IconName::CarRear },
            {  551, IconName::Carrot },
            {  552, IconName::Cars },
            {  553, IconName::CarSide },
            {  554, IconName::CarSideBolt },
            {  555, IconName::CartArrowDown },
            {  556, IconName::CartArrowUp },
            {  557, IconName::CartCircleArrowDown },
            {  558, IconName::CartCircleArrowUp },
            {  559, IconName::CartCircleCheck },
            {  560, IconName::CartCircleExclamation },
            {  561, IconName::CartCirclePlus },
            {  562, IconName::CartCircleXmark },
            {  563, IconName::CartFlatbed },
            {  564, IconName::CartFlatbedBoxes },
            {  565, IconName::CartFlatbedEmpty },
            {  566, IconName::CartFlatbedSuitcase },
            {  567, IconName::CarTilt },
            {  568, IconName::CartMinus },
            {  569, IconName::CartShopping },
            {  570, IconName::CartPlus },
            {  571, IconName::CartShoppingFast },
            {  572, IconName::CarTunnel },
            {  573, IconName::CartXmark },
            {  574, IconName::CarWash },
            {  575, IconName::CarWrench },
            {  576, IconName::CashRegister },
            {  577, IconName::CassetteBetamax },
            {  578, IconName::CassetteTape },
            {  579, IconName::CassetteVhs },
            {  580, IconName::Castle },
            {  581, IconName::Cat },
            {  582, IconName::CatSpace },
            {  583, IconName::Cauldron },
            {  584, IconName::CediSign },
            {  585, IconName::CentSign },
            {  586, IconName::Certificate },
            {  587, IconName::Chair },
            {  588, IconName::ChairOffice },
            {  589, IconName::Chalkboard },
            {  590, IconName::ChalkboardUser },
            {  591, IconName::ChampagneGlass },
            {  592, IconName::ChampagneGlasses },
            {  593, IconName::ChargingStation },
            {  594, IconName::ChartArea },
            {  595, IconName::ChartBar },
            {  596, IconName::ChartBullet },
            {  597, IconName::ChartCandlestick },
            {  598, IconName::ChartColumn },
            {  599, IconName::ChartGantt },
            {  600, IconName::ChartKanban },
            {  601, IconName::ChartLine },
            {  602, IconName::ChartLineDown },
            {  603, IconName::ChartLineUp },
            {  604, IconName::ChartLineUpDown },
            {  605, IconName::ChartMixed },
            {  606, IconName::ChartMixedUpCircleCurrency },
            {  607, IconName::ChartMixedUpCircleDollar },
            {  608, IconName::ChartNetwork },
            {  609, IconName::ChartPie },
            {  610, IconName::ChartPieSimple },
            {  611, IconName::ChartPieSimpleCircleCurrency },
            {  612, IconName::ChartPieSimpleCircleDollar },
            {  613, IconName::ChartPyramid },
            {  614, IconName::ChartRadar },
            {  615, IconName::ChartScatter },
            {  616, IconName::ChartScatter3d },
            {  617, IconName::ChartScatterBubble },
            {  618, IconName::ChartSimple },
            {  619, IconName::ChartSimpleHorizontal },
            {  620, IconName::ChartTreeMap },
            {  621, IconName::ChartUser },
            {  622, IconName::ChartWaterfall },
            {  623, IconName::Check },
            {  624, IconName::CheckDouble },
            {  625, IconName::CheckToSlot },
            {  626, IconName::Cheese },
            {  627, IconName::CheeseSwiss },
            {  628, IconName::Cherries },
            {  629, IconName::Chess },
            {  630, IconName::ChessBishop },
            {  631, IconName::ChessBishopPiece },
            {  632, IconName::ChessBoard },
            {  633, IconName::ChessClock },
            {  634, IconName::ChessClockFlip },
            {  635, IconName::ChessKing },
            {  636, IconName::ChessKingPiece },
            {  637, IconName::ChessKnight },
            {  638, IconName::ChessKnightPiece },
            {  639, IconName::ChessPawn },
            {  640, IconName::ChessPawnPiece },
            {  641, IconName::ChessQueen },
            {  642, IconName::ChessQueenPiece },
            {  643, IconName::ChessRook },
            {  644, IconName::ChessRookPiece },
            {  645, IconName::Chestnut },
            {  646, IconName::ChevronDown },
            {  647, IconName::ChevronLeft },
            {  648, IconName::ChevronRight },
            {  649, IconName::ChevronsDown },
            {  650, IconName::ChevronsLeft },
            {  651, IconName::ChevronsRight },
            {  652, IconName::ChevronsUp },
            {  653, IconName::ChevronUp },
            {  654, IconName::ChfSign },
            {  655, IconName::Child },
            {  656, IconName::ChildCombatant },
            {  657, IconName::ChildDress },
            {  658, IconName::ChildReaching },
            {  659, IconName::Children },
            {  660, IconName::Chimney },
            {  661, IconName::Chopsticks },
            {  662, IconName::Church },
            {  663, IconName::Circle },
            {  664, IconName::Circle0 },
            {  665, IconName::Circle1 },
            {  666, IconName::Circle2 },
            {  667, IconName::Circle3 },
            {  668, IconName::Circle4 },
            {  669, IconName::Circle5 },
            {  670, IconName::Circle6 },
            {  671, IconName::Circle7 },
            {  672, IconName::Circle8 },
            {  673, IconName::Circle9 },
            {  674, IconName::CircleA },
            {  675, IconName::CircleAmpersand },
            {  676, IconName::CircleArrowDown },
            {  677, IconName::CircleArrowDownLeft },
            {  678, IconName::CircleArrowDownRight },
            {  679, IconName::CircleArrowLeft },
            {  680, IconName::CircleArrowRight },
            {  681, IconName::CircleArrowUp },
            {  682, IconName::CircleArrowUpLeft },
            {  683, IconName::CircleArrowUpRight },
            {  684, IconName::CircleB },
            {  685, IconName::CircleBolt },
            {  686, IconName::CircleBookmark },
            {  687, IconName::CircleBookOpen },
            {  688, IconName::CircleC },
            {  689, IconName::CircleCalendar },
            {  690, IconName::CircleCamera },
            {  691, IconName::CircleCaretDown },
            {  692, IconName::CircleCaretLeft },
            {  693, IconName::CircleCaretRight },
            {  694, IconName::CircleCaretUp },
            {  695, IconName::CircleCheck },
            {  696, IconName::CircleChevronDown },
            {  697, IconName::CircleChevronLeft },
            {  698, IconName::CircleChevronRight },
            {  699, IconName::CircleChevronUp },
            {  700, IconName::CircleD },
            {  701, IconName::CircleDashed },
            {  702, IconName::CircleDivide },
            {  703, IconName::CircleDollar },
            {  704, IconName::CircleDollarToSlot },
            {  705, IconName::CircleDot },
            {  706, IconName::CircleDown },
            {  707, IconName::CircleDownLeft },
            {  708, IconName::CircleDownRight },
            {  709, IconName::CircleE },
            {  710, IconName::CircleEllipsis },
            {  711, IconName::CircleEllipsisVertical },
            {  712, IconName::CircleEnvelope },
            {  713, IconName::CircleEuro },
            {  714, IconName::CircleExclamation },
            {  715, IconName::CircleExclamationCheck },
            {  716, IconName::CircleF },
            {  717, IconName::CircleG },
            {  718, IconName::CircleH },
            {  719, IconName::CircleHalf },
            {  720, IconName::CircleHalfStroke },
            {  721, IconName::CircleHeart },
            {  722, IconName::CircleI },
            {  723, IconName::CircleInfo },
            {  724, IconName::CircleJ },
            {  725, IconName::CircleK },
            {  726, IconName::CircleL },
            {  727, IconName::CircleLeft },
            {  728, IconName::CircleLocationArrow },
            {  729, IconName::CircleM },
            {  730, IconName::CircleMicrophone },
            {  731, IconName::CircleMicrophoneLines },
            {  732, IconName::CircleMinus },
            {  733, IconName::CircleN },
            {  734, IconName::CircleNodes },
            {  735, IconName::CircleNotch },
            {  736, IconName::CircleO },
            {  737, IconName::CircleP },
            {  738, IconName::CircleParking },
            {  739, IconName::CirclePause },
            {  740, IconName::CirclePhone },
            {  741, IconName::CirclePhoneFlip },
            {  742, IconName::CirclePhoneHangup },
            {  743, IconName::CirclePlay },
            {  744, IconName::CirclePlus },
            {  745, IconName::CircleQ },
            {  746, IconName::CircleQuarter },
            {  747, IconName::CircleQuarters },
            {  748, IconName::CircleQuarterStroke },
            {  749, IconName::CircleQuestion },
            {  750, IconName::CircleR },
            {  751, IconName::CircleRadiation },
            {  752, IconName::CircleRight },
            {  753, IconName::CircleS },
            {  754, IconName::CircleSmall },
            {  755, IconName::CircleSort },
            {  756, IconName::CircleSortDown },
            {  757, IconName::CircleSortUp },
            {  758, IconName::CirclesOverlap },
            {  759, IconName::CircleStar },
            {  760, IconName::CircleSterling },
            {  761, IconName::CircleStop },
            {  762, IconName::CircleT },
            {  763, IconName::CircleThreeQuarters },
            {  764, IconName::CircleThreeQuartersStroke },
            {  765, IconName::CircleTrash },
            {  766, IconName::CircleU },
            {  767, IconName::CircleUp },
            {  768, IconName::CircleUpLeft },
            {  769, IconName::CircleUpRight },
            {  770, IconName::CircleUser },
            {  771, IconName::CircleV },
            {  772, IconName::CircleVideo },
            {  773, IconName::CircleW },
            {  774, IconName::CircleWaveformLines },
            {  775, IconName::CircleX },
            {  776, IconName::CircleXmark },
            {  777, IconName::CircleY },
            {  778, IconName::CircleYen },
            {  779, IconName::CircleZ },
            {  780, IconName::Citrus },
            {  781, IconName::CitrusSlice },
            {  782, IconName::City },
            {  783, IconName::Clapperboard },
            {  784, IconName::ClapperboardPlay },
            {  785, IconName::Clarinet },
            {  786, IconName::ClawMarks },
            {  787, IconName::Clipboard },
            {  788, IconName::ClipboardCheck },
            {  789, IconName::ClipboardList },
            {  790, IconName::ClipboardListCheck },
            {  791, IconName::ClipboardMedical },
            {  792, IconName::ClipboardPrescription },
            {  793, IconName::ClipboardQuestion },
            {  794, IconName::ClipboardUser },
            {  795, IconName::Clock },
            {  796, IconName::ClockDesk },
            {  797, IconName::ClockEight },
            {  798, IconName::ClockEightThirty },
            {  799, IconName::ClockEleven },
            {  800, IconName::ClockElevenThirty },
            {  801, IconName::ClockFive },
            {  802, IconName::ClockFiveThirty },
            {  803, IconName::ClockFourThirty },
            {  804, IconName::ClockNine },
            {  805, IconName::ClockNineThirty },
            {  806, IconName::ClockOne },
            {  807, IconName::ClockOneThirty },
            {  808, IconName::ClockRotateLeft },
            {  809, IconName::ClockSeven },
            {  810, IconName::ClockSevenThirty },
            {  811, IconName::ClockSix },
            {  812, IconName::ClockSixThirty },
            {  813, IconName::ClockTen },
            {  814, IconName::ClockTenThirty },
            {  815, IconName::ClockThree },
            {  816, IconName::ClockThreeThirty },
            {  817, IconName::ClockTwelve },
            {  818, IconName::ClockTwelveThirty },
            {  819, IconName::ClockTwo },
            {  820, IconName::ClockTwoThirty },
            {  821, IconName::Clone },
            {  822, IconName::ClosedCaptioning },
            {  823, IconName::ClosedCaptioningSlash },
            {  824, IconName::ClothesHanger },
            {  825, IconName::Cloud },
            {  826, IconName::CloudArrowDown },
            {  827, IconName::CloudArrowUp },
            {  828, IconName::CloudBinary },
            {  829, IconName::CloudBolt },
            {  830, IconName::CloudBoltMoon },
            {  831, IconName::CloudBoltSun },
            {  832, IconName::CloudCheck },
            {  833, IconName::CloudDrizzle },
            {  834, IconName::CloudExclamation },
            {  835, IconName::CloudFog },
            {  836, IconName::CloudHail },
            {  837, IconName::CloudHailMixed },
            {  838, IconName::CloudMeatball },
            {  839, IconName::CloudMinus },
            {  840, IconName::CloudMoon },
            {  841, IconName::CloudMoonRain },
            {  842, IconName::CloudMusic },
            {  843, IconName::CloudPlus },
            {  844, IconName::CloudQuestion },
            {  845, IconName::CloudRain },
            {  846, IconName::CloudRainbow },
            {  847, IconName::Clouds },
            {  848, IconName::CloudShowers },
            {  849, IconName::CloudShowersHeavy },
            {  850, IconName::CloudShowersWater },
            {  851, IconName::CloudSlash },
            {  852, IconName::CloudSleet },
            {  853, IconName::CloudsMoon },
            {  854, IconName::CloudSnow },
            {  855, IconName::CloudsSun },
            {  856, IconName::CloudSun },
            {  857, IconName::CloudSunRain },
            {  858, IconName::CloudWord },
            {  859, IconName::CloudXmark },
            {  860, IconName::FaceSaluting },
            {  861, IconName::Club },
            {  862, IconName::Coconut },
            {  863, IconName::Code },
            {  864, IconName::CodeBranch },
            {  865, IconName::CodeCommit },
            {  866, IconName::CodeCompare },
            {  867, IconName::CodeFork },
            {  868, IconName::CodePullRequest },
            {  869, IconName::CodePullRequestClosed },
            {  870, IconName::CodeMerge },
            {  871, IconName::CodePullRequestDraft },
            {  872, IconName::CodeSimple },
            {  873, IconName::CoffeeBean },
            {  874, IconName::CoffeeBeans },
            {  875, IconName::CoffeePot },
            {  876, IconName::CoffinCross },
            {  877, IconName::Coffin },
            {  878, IconName::Coin },
            {  879, IconName::CoinBlank },
            {  880, IconName::CoinVertical },
            {  881, IconName::CoinFront },
            {  882, IconName::Colon },
            {  883, IconName::Coins },
            {  884, IconName::ColonSign },
            {  885, IconName::Columns3 },
            {  886, IconName::Comet },
            {  887, IconName::Comma },
            {  888, IconName::Comment },
            {  889, IconName::CommentArrowDown },
            {  890, IconName::CommentArrowUp },
            {  891, IconName::CommentArrowUpRight },
            {  892, IconName::CommentCaptions },
            {  893, IconName::CommentCheck },
            {  894, IconName::CommentDollar },
            {  895, IconName::CommentDots },
            {  896, IconName::CommentCode },
            {  897, IconName::Command },
            {  898, IconName::CommentImage },
            {  899, IconName::CommentHeart },
            {  900, IconName::CommentExclamation },
            {  901, IconName::CommentLines },
            {  902, IconName::CommentMedical },
            {  903, IconName::CommentMiddle },
            {  904, IconName::CommentMiddleTop },
            {  905, IconName::CommentMinus },
            {  906, IconName::CommentMusic },
            {  907, IconName::CommentPen },
            {  908, IconName::CommentPlus },
            {  909, IconName::CommentQuestion },
            {  910, IconName::CommentQuote },
            {  911, IconName::Comments },
            {  912, IconName::CommentsDollar },
            {  913, IconName::CommentSlash },
            {  914, IconName::CommentSmile },
            {  915, IconName::CommentSms },
            {  916, IconName::CommentsQuestion },
            {  917, IconName::CommentsQuestionCheck },
            {  918, IconName::CommentText },
            {  919, IconName::CommentXmark },
            {  920, IconName::CompactDisc },
            {  921, IconName::Compass },
            {  922, IconName::CompassDrafting },
            {  923, IconName::CompassSlash },
            {  924, IconName::Compress },
            {  925, IconName::CompressWide },
            {  926, IconName::ComputerClassic },
            {  927, IconName::ComputerMouse },
            {  928, IconName::ComputerMouseScrollwheel },
            {  929, IconName::ComputerSpeaker },
            {  930, IconName::ContainerStorage },
            {  931, IconName::ConveyorBelt },
            {  932, IconName::ConveyorBeltBoxes },
            {  933, IconName::ConveyorBeltArm },
            {  934, IconName::CookieBite },
            {  935, IconName::Computer },
            {  936, IconName::Copyright },
            {  937, IconName::Cookie },
            {  938, IconName::Corn },
            {  939, IconName::Corner },
            {  940, IconName::Copy },
            {  941, IconName::CourtSport },
            {  942, IconName::Cow },
            {  943, IconName::Couch },
            {  944, IconName::CowbellCirclePlus },
            {  945, IconName::Cowbell },
            {  946, IconName::CrateEmpty },
            {  947, IconName::CreditCardFront },
            {  948, IconName::Crab },
            {  949, IconName::CreditCardBlank },
            {  950, IconName::CreditCard },
            {  951, IconName::CrateApple },
            {  952, IconName::ConveyorBeltEmpty },
            {  953, IconName::Crop },
            {  954, IconName::Crosshairs },
            {  955, IconName::CropSimple },
            {  956, IconName::Cross },
            {  957, IconName::Croissant },
            {  958, IconName::CricketBatBall },
            {  959, IconName::CrosshairsSimple },
            {  960, IconName::Crow },
            {  961, IconName::Crown },
            {  962, IconName::Crutch },
            {  963, IconName::Crutches },
            {  964, IconName::CruzeiroSign },
            {  965, IconName::CrystalBall },
            {  966, IconName::Cube },
            {  967, IconName::Cubes },
            {  968, IconName::CubesStacked },
            {  969, IconName::Cucumber },
            {  970, IconName::Cupcake },
            {  971, IconName::CupStraw },
            {  972, IconName::CupStrawSwoosh },
            {  973, IconName::CupTogo },
            {  974, IconName::CurlingStone },
            {  975, IconName::Custard },
            {  976, IconName::D },
            {  977, IconName::Dagger },
            {  978, IconName::Dash },
            {  979, IconName::Database },
            {  980, IconName::Deer },
            {  981, IconName::DeerRudolph },
            {  982, IconName::DeleteLeft },
            {  983, IconName::DeleteRight },
            {  984, IconName::Desktop },
            {  985, IconName::Democrat },
            {  986, IconName::DesktopArrowDown },
            {  987, IconName::Dharmachakra },
            {  988, IconName::DiagramCells },
            {  989, IconName::DiagramLeanCanvas },
            {  990, IconName::DiagramNested },
            {  991, IconName::DiagramNext },
            {  992, IconName::DiagramPredecessor },
            {  993, IconName::DiagramPrevious },
            {  994, IconName::DiagramProject },
            {  995, IconName::DiagramSankey },
            {  996, IconName::DiagramSubtask },
            {  997, IconName::DiagramSuccessor },
            {  998, IconName::DiagramVenn },
            {  999, IconName::Dial },
            { 1000, IconName::DialHigh },
            { 1001, IconName::DialLow },
            { 1002, IconName::DialMax },
            { 1003, IconName::DialMed },
            { 1004, IconName::DialMedLow },
            { 1005, IconName::DialMin },
            { 1006, IconName::DialOff },
            { 1007, IconName::Diamond },
            { 1008, IconName::DiamondExclamation },
            { 1009, IconName::DiamondHalf },
            { 1010, IconName::DiamondHalfStroke },
            { 1011, IconName::DiamondTurnRight },
            { 1012, IconName::Dice },
            { 1013, IconName::DiceD4 },
            { 1014, IconName::DiceD6 },
            { 1015, IconName::DiceD8 },
            { 1016, IconName::DiceD10 },
            { 1017, IconName::DiceD12 },
            { 1018, IconName::DiceD20 },
            { 1019, IconName::DiceFive },
            { 1020, IconName::DiceFour },
            { 1021, IconName::DiceOne },
            { 1022, IconName::DiceSix },
            { 1023, IconName::DiceThree },
            { 1024, IconName::DiceTwo },
            { 1025, IconName::Dinosaur },
            { 1026, IconName::Diploma },
            { 1027, IconName::DiscDrive },
            { 1028, IconName::Disease },
            { 1029, IconName::Display },
            { 1030, IconName::DisplayArrowDown },
            { 1031, IconName::DisplayChartUp },
            { 1032, IconName::DisplayChartUpCircleCurrency },
            { 1033, IconName::DisplayChartUpCircleDollar },
            { 1034, IconName::DisplayCode },
            { 1035, IconName::DisplayMedical },
            { 1036, IconName::DisplaySlash },
            { 1037, IconName::DistributeSpacingHorizontal },
            { 1038, IconName::DistributeSpacingVertical },
            { 1039, IconName::Ditto },
            { 1040, IconName::Divide },
            { 1041, IconName::Dna },
            { 1042, IconName::Dog },
            { 1043, IconName::DogLeashed },
            { 1044, IconName::DollarSign },
            { 1045, IconName::Dolly },
            { 1046, IconName::DollyEmpty },
            { 1047, IconName::Dolphin },
            { 1048, IconName::DongSign },
            { 1049, IconName::DoNotEnter },
            { 1050, IconName::Donut },
            { 1051, IconName::DoorClosed },
            { 1052, IconName::DoorOpen },
            { 1053, IconName::Dove },
            { 1054, IconName::Down },
            { 1055, IconName::DownFromDottedLine },
            { 1056, IconName::DownFromLine },
            { 1057, IconName::DownLeft },
            { 1058, IconName::DownLeftAndUpRightToCenter },
            { 1059, IconName::Download },
            { 1060, IconName::DownLong },
            { 1061, IconName::DownRight },
            { 1062, IconName::DownToBracket },
            { 1063, IconName::DownToDottedLine },
            { 1064, IconName::DownToLine },
            { 1065, IconName::Dragon },
            { 1066, IconName::DrawCircle },
            { 1067, IconName::DrawPolygon },
            { 1068, IconName::DrawSquare },
            { 1069, IconName::Dreidel },
            { 1070, IconName::Drone },
            { 1071, IconName::DroneFront },
            { 1072, IconName::Droplet },
            { 1073, IconName::DropletDegree },
            { 1074, IconName::DropletPercent },
            { 1075, IconName::DropletSlash },
            { 1076, IconName::Drum },
            { 1077, IconName::DrumSteelpan },
            { 1078, IconName::Drumstick },
            { 1079, IconName::DrumstickBite },
            { 1080, IconName::Dryer },
            { 1081, IconName::DryerHeat },
            { 1082, IconName::Duck },
            { 1083, IconName::Dumbbell },
            { 1084, IconName::Dumpster },
            { 1085, IconName::DumpsterFire },
            { 1086, IconName::Dungeon },
            { 1087, IconName::E },
            { 1088, IconName::Ear },
            { 1089, IconName::EarDeaf },
            { 1090, IconName::EarListen },
            { 1091, IconName::EarMuffs },
            { 1092, IconName::EarthAfrica },
            { 1093, IconName::EarthAmericas },
            { 1094, IconName::EarthAsia },
            { 1095, IconName::EarthEurope },
            { 1096, IconName::EarthOceania },
            { 1097, IconName::Eclipse },
            { 1098, IconName::Egg },
            { 1099, IconName::EggFried },
            { 1100, IconName::Eggplant },
            { 1101, IconName::Eject },
            { 1102, IconName::Elephant },
            { 1103, IconName::Elevator },
            { 1104, IconName::Ellipsis },
            { 1105, IconName::EllipsisStroke },
            { 1106, IconName::EllipsisStrokeVertical },
            { 1107, IconName::EllipsisVertical },
            { 1108, IconName::EmptySet },
            { 1109, IconName::Engine },
            { 1110, IconName::EngineWarning },
            { 1111, IconName::Envelope },
            { 1112, IconName::EnvelopeCircleCheck },
            { 1113, IconName::EnvelopeDot },
            { 1114, IconName::EnvelopeOpen },
            { 1115, IconName::EnvelopeOpenDollar },
            { 1116, IconName::EnvelopeOpenText },
            { 1117, IconName::Envelopes },
            { 1118, IconName::EnvelopesBulk },
            { 1119, IconName::Equals },
            { 1120, IconName::Eraser },
            { 1121, IconName::Escalator },
            { 1122, IconName::Ethernet },
            { 1123, IconName::EuroSign },
            { 1124, IconName::Excavator },
            { 1125, IconName::Exclamation },
            { 1126, IconName::Expand },
            { 1127, IconName::ExpandWide },
            { 1128, IconName::Explosion },
            { 1129, IconName::Eye },
            { 1130, IconName::EyeDropper },
            { 1131, IconName::EyeDropperFull },
            { 1132, IconName::EyeDropperHalf },
            { 1133, IconName::EyeEvil },
            { 1134, IconName::EyeLowVision },
            { 1135, IconName::Eyes },
            { 1136, IconName::EyeSlash },
            { 1137, IconName::F },
            { 1138, IconName::FaceAngry },
            { 1139, IconName::FaceAngryHorns },
            { 1140, IconName::FaceAnguished },
            { 1141, IconName::FaceAnxiousSweat },
            { 1142, IconName::FaceAstonished },
            { 1143, IconName::FaceAwesome },
            { 1144, IconName::FaceBeamHandOverMouth },
            { 1145, IconName::FaceClouds },
            { 1146, IconName::FaceConfounded },
            { 1147, IconName::FaceConfused },
            { 1148, IconName::FaceCowboyHat },
            { 1149, IconName::FaceDiagonalMouth },
            { 1150, IconName::FaceDisappointed },
            { 1151, IconName::FaceDisguise },
            { 1152, IconName::FaceDizzy },
            { 1153, IconName::FaceDotted },
            { 1154, IconName::FaceDowncastSweat },
            { 1155, IconName::FaceDrooling },
            { 1156, IconName::FaceExhaling },
            { 1157, IconName::FaceExplode },
            { 1158, IconName::FaceExpressionless },
            { 1159, IconName::FaceEyesXmarks },
            { 1160, IconName::FaceFearful },
            { 1161, IconName::FaceFlushed },
            { 1162, IconName::FaceFrown },
            { 1163, IconName::FaceFrownOpen },
            { 1164, IconName::FaceFrownSlight },
            { 1165, IconName::FaceGlasses },
            { 1166, IconName::FaceGrimace },
            { 1167, IconName::FaceGrin },
            { 1168, IconName::FaceGrinBeam },
            { 1169, IconName::FaceGrinBeamSweat },
            { 1170, IconName::FaceGrinHearts },
            { 1171, IconName::FaceGrinSquint },
            { 1172, IconName::FaceGrinSquintTears },
            { 1173, IconName::FaceGrinStars },
            { 1174, IconName::FaceGrinTears },
            { 1175, IconName::FaceGrinTongue },
            { 1176, IconName::FaceGrinTongueSquint },
            { 1177, IconName::FaceGrinTongueWink },
            { 1178, IconName::FaceGrinWide },
            { 1179, IconName::FaceGrinWink },
            { 1180, IconName::FaceHandOverMouth },
            { 1181, IconName::FaceHandPeeking },
            { 1182, IconName::FaceHandYawn },
            { 1183, IconName::FaceHeadBandage },
            { 1184, IconName::FaceHoldingBackTears },
            { 1185, IconName::FaceHushed },
            { 1186, IconName::FaceIcicles },
            { 1187, IconName::FaceKiss },
            { 1188, IconName::FaceKissBeam },
            { 1189, IconName::FaceKissClosedEyes },
            { 1190, IconName::FaceKissWinkHeart },
            { 1191, IconName::FaceLaugh },
            { 1192, IconName::FaceLaughBeam },
            { 1193, IconName::FaceLaughSquint },
            { 1194, IconName::FaceLaughWink },
            { 1195, IconName::FaceLying },
            { 1196, IconName::FaceMask },
            { 1197, IconName::FaceMeh },
            { 1198, IconName::FaceMehBlank },
            { 1199, IconName::FaceMelting },
            { 1200, IconName::FaceMonocle },
            { 1201, IconName::FaceNauseated },
            { 1202, IconName::FaceNoseSteam },
            { 1203, IconName::FaceParty },
            { 1204, IconName::FacePensive },
            { 1205, IconName::FacePersevering },
            { 1206, IconName::FacePleading },
            { 1207, IconName::FacePouting },
            { 1208, IconName::FaceRaisedEyebrow },
            { 1209, IconName::FaceRelieved },
            { 1210, IconName::FaceRollingEyes },
            { 1211, IconName::FaceSadCry },
            { 1212, IconName::FaceSadSweat },
            { 1213, IconName::FaceSadTear },
            { 1214, IconName::Hotdog },
            { 1215, IconName::FaceScream },
            { 1216, IconName::FaceShush },
            { 1217, IconName::FaceSleeping },
            { 1218, IconName::FaceSleepy },
            { 1219, IconName::FaceSmileBeam },
            { 1220, IconName::FaceSmile },
            { 1221, IconName::FaceSmileHalo },
            { 1222, IconName::FaceSmileHearts },
            { 1223, IconName::FaceSmileHorns },
            { 1224, IconName::FaceSmilePlus },
            { 1225, IconName::FaceSmileRelaxed },
            { 1226, IconName::FaceSmileTear },
            { 1227, IconName::FaceSmileTongue },
            { 1228, IconName::FaceSmileUpsideDown },
            { 1229, IconName::FaceSmileWink },
            { 1230, IconName::FaceSmilingHands },
            { 1231, IconName::FaceSpiralEyes },
            { 1232, IconName::FaceSmirking },
            { 1233, IconName::FaceSunglasses },
            { 1234, IconName::FaceSurprise },
            { 1235, IconName::FaceSwear },
            { 1236, IconName::FaceThermometer },
            { 1237, IconName::FaceThinking },
            { 1238, IconName::FaceTired },
            { 1239, IconName::FaceTissue },
            { 1240, IconName::FaceTongueMoney },
            { 1241, IconName::FaceTongueSweat },
            { 1242, IconName::FaceUnamused },
            { 1243, IconName::FaceViewfinder },
            { 1244, IconName::FaceVomit },
            { 1245, IconName::FaceWeary },
            { 1246, IconName::FaceWoozy },
            { 1247, IconName::FaceWorried },
            { 1248, IconName::FaceZany },
            { 1249, IconName::FaceZipper },
            { 1250, IconName::Falafel },
            { 1251, IconName::Family },
            { 1252, IconName::FamilyDress },
            { 1253, IconName::FamilyPants },
            { 1254, IconName::Fan },
            { 1255, IconName::FanTable },
            { 1256, IconName::Farm },
            { 1257, IconName::Faucet },
            { 1258, IconName::FaucetDrip },
            { 1259, IconName::Fax },
            { 1260, IconName::Feather },
            { 1261, IconName::FeatherPointed },
            { 1262, IconName::Fence },
            { 1263, IconName::FerrisWheel },
            { 1264, IconName::Ferry },
            { 1265, IconName::FieldHockeyStickBall },
            { 1266, IconName::File },
            { 1267, IconName::FileArrowDown },
            { 1268, IconName::FileArrowUp },
            { 1269, IconName::FileAudio },
            { 1270, IconName::FileBinary },
            { 1271, IconName::FileCertificate },
            { 1272, IconName::FileChartColumn },
            { 1273, IconName::FileChartPie },
            { 1274, IconName::FileCheck },
            { 1275, IconName::FileCircleCheck },
            { 1276, IconName::FileCircleExclamation },
            { 1277, IconName::FileCircleInfo },
            { 1278, IconName::FileCircleMinus },
            { 1279, IconName::FileCirclePlus },
            { 1280, IconName::FileCircleQuestion },
            { 1281, IconName::FileCircleXmark },
            { 1282, IconName::FileCode },
            { 1283, IconName::FileContract },
            { 1284, IconName::FileCsv },
            { 1285, IconName::FileDashedLine },
            { 1286, IconName::FileDoc },
            { 1287, IconName::FileEps },
            { 1288, IconName::FileExclamation },
            { 1289, IconName::FileGif },
            { 1290, IconName::FileExport },
            { 1291, IconName::FileImport },
            { 1292, IconName::FileExcel },
            { 1293, IconName::FileHeart },
            { 1294, IconName::FileInvoiceDollar },
            { 1295, IconName::FileImage },
            { 1296, IconName::FileInvoice },
            { 1297, IconName::FileJpg },
            { 1298, IconName::FileLines },
            { 1299, IconName::FileMagnifyingGlass },
            { 1300, IconName::FileLock },
            { 1301, IconName::FileMedical },
            { 1302, IconName::FileMinus },
            { 1303, IconName::FileMov },
            { 1304, IconName::FileMp4 },
            { 1305, IconName::FileMp3 },
            { 1306, IconName::FilePdf },
            { 1307, IconName::FileMusic },
            { 1308, IconName::FilePen },
            { 1309, IconName::FilePlusMinus },
            { 1310, IconName::FilePrescription },
            { 1311, IconName::FilePlus },
            { 1312, IconName::FilePowerpoint },
            { 1313, IconName::FilePpt },
            { 1314, IconName::Files },
            { 1315, IconName::FileShield },
            { 1316, IconName::FileSignature },
            { 1317, IconName::FileSlash },
            { 1318, IconName::FilesMedical },
            { 1319, IconName::FileSpreadsheet },
            { 1320, IconName::FileSvg },
            { 1321, IconName::FileUser },
            { 1322, IconName::FileVector },
            { 1323, IconName::FileVideo },
            { 1324, IconName::FileWaveform },
            { 1325, IconName::FileWord },
            { 1326, IconName::FileXls },
            { 1327, IconName::FileXmark },
            { 1328, IconName::FileXml },
            { 1329, IconName::FileZip },
            { 1330, IconName::FileZipper },
            { 1331, IconName::Fill },
            { 1332, IconName::FillDrip },
            { 1333, IconName::Film },
            { 1334, IconName::FilmCanister },
            { 1335, IconName::Films },
            { 1336, IconName::FilmSimple },
            { 1337, IconName::FilmSlash },
            { 1338, IconName::Filter },
            { 1339, IconName::FilterCircleDollar },
            { 1340, IconName::FilterCircleXmark },
            { 1341, IconName::FilterList },
            { 1342, IconName::Filters },
            { 1343, IconName::FilterSlash },
            { 1344, IconName::Fingerprint },
            { 1345, IconName::Fire },
            { 1346, IconName::FireBurner },
            { 1347, IconName::FireExtinguisher },
            { 1348, IconName::FireFlame },
            { 1349, IconName::FireFlameCurved },
            { 1350, IconName::FireFlameSimple },
            { 1351, IconName::FireHydrant },
            { 1352, IconName::Fireplace },
            { 1353, IconName::FireSmoke },
            { 1354, IconName::Fish },
            { 1355, IconName::FishBones },
            { 1356, IconName::FishCooked },
            { 1357, IconName::FishFins },
            { 1358, IconName::FishingRod },
            { 1359, IconName::Flag },
            { 1360, IconName::FlagCheckered },
            { 1361, IconName::FlagPennant },
            { 1362, IconName::FlagSwallowtail },
            { 1363, IconName::FlagUsa },
            { 1364, IconName::Flashlight },
            { 1365, IconName::Flask },
            { 1366, IconName::FlaskGear },
            { 1367, IconName::FlaskRoundPoison },
            { 1368, IconName::FlaskRoundPotion },
            { 1369, IconName::FlaskVial },
            { 1370, IconName::Flatbread },
            { 1371, IconName::FlatbreadStuffed },
            { 1372, IconName::FloppyDisk },
            { 1373, IconName::FloppyDiskCircleArrowRight },
            { 1374, IconName::FloppyDiskCircleXmark },
            { 1375, IconName::FloppyDiskPen },
            { 1376, IconName::FloppyDisks },
            { 1377, IconName::FlorinSign },
            { 1378, IconName::Flower },
            { 1379, IconName::FlowerDaffodil },
            { 1380, IconName::FlowerTulip },
            { 1381, IconName::Flute },
            { 1382, IconName::FluxCapacitor },
            { 1383, IconName::FlyingDisc },
            { 1384, IconName::Folder },
            { 1385, IconName::FolderArrowDown },
            { 1386, IconName::FolderArrowUp },
            { 1387, IconName::FolderBookmark },
            { 1388, IconName::FolderCheck },
            { 1389, IconName::FolderClosed },
            { 1390, IconName::FolderGear },
            { 1391, IconName::FolderGrid },
            { 1392, IconName::FolderHeart },
            { 1393, IconName::FolderImage },
            { 1394, IconName::FolderMagnifyingGlass },
            { 1395, IconName::FolderMedical },
            { 1396, IconName::FolderMinus },
            { 1397, IconName::FolderMusic },
            { 1398, IconName::FolderOpen },
            { 1399, IconName::FolderPlus },
            { 1400, IconName::Folders },
            { 1401, IconName::FolderTree },
            { 1402, IconName::FolderUser },
            { 1403, IconName::FolderXmark },
            { 1404, IconName::FonduePot },
            { 1405, IconName::Font },
            { 1406, IconName::FontAwesome },
            { 1407, IconName::FontCase },
            { 1408, IconName::Football },
            { 1409, IconName::FootballHelmet },
            { 1410, IconName::Fork },
            { 1411, IconName::ForkKnife },
            { 1412, IconName::Forklift },
            { 1413, IconName::Fort },
            { 1414, IconName::Forward },
            { 1415, IconName::ForwardFast },
            { 1416, IconName::ForwardStep },
            { 1417, IconName::Frame },
            { 1418, IconName::FrancSign },
            { 1419, IconName::FrenchFries },
            { 1420, IconName::Frog },
            { 1421, IconName::Function },
            { 1422, IconName::Futbol },
            { 1423, IconName::G },
            { 1424, IconName::Galaxy },
            { 1425, IconName::GalleryThumbnails },
            { 1426, IconName::GameBoard },
            { 1427, IconName::GameBoardSimple },
            { 1428, IconName::GameConsoleHandheld },
            { 1429, IconName::GameConsoleHandheldCrank },
            { 1430, IconName::Gamepad },
            { 1431, IconName::GamepadModern },
            { 1432, IconName::Garage },
            { 1433, IconName::GarageCar },
            { 1434, IconName::GarageOpen },
            { 1435, IconName::Garlic },
            { 1436, IconName::GasPump },
            { 1437, IconName::GasPumpSlash },
            { 1438, IconName::Gauge },
            { 1439, IconName::GaugeCircleBolt },
            { 1440, IconName::GaugeCircleMinus },
            { 1441, IconName::GaugeCirclePlus },
            { 1442, IconName::GaugeHigh },
            { 1443, IconName::GaugeLow },
            { 1444, IconName::GaugeMax },
            { 1445, IconName::GaugeMin },
            { 1446, IconName::GaugeSimple },
            { 1447, IconName::GaugeSimpleHigh },
            { 1448, IconName::GaugeSimpleLow },
            { 1449, IconName::GaugeSimpleMax },
            { 1450, IconName::GaugeSimpleMin },
            { 1451, IconName::Gavel },
            { 1452, IconName::Gear },
            { 1453, IconName::GearCode },
            { 1454, IconName::GearComplex },
            { 1455, IconName::GearComplexCode },
            { 1456, IconName::Gears },
            { 1457, IconName::Gem },
            { 1458, IconName::Genderless },
            { 1459, IconName::Ghost },
            { 1460, IconName::Gif },
            { 1461, IconName::Gift },
            { 1462, IconName::GiftCard },
            { 1463, IconName::Gifts },
            { 1464, IconName::GingerbreadMan },
            { 1465, IconName::Glass },
            { 1466, IconName::GlassCitrus },
            { 1467, IconName::GlassEmpty },
            { 1468, IconName::Glasses },
            { 1469, IconName::GlassesRound },
            { 1470, IconName::GlassHalf },
            { 1471, IconName::GlassWater },
            { 1472, IconName::GlassWaterDroplet },
            { 1473, IconName::Globe },
            { 1474, IconName::GlobePointer },
            { 1475, IconName::GlobeSnow },
            { 1476, IconName::GlobeStand },
            { 1477, IconName::GoalNet },
            { 1478, IconName::GolfBallTee },
            { 1479, IconName::GolfClub },
            { 1480, IconName::GolfFlagHole },
            { 1481, IconName::Gopuram },
            { 1482, IconName::GraduationCap },
            { 1483, IconName::Gramophone },
            { 1484, IconName::Grapes },
            { 1485, IconName::Grate },
            { 1486, IconName::GrateDroplet },
            { 1487, IconName::GreaterThan },
            { 1488, IconName::GreaterThanEqual },
            { 1489, IconName::Grid },
            { 1490, IconName::Grid2 },
            { 1491, IconName::Grid2Plus },
            { 1492, IconName::Grid4 },
            { 1493, IconName::Grid5 },
            { 1494, IconName::GridDividers },
            { 1495, IconName::GridHorizontal },
            { 1496, IconName::GridRound },
            { 1497, IconName::GridRound2 },
            { 1498, IconName::GridRound2Plus },
            { 1499, IconName::GridRound4 },
            { 1500, IconName::GridRound5 },
            { 1501, IconName::Grill },
            { 1502, IconName::GrillFire },
            { 1503, IconName::GrillHot },
            { 1504, IconName::Grip },
            { 1505, IconName::GripDots },
            { 1506, IconName::GripDotsVertical },
            { 1507, IconName::GripLines },
            { 1508, IconName::GripVertical },
            { 1509, IconName::GripLinesVertical },
            { 1510, IconName::GroupArrowsRotate },
            { 1511, IconName::Gun },
            { 1512, IconName::GuitarElectric },
            { 1513, IconName::Guitars },
            { 1514, IconName::Guitar },
            { 1515, IconName::GuaraniSign },
            { 1516, IconName::GunSlash },
            { 1517, IconName::GunSquirt },
            { 1518, IconName::H },
            { 1519, IconName::H1 },
            { 1520, IconName::H2 },
            { 1521, IconName::H3 },
            { 1522, IconName::H4 },
            { 1523, IconName::H5 },
            { 1524, IconName::H6 },
            { 1525, IconName::Hammer },
            { 1526, IconName::HammerBrush },
            { 1527, IconName::HammerCrash },
            { 1528, IconName::HammerWar },
            { 1529, IconName::Hamsa },
            { 1530, IconName::Hand },
            { 1531, IconName::HandBackFist },
            { 1532, IconName::HandBackPointDown },
            { 1533, IconName::HandBackPointLeft },
            { 1534, IconName::HandBackPointRibbon },
            { 1535, IconName::HandBackPointRight },
            { 1536, IconName::HandBackPointUp },
            { 1537, IconName::Handcuffs },
            { 1538, IconName::HandDots },
            { 1539, IconName::HandFingersCrossed },
            { 1540, IconName::HandFist },
            { 1541, IconName::HandHeart },
            { 1542, IconName::HandHolding },
            { 1543, IconName::HandHoldingBox },
            { 1544, IconName::HandHoldingCircleDollar },
            { 1545, IconName::HandHoldingDollar },
            { 1546, IconName::HandHoldingDroplet },
            { 1547, IconName::HandHoldingHand },
            { 1548, IconName::HandHoldingHeart },
            { 1549, IconName::HandHoldingMagic },
            { 1550, IconName::HandHoldingMedical },
            { 1551, IconName::HandHoldingSeedling },
            { 1552, IconName::HandHoldingSkull },
            { 1553, IconName::HandHorns },
            { 1554, IconName::HandLizard },
            { 1555, IconName::HandLove },
            { 1556, IconName::HandMiddleFinger },
            { 1557, IconName::HandPeace },
            { 1558, IconName::HandPointDown },
            { 1559, IconName::HandPointer },
            { 1560, IconName::HandPointLeft },
            { 1561, IconName::HandPointRibbon },
            { 1562, IconName::HandPointRight },
            { 1563, IconName::HandPointUp },
            { 1564, IconName::Hands },
            { 1565, IconName::HandsAslInterpreting },
            { 1566, IconName::HandsBound },
            { 1567, IconName::HandsBubbles },
            { 1568, IconName::HandScissors },
            { 1569, IconName::HandsClapping },
            { 1570, IconName::Handshake },
            { 1571, IconName::HandshakeAngle },
            { 1572, IconName::HandshakeSimple },
            { 1573, IconName::HandshakeSimpleSlash },
            { 1574, IconName::HandshakeSlash },
            { 1575, IconName::HandsHolding },
            { 1576, IconName::HandsHoldingChild },
            { 1577, IconName::HandsHoldingCircle },
            { 1578, IconName::HandsHoldingDiamond },
            { 1579, IconName::HandsHoldingDollar },
            { 1580, IconName::HandsHoldingHeart },
            { 1581, IconName::HandSparkles },
            { 1582, IconName::HandSpock },
            { 1583, IconName::HandsPraying },
            { 1584, IconName::HandWave },
            { 1585, IconName::Hanukiah },
            { 1586, IconName::HardDrive },
            { 1587, IconName::Hashtag },
            { 1588, IconName::HashtagLock },
            { 1589, IconName::HatBeach },
            { 1590, IconName::HatChef },
            { 1591, IconName::HatCowboy },
            { 1592, IconName::HatCowboySide },
            { 1593, IconName::HatSanta },
            { 1594, IconName::HatWinter },
            { 1595, IconName::HatWitch },
            { 1596, IconName::HatWizard },
            { 1597, IconName::Heading },
            { 1598, IconName::Headphones },
            { 1599, IconName::HeadphonesSimple },
            { 1600, IconName::Headset },
            { 1601, IconName::HeadSide },
            { 1602, IconName::HeadSideBrain },
            { 1603, IconName::HeadSideCough },
            { 1604, IconName::HeadSideCoughSlash },
            { 1605, IconName::HeadSideGear },
            { 1606, IconName::HeadSideGoggles },
            { 1607, IconName::HeadSideHeadphones },
            { 1608, IconName::HeadSideHeart },
            { 1609, IconName::HeadSideMask },
            { 1610, IconName::HeadSideMedical },
            { 1611, IconName::HeadSideVirus },
            { 1612, IconName::Heart },
            { 1613, IconName::HeartCircleBolt },
            { 1614, IconName::HeartCircleCheck },
            { 1615, IconName::HeartCircleExclamation },
            { 1616, IconName::HeartCircleMinus },
            { 1617, IconName::HeartCirclePlus },
            { 1618, IconName::HeartCircleXmark },
            { 1619, IconName::HeartCrack },
            { 1620, IconName::HeartHalf },
            { 1621, IconName::HeartHalfStroke },
            { 1622, IconName::HeartPulse },
            { 1623, IconName::Heat },
            { 1624, IconName::Helicopter },
            { 1625, IconName::HelicopterSymbol },
            { 1626, IconName::HelmetBattle },
            { 1627, IconName::HelmetSafety },
            { 1628, IconName::HelmetUn },
            { 1629, IconName::Hexagon },
            { 1630, IconName::HexagonCheck },
            { 1631, IconName::HexagonDivide },
            { 1632, IconName::HexagonExclamation },
            { 1633, IconName::HexagonImage },
            { 1634, IconName::HexagonMinus },
            { 1635, IconName::HexagonPlus },
            { 1636, IconName::HexagonVerticalNft },
            { 1637, IconName::HexagonVerticalNftSlanted },
            { 1638, IconName::HexagonXmark },
            { 1639, IconName::HighDefinition },
            { 1640, IconName::Highlighter },
            { 1641, IconName::HighlighterLine },
            { 1642, IconName::HillAvalanche },
            { 1643, IconName::HillRockslide },
            { 1644, IconName::Hippo },
            { 1645, IconName::HockeyMask },
            { 1646, IconName::HockeyPuck },
            { 1647, IconName::HockeyStickPuck },
            { 1648, IconName::HockeySticks },
            { 1649, IconName::HollyBerry },
            { 1650, IconName::HoneyPot },
            { 1651, IconName::HoodCloak },
            { 1652, IconName::HorizontalRule },
            { 1653, IconName::Horse },
            { 1654, IconName::HorseHead },
            { 1655, IconName::HorseSaddle },
            { 1656, IconName::Hose },
            { 1657, IconName::HoseReel },
            { 1658, IconName::Hospital },
            { 1659, IconName::Hospitals },
            { 1660, IconName::HospitalUser },
            { 1661, IconName::P },
            { 1662, IconName::Hotel },
            { 1663, IconName::HotTubPerson },
            { 1664, IconName::Hourglass },
            { 1665, IconName::HourglassClock },
            { 1666, IconName::HourglassEnd },
            { 1667, IconName::HourglassHalf },
            { 1668, IconName::HourglassStart },
            { 1669, IconName::House },
            { 1670, IconName::HouseBlank },
            { 1671, IconName::HouseBuilding },
            { 1672, IconName::HouseChimney },
            { 1673, IconName::HouseChimneyBlank },
            { 1674, IconName::HouseChimneyCrack },
            { 1675, IconName::HouseChimneyHeart },
            { 1676, IconName::HouseChimneyUser },
            { 1677, IconName::HouseChimneyWindow },
            { 1678, IconName::HouseChimneyMedical },
            { 1679, IconName::HouseCircleCheck },
            { 1680, IconName::HouseCircleExclamation },
            { 1681, IconName::HouseCircleXmark },
            { 1682, IconName::HouseDay },
            { 1683, IconName::HouseCrack },
            { 1684, IconName::HouseFire },
            { 1685, IconName::HouseFlag },
            { 1686, IconName::HouseFloodWater },
            { 1687, IconName::HouseFloodWaterCircleArrowRight },
            { 1688, IconName::HouseHeart },
            { 1689, IconName::HouseLaptop },
            { 1690, IconName::HouseLock },
            { 1691, IconName::HouseMedical },
            { 1692, IconName::HouseMedicalCircleCheck },
            { 1693, IconName::HouseMedicalCircleExclamation },
            { 1694, IconName::HouseMedicalCircleXmark },
            { 1695, IconName::HouseMedicalFlag },
            { 1696, IconName::HouseNight },
            { 1697, IconName::HousePersonLeave },
            { 1698, IconName::HousePersonReturn },
            { 1699, IconName::HouseSignal },
            { 1700, IconName::HouseTree },
            { 1701, IconName::HouseTsunami },
            { 1702, IconName::HouseTurret },
            { 1703, IconName::HouseUser },
            { 1704, IconName::HouseWater },
            { 1705, IconName::HouseWindow },
            { 1706, IconName::HryvniaSign },
            { 1707, IconName::HundredPoints },
            { 1708, IconName::Hurricane },
            { 1709, IconName::Hyphen },
            { 1710, IconName::I },
            { 1711, IconName::IceCream },
            { 1712, IconName::IceSkate },
            { 1713, IconName::Icicles },
            { 1714, IconName::Icons },
            { 1715, IconName::ICursor },
            { 1716, IconName::IdBadge },
            { 1717, IconName::IdCard },
            { 1718, IconName::IdCardClip },
            { 1719, IconName::Igloo },
            { 1720, IconName::Image },
            { 1721, IconName::ImageLandscape },
            { 1722, IconName::ImagePolaroid },
            { 1723, IconName::ImagePolaroidUser },
            { 1724, IconName::ImagePortrait },
            { 1725, IconName::Images },
            { 1726, IconName::ImageSlash },
            { 1727, IconName::ImagesUser },
            { 1728, IconName::ImageUser },
            { 1729, IconName::Inboxes },
            { 1730, IconName::InboxFull },
            { 1731, IconName::InboxIn },
            { 1732, IconName::Inbox },
            { 1733, IconName::IndianRupeeSign },
            { 1734, IconName::Industry },
            { 1735, IconName::InboxOut },
            { 1736, IconName::IndustryWindows },
            { 1737, IconName::Infinity },
            { 1738, IconName::Indent },
            { 1739, IconName::Info },
            { 1740, IconName::Inhaler },
            { 1741, IconName::InputNumeric },
            { 1742, IconName::InputPipe },
            { 1743, IconName::InputText },
            { 1744, IconName::Integral },
            { 1745, IconName::Interrobang },
            { 1746, IconName::Intersection },
            { 1747, IconName::J },
            { 1748, IconName::JackOLantern },
            { 1749, IconName::Jar },
            { 1750, IconName::Italic },
            { 1751, IconName::IslandTropical },
            { 1752, IconName::JarWheat },
            { 1753, IconName::Jedi },
            { 1754, IconName::JetFighter },
            { 1755, IconName::Joystick },
            { 1756, IconName::Jug },
            { 1757, IconName::JetFighterUp },
            { 1758, IconName::Joint },
            { 1759, IconName::JugDetergent },
            { 1760, IconName::JugBottle },
            { 1761, IconName::K },
            { 1762, IconName::Kaaba },
            { 1763, IconName::Kazoo },
            { 1764, IconName::Kerning },
            { 1765, IconName::Key },
            { 1766, IconName::Keyboard },
            { 1767, IconName::KeyboardBrightness },
            { 1768, IconName::KeyboardBrightnessLow },
            { 1769, IconName::KeyboardDown },
            { 1770, IconName::KeyboardLeft },
            { 1771, IconName::Keynote },
            { 1772, IconName::KeySkeleton },
            { 1773, IconName::KeySkeletonLeftRight },
            { 1774, IconName::Khanda },
            { 1775, IconName::Kidneys },
            { 1776, IconName::KipSign },
            { 1777, IconName::KitchenSet },
            { 1778, IconName::Kite },
            { 1779, IconName::KitMedical },
            { 1780, IconName::KiwiBird },
            { 1781, IconName::KiwiFruit },
            { 1782, IconName::Knife },
            { 1783, IconName::KnifeKitchen },
            { 1784, IconName::L },
            { 1785, IconName::LacrosseStick },
            { 1786, IconName::LacrosseStickBall },
            { 1787, IconName::Lambda },
            { 1788, IconName::Lamp },
            { 1789, IconName::LampDesk },
            { 1790, IconName::LampFloor },
            { 1791, IconName::LampStreet },
            { 1792, IconName::Landmark },
            { 1793, IconName::LandmarkDome },
            { 1794, IconName::LandmarkFlag },
            { 1795, IconName::LandmarkMagnifyingGlass },
            { 1796, IconName::LandMineOn },
            { 1797, IconName::Language },
            { 1798, IconName::Laptop },
            { 1799, IconName::LaptopArrowDown },
            { 1800, IconName::LaptopBinary },
            { 1801, IconName::LaptopCode },
            { 1802, IconName::LaptopFile },
            { 1803, IconName::LaptopMedical },
            { 1804, IconName::LaptopMobile },
            { 1805, IconName::LaptopSlash },
            { 1806, IconName::LariSign },
            { 1807, IconName::Lasso },
            { 1808, IconName::LassoSparkles },
            { 1809, IconName::LayerGroup },
            { 1810, IconName::LayerMinus },
            { 1811, IconName::LayerPlus },
            { 1812, IconName::Leaf },
            { 1813, IconName::LeafHeart },
            { 1814, IconName::LeafMaple },
            { 1815, IconName::LeafOak },
            { 1816, IconName::LeafyGreen },
            { 1817, IconName::Left },
            { 1818, IconName::LeftFromLine },
            { 1819, IconName::LeftLong },
            { 1820, IconName::LeftLongToLine },
            { 1821, IconName::LeftRight },
            { 1822, IconName::LeftToLine },
            { 1823, IconName::Lemon },
            { 1824, IconName::LessThan },
            { 1825, IconName::LessThanEqual },
            { 1826, IconName::LifeRing },
            { 1827, IconName::Lightbulb },
            { 1828, IconName::LightbulbCfl },
            { 1829, IconName::LightbulbCflOn },
            { 1830, IconName::LightbulbDollar },
            { 1831, IconName::LightbulbExclamation },
            { 1832, IconName::LightbulbExclamationOn },
            { 1833, IconName::LightbulbGear },
            { 1834, IconName::LightbulbOn },
            { 1835, IconName::LightbulbSlash },
            { 1836, IconName::LightCeiling },
            { 1837, IconName::LightEmergency },
            { 1838, IconName::LightEmergencyOn },
            { 1839, IconName::Lighthouse },
            { 1840, IconName::LightsHoliday },
            { 1841, IconName::LightSwitch },
            { 1842, IconName::LightSwitchOff },
            { 1843, IconName::LightSwitchOn },
            { 1844, IconName::LineColumns },
            { 1845, IconName::LineHeight },
            { 1846, IconName::LinesLeaning },
            { 1847, IconName::Link },
            { 1848, IconName::LinkHorizontal },
            { 1849, IconName::LinkHorizontalSlash },
            { 1850, IconName::LinkSimple },
            { 1851, IconName::LinkSimpleSlash },
            { 1852, IconName::LinkSlash },
            { 1853, IconName::Lips },
            { 1854, IconName::LiraSign },
            { 1855, IconName::List },
            { 1856, IconName::ListCheck },
            { 1857, IconName::ListDropdown },
            { 1858, IconName::ListMusic },
            { 1859, IconName::ListOl },
            { 1860, IconName::ListRadio },
            { 1861, IconName::ListTimeline },
            { 1862, IconName::ListTree },
            { 1863, IconName::ListUl },
            { 1864, IconName::LitecoinSign },
            { 1865, IconName::Loader },
            { 1866, IconName::Lobster },
            { 1867, IconName::LocationArrow },
            { 1868, IconName::LocationArrowUp },
            { 1869, IconName::LocationCheck },
            { 1870, IconName::LocationCrosshairs },
            { 1871, IconName::LocationCrosshairsSlash },
            { 1872, IconName::LocationDot },
            { 1873, IconName::LocationDotSlash },
            { 1874, IconName::LocationExclamation },
            { 1875, IconName::LocationMinus },
            { 1876, IconName::LocationPen },
            { 1877, IconName::LocationPin },
            { 1878, IconName::LocationPinLock },
            { 1879, IconName::LocationPinSlash },
            { 1880, IconName::LocationPlus },
            { 1881, IconName::LocationQuestion },
            { 1882, IconName::LocationSmile },
            { 1883, IconName::LocationXmark },
            { 1884, IconName::Lock },
            { 1885, IconName::LockA },
            { 1886, IconName::LockHashtag },
            { 1887, IconName::LockKeyhole },
            { 1888, IconName::LockKeyholeOpen },
            { 1889, IconName::LockOpen },
            { 1890, IconName::Locust },
            { 1891, IconName::Lollipop },
            { 1892, IconName::Loveseat },
            { 1893, IconName::LuchadorMask },
            { 1894, IconName::Lungs },
            { 1895, IconName::LungsVirus },
            { 1896, IconName::M },
            { 1897, IconName::Mace },
            { 1898, IconName::Magnet },
            { 1899, IconName::MagnifyingGlass },
            { 1900, IconName::MagnifyingGlassArrowRight },
            { 1901, IconName::MagnifyingGlassArrowsRotate },
            { 1902, IconName::MagnifyingGlassChart },
            { 1903, IconName::MagnifyingGlassDollar },
            { 1904, IconName::MagnifyingGlassLocation },
            { 1905, IconName::MagnifyingGlassMinus },
            { 1906, IconName::MagnifyingGlassMusic },
            { 1907, IconName::MagnifyingGlassPlay },
            { 1908, IconName::MagnifyingGlassPlus },
            { 1909, IconName::MagnifyingGlassWaveform },
            { 1910, IconName::Mailbox },
            { 1911, IconName::MailboxFlagUp },
            { 1912, IconName::ManatSign },
            { 1913, IconName::Mandolin },
            { 1914, IconName::Mango },
            { 1915, IconName::Manhole },
            { 1916, IconName::Map },
            { 1917, IconName::MapLocation },
            { 1918, IconName::MapLocationDot },
            { 1919, IconName::MapPin },
            { 1920, IconName::Marker },
            { 1921, IconName::Mars },
            { 1922, IconName::MarsAndVenus },
            { 1923, IconName::MarsAndVenusBurst },
            { 1924, IconName::MarsDouble },
            { 1925, IconName::MarsStroke },
            { 1926, IconName::MarsStrokeRight },
            { 1927, IconName::MarsStrokeUp },
            { 1928, IconName::MartiniGlass },
            { 1929, IconName::MartiniGlassCitrus },
            { 1930, IconName::MartiniGlassEmpty },
            { 1931, IconName::Mask },
            { 1932, IconName::MaskFace },
            { 1933, IconName::MaskSnorkel },
            { 1934, IconName::MasksTheater },
            { 1935, IconName::MaskVentilator },
            { 1936, IconName::MattressPillow },
            { 1937, IconName::Maximize },
            { 1938, IconName::Meat },
            { 1939, IconName::Medal },
            { 1940, IconName::Megaphone },
            { 1941, IconName::Melon },
            { 1942, IconName::MelonSlice },
            { 1943, IconName::Memo },
            { 1944, IconName::MemoCircleCheck },
            { 1945, IconName::MemoCircleInfo },
            { 1946, IconName::MemoPad },
            { 1947, IconName::Memory },
            { 1948, IconName::Menorah },
            { 1949, IconName::Mercury },
            { 1950, IconName::Merge },
            { 1951, IconName::Message },
            { 1952, IconName::MessageArrowDown },
            { 1953, IconName::MessageArrowUp },
            { 1954, IconName::MessageArrowUpRight },
            { 1955, IconName::MessageBot },
            { 1956, IconName::MessageCaptions },
            { 1957, IconName::MessageCheck },
            { 1958, IconName::MessageCode },
            { 1959, IconName::MessageDollar },
            { 1960, IconName::MessageDots },
            { 1961, IconName::MessageExclamation },
            { 1962, IconName::MessageHeart },
            { 1963, IconName::MessageImage },
            { 1964, IconName::MessageLines },
            { 1965, IconName::MessageMedical },
            { 1966, IconName::MessageMiddle },
            { 1967, IconName::MessageMiddleTop },
            { 1968, IconName::MessageMinus },
            { 1969, IconName::MessageMusic },
            { 1970, IconName::MessagePen },
            { 1971, IconName::MessagePlus },
            { 1972, IconName::MessageQuestion },
            { 1973, IconName::MessageQuote },
            { 1974, IconName::Messages },
            { 1975, IconName::MessagesDollar },
            { 1976, IconName::MessageSlash },
            { 1977, IconName::MessageSmile },
            { 1978, IconName::MessageSms },
            { 1979, IconName::MessagesQuestion },
            { 1980, IconName::MessageText },
            { 1981, IconName::MessageXmark },
            { 1982, IconName::Meteor },
            { 1983, IconName::Meter },
            { 1984, IconName::MeterBolt },
            { 1985, IconName::MeterDroplet },
            { 1986, IconName::MeterFire },
            { 1987, IconName::Microchip },
            { 1988, IconName::MicrochipAi },
            { 1989, IconName::Microphone },
            { 1990, IconName::MicrophoneLines },
            { 1991, IconName::MicrophoneLinesSlash },
            { 1992, IconName::MicrophoneSlash },
            { 1993, IconName::MicrophoneStand },
            { 1994, IconName::Microscope },
            { 1995, IconName::Microwave },
            { 1996, IconName::MillSign },
            { 1997, IconName::Minimize },
            { 1998, IconName::Minus },
            { 1999, IconName::Mistletoe },
            { 2000, IconName::Mitten },
            { 2001, IconName::Mobile },
            { 2002, IconName::MobileButton },
            { 2003, IconName::MobileNotch },
            { 2004, IconName::MobileRetro },
            { 2005, IconName::MobileScreen },
            { 2006, IconName::MobileScreenButton },
            { 2007, IconName::MobileSignal },
            { 2008, IconName::MobileSignalOut },
            { 2009, IconName::MoneyBill },
            { 2010, IconName::MoneyBill1 },
            { 2011, IconName::MoneyBill1Wave },
            { 2012, IconName::MoneyBills },
            { 2013, IconName::MoneyBillSimple },
            { 2014, IconName::MoneyBillSimpleWave },
            { 2015, IconName::MoneyBillsSimple },
            { 2016, IconName::MoneyBillTransfer },
            { 2017, IconName::MoneyBillTrendUp },
            { 2018, IconName::MoneyBillWave },
            { 2019, IconName::MoneyBillWheat },
            { 2020, IconName::MoneyCheck },
            { 2021, IconName::MoneyCheckDollar },
            { 2022, IconName::MoneyCheckDollarPen },
            { 2023, IconName::MoneyCheckPen },
            { 2024, IconName::MoneyFromBracket },
            { 2025, IconName::MoneySimpleFromBracket },
            { 2026, IconName::MonitorWaveform },
            { 2027, IconName::Monkey },
            { 2028, IconName::Monument },
            { 2029, IconName::Moon },
            { 2030, IconName::MoonCloud },
            { 2031, IconName::MoonOverSun },
            { 2032, IconName::MoonStars },
            { 2033, IconName::Moped },
            { 2034, IconName::MortarPestle },
            { 2035, IconName::Mosque },
            { 2036, IconName::Mosquito },
            { 2037, IconName::MosquitoNet },
            { 2038, IconName::Motorcycle },
            { 2039, IconName::Mound },
            { 2040, IconName::Mountain },
            { 2041, IconName::MountainCity },
            { 2042, IconName::Mountains },
            { 2043, IconName::MountainSun },
            { 2044, IconName::MouseField },
            { 2045, IconName::Mp3Player },
            { 2046, IconName::Mug },
            { 2047, IconName::MugHot },
            { 2048, IconName::MugMarshmallows },
            { 2049, IconName::MugSaucer },
            { 2050, IconName::MugTea },
            { 2051, IconName::MugTeaSaucer },
            { 2052, IconName::Mushroom },
            { 2053, IconName::Music },
            { 2054, IconName::MusicMagnifyingGlass },
            { 2055, IconName::MusicNote },
            { 2056, IconName::MusicNoteSlash },
            { 2057, IconName::MusicSlash },
            { 2058, IconName::Mustache },
            { 2059, IconName::N },
            { 2060, IconName::NairaSign },
            { 2061, IconName::Narwhal },
            { 2062, IconName::NestingDolls },
            { 2063, IconName::NetworkWired },
            { 2064, IconName::Neuter },
            { 2065, IconName::Newspaper },
            { 2066, IconName::Nfc },
            { 2067, IconName::NfcLock },
            { 2068, IconName::NfcMagnifyingGlass },
            { 2069, IconName::NfcPen },
            { 2070, IconName::NfcSignal },
            { 2071, IconName::NfcSlash },
            { 2072, IconName::NfcSymbol },
            { 2073, IconName::NfcTrash },
            { 2074, IconName::Nose },
            { 2075, IconName::Notdef },
            { 2076, IconName::Note },
            { 2077, IconName::Notebook },
            { 2078, IconName::NoteMedical },
            { 2079, IconName::NotEqual },
            { 2080, IconName::Notes },
            { 2081, IconName::NotesMedical },
            { 2082, IconName::NoteSticky },
            { 2083, IconName::O },
            { 2084, IconName::ObjectExclude },
            { 2085, IconName::ObjectGroup },
            { 2086, IconName::ObjectIntersect },
            { 2087, IconName::ObjectsAlignBottom },
            { 2088, IconName::ObjectsAlignCenterHorizontal },
            { 2089, IconName::ObjectsAlignCenterVertical },
            { 2090, IconName::ObjectsAlignLeft },
            { 2091, IconName::ObjectsAlignRight },
            { 2092, IconName::ObjectsAlignTop },
            { 2093, IconName::ObjectsColumn },
            { 2094, IconName::ObjectSubtract },
            { 2095, IconName::ObjectUngroup },
            { 2096, IconName::ObjectUnion },
            { 2097, IconName::Octagon },
            { 2098, IconName::OctagonCheck },
            { 2099, IconName::OctagonDivide },
            { 2100, IconName::OctagonExclamation },
            { 2101, IconName::OctagonMinus },
            { 2102, IconName::OctagonPlus },
            { 2103, IconName::OctagonXmark },
            { 2104, IconName::OilCan },
            { 2105, IconName::OilCanDrip },
            { 2106, IconName::OilTemperature },
            { 2107, IconName::OilWell },
            { 2108, IconName::Olive },
            { 2109, IconName::OliveBranch },
            { 2110, IconName::Om },
            { 2111, IconName::Omega },
            { 2112, IconName::Onion },
            { 2113, IconName::Option },
            { 2114, IconName::Ornament },
            { 2115, IconName::Otter },
            { 2116, IconName::Outdent },
            { 2117, IconName::Outlet },
            { 2118, IconName::Oven },
            { 2119, IconName::Overline },
            { 2120, IconName::SignalSlash },
            { 2121, IconName::PageCaretDown },
            { 2122, IconName::Page },
            { 2123, IconName::PageCaretUp },
            { 2124, IconName::Pager },
            { 2125, IconName::Paintbrush },
            { 2126, IconName::PaintbrushFine },
            { 2127, IconName::PaintbrushPencil },
            { 2128, IconName::PaintRoller },
            { 2129, IconName::Pallet },
            { 2130, IconName::Palette },
            { 2131, IconName::PalletBox },
            { 2132, IconName::PalletBoxes },
            { 2133, IconName::Pancakes },
            { 2134, IconName::PanelEws },
            { 2135, IconName::PanelFire },
            { 2136, IconName::PanFood },
            { 2137, IconName::PanFrying },
            { 2138, IconName::Panorama },
            { 2139, IconName::Paperclip },
            { 2140, IconName::PaperclipVertical },
            { 2141, IconName::PaperPlane },
            { 2142, IconName::PaperPlaneTop },
            { 2143, IconName::ParachuteBox },
            { 2144, IconName::Paragraph },
            { 2145, IconName::PartyBell },
            { 2146, IconName::ParagraphLeft },
            { 2147, IconName::PartyHorn },
            { 2148, IconName::Passport },
            { 2149, IconName::Paste },
            { 2150, IconName::Pause },
            { 2151, IconName::Paw },
            { 2152, IconName::PawClaws },
            { 2153, IconName::PawSimple },
            { 2154, IconName::Peace },
            { 2155, IconName::Peach },
            { 2156, IconName::Peanut },
            { 2157, IconName::Peanuts },
            { 2158, IconName::Peapod },
            { 2159, IconName::Pear },
            { 2160, IconName::Pedestal },
            { 2161, IconName::Pegasus },
            { 2162, IconName::Pen },
            { 2163, IconName::Pencil },
            { 2164, IconName::PencilMechanical },
            { 2165, IconName::PencilSlash },
            { 2166, IconName::PenCircle },
            { 2167, IconName::PenClip },
            { 2168, IconName::PenClipSlash },
            { 2169, IconName::PenFancy },
            { 2170, IconName::PenFancySlash },
            { 2171, IconName::PenField },
            { 2172, IconName::PenNib },
            { 2173, IconName::PenLine },
            { 2174, IconName::PenNibSlash },
            { 2175, IconName::PenPaintbrush },
            { 2176, IconName::PenRuler },
            { 2177, IconName::PenSlash },
            { 2178, IconName::PenSwirl },
            { 2179, IconName::PenToSquare },
            { 2180, IconName::People },
            { 2181, IconName::PeopleArrows },
            { 2182, IconName::PeopleCarryBox },
            { 2183, IconName::PeopleDress },
            { 2184, IconName::PeopleDressSimple },
            { 2185, IconName::PeopleGroup },
            { 2186, IconName::PeopleLine },
            { 2187, IconName::PeoplePants },
            { 2188, IconName::PeoplePantsSimple },
            { 2189, IconName::PeoplePulling },
            { 2190, IconName::PeopleRobbery },
            { 2191, IconName::PeopleRoof },
            { 2192, IconName::PeopleSimple },
            { 2193, IconName::Pepper },
            { 2194, IconName::PepperHot },
            { 2195, IconName::Person },
            { 2196, IconName::Percent },
            { 2197, IconName::Period },
            { 2198, IconName::PersonArrowDownToLine },
            { 2199, IconName::PersonBiking },
            { 2200, IconName::PersonArrowUpFromLine },
            { 2201, IconName::PersonBikingMountain },
            { 2202, IconName::PersonBooth },
            { 2203, IconName::PersonBreastfeeding },
            { 2204, IconName::PersonBurst },
            { 2205, IconName::PersonCane },
            { 2206, IconName::PersonCarryBox },
            { 2207, IconName::PersonChalkboard },
            { 2208, IconName::PersonCircleCheck },
            { 2209, IconName::PersonCircleExclamation },
            { 2210, IconName::PersonCircleMinus },
            { 2211, IconName::PersonCirclePlus },
            { 2212, IconName::PersonCircleQuestion },
            { 2213, IconName::PersonCircleXmark },
            { 2214, IconName::PersonDigging },
            { 2215, IconName::PersonDollyEmpty },
            { 2216, IconName::PersonDotsFromLine },
            { 2217, IconName::PersonDressBurst },
            { 2218, IconName::PersonDress },
            { 2219, IconName::PersonDolly },
            { 2220, IconName::PersonDressFairy },
            { 2221, IconName::PersonDressSimple },
            { 2222, IconName::PersonDrowning },
            { 2223, IconName::PersonFairy },
            { 2224, IconName::PersonFalling },
            { 2225, IconName::PersonFallingBurst },
            { 2226, IconName::PersonFromPortal },
            { 2227, IconName::PersonHalfDress },
            { 2228, IconName::PersonHarassing },
            { 2229, IconName::PersonHiking },
            { 2230, IconName::PersonMilitaryPointing },
            { 2231, IconName::PersonMilitaryRifle },
            { 2232, IconName::PersonMilitaryToPerson },
            { 2233, IconName::PersonPinball },
            { 2234, IconName::PersonPraying },
            { 2235, IconName::PersonPregnant },
            { 2236, IconName::PersonRays },
            { 2237, IconName::PersonRifle },
            { 2238, IconName::PersonRunning },
            { 2239, IconName::PersonRunningFast },
            { 2240, IconName::PersonSeat },
            { 2241, IconName::PersonSeatReclined },
            { 2242, IconName::PersonShelter },
            { 2243, IconName::PersonSign },
            { 2244, IconName::PersonSimple },
            { 2245, IconName::PersonSkating },
            { 2246, IconName::PersonSkiing },
            { 2247, IconName::PersonSkiingNordic },
            { 2248, IconName::PersonSkiJumping },
            { 2249, IconName::PersonSkiLift },
            { 2250, IconName::PersonSledding },
            { 2251, IconName::PersonSnowboarding },
            { 2252, IconName::PersonSnowmobiling },
            { 2253, IconName::PersonSwimming },
            { 2254, IconName::PersonThroughWindow },
            { 2255, IconName::PersonToDoor },
            { 2256, IconName::PersonToPortal },
            { 2257, IconName::PersonWalking },
            { 2258, IconName::PersonWalkingArrowLoopLeft },
            { 2259, IconName::PersonWalkingArrowRight },
            { 2260, IconName::PersonWalkingDashedLineArrowRight },
            { 2261, IconName::PersonWalkingLuggage },
            { 2262, IconName::PersonWalkingWithCane },
            { 2263, IconName::PesetaSign },
            { 2264, IconName::PesoSign },
            { 2265, IconName::Phone },
            { 2266, IconName::PhoneArrowDownLeft },
            { 2267, IconName::PhoneArrowRight },
            { 2268, IconName::PhoneArrowUpRight },
            { 2269, IconName::PhoneFlip },
            { 2270, IconName::PhoneHangup },
            { 2271, IconName::PhoneIntercom },
            { 2272, IconName::PhoneMissed },
            { 2273, IconName::PhoneOffice },
            { 2274, IconName::PhonePlus },
            { 2275, IconName::PhoneRotary },
            { 2276, IconName::PhoneSlash },
            { 2277, IconName::PhoneVolume },
            { 2278, IconName::PhoneXmark },
            { 2279, IconName::PhotoFilm },
            { 2280, IconName::PhotoFilmMusic },
            { 2281, IconName::Pi },
            { 2282, IconName::Piano },
            { 2283, IconName::PiggyBank },
            { 2284, IconName::Pig },
            { 2285, IconName::PianoKeyboard },
            { 2286, IconName::Pickaxe },
            { 2287, IconName::Pinata },
            { 2288, IconName::Pinball },
            { 2289, IconName::Pie },
            { 2290, IconName::Pills },
            { 2291, IconName::Pickleball },
            { 2292, IconName::Pineapple },
            { 2293, IconName::Pipe },
            { 2294, IconName::PipeCircleCheck },
            { 2295, IconName::PipeCollar },
            { 2296, IconName::PipeSection },
            { 2297, IconName::PipeSmoking },
            { 2298, IconName::PipeValve },
            { 2299, IconName::Pizza },
            { 2300, IconName::PizzaSlice },
            { 2301, IconName::PlaceOfWorship },
            { 2302, IconName::Plane },
            { 2303, IconName::PlaneArrival },
            { 2304, IconName::PlaneCircleCheck },
            { 2305, IconName::PlaneCircleExclamation },
            { 2306, IconName::PlaneCircleXmark },
            { 2307, IconName::PlaneDeparture },
            { 2308, IconName::PlaneEngines },
            { 2309, IconName::PlaneLock },
            { 2310, IconName::PlaneProp },
            { 2311, IconName::PlaneSlash },
            { 2312, IconName::PlaneTail },
            { 2313, IconName::PlanetMoon },
            { 2314, IconName::PlanetRinged },
            { 2315, IconName::PlaneUp },
            { 2316, IconName::PlaneUpSlash },
            { 2317, IconName::PlantWilt },
            { 2318, IconName::PlateUtensils },
            { 2319, IconName::PlateWheat },
            { 2320, IconName::Play },
            { 2321, IconName::PlayPause },
            { 2322, IconName::Plug },
            { 2323, IconName::PlugCircleBolt },
            { 2324, IconName::PlugCircleCheck },
            { 2325, IconName::PlugCircleExclamation },
            { 2326, IconName::PlugCircleMinus },
            { 2327, IconName::PlugCirclePlus },
            { 2328, IconName::PlugCircleXmark },
            { 2329, IconName::Plus },
            { 2330, IconName::PlusLarge },
            { 2331, IconName::PlusMinus },
            { 2332, IconName::Podcast },
            { 2333, IconName::Podium },
            { 2334, IconName::PodiumStar },
            { 2335, IconName::PoliceBox },
            { 2336, IconName::PollPeople },
            { 2337, IconName::Pompebled },
            { 2338, IconName::Poo },
            { 2339, IconName::Pool8Ball },
            { 2340, IconName::Poop },
            { 2341, IconName::PooStorm },
            { 2342, IconName::Popcorn },
            { 2343, IconName::Popsicle },
            { 2344, IconName::Potato },
            { 2345, IconName::PotFood },
            { 2346, IconName::PowerOff },
            { 2347, IconName::Prescription },
            { 2348, IconName::PrescriptionBottle },
            { 2349, IconName::PrescriptionBottleMedical },
            { 2350, IconName::PrescriptionBottlePill },
            { 2351, IconName::PresentationScreen },
            { 2352, IconName::Pretzel },
            { 2353, IconName::Print },
            { 2354, IconName::PrintMagnifyingGlass },
            { 2355, IconName::PrintSlash },
            { 2356, IconName::Projector },
            { 2357, IconName::Pump },
            { 2358, IconName::Pumpkin },
            { 2359, IconName::PumpMedical },
            { 2360, IconName::PumpSoap },
            { 2361, IconName::Puzzle },
            { 2362, IconName::PuzzlePiece },
            { 2363, IconName::PuzzlePieceSimple },
            { 2364, IconName::Q },
            { 2365, IconName::Qrcode },
            { 2366, IconName::Question },
            { 2367, IconName::QuoteLeft },
            { 2368, IconName::QuoteRight },
            { 2369, IconName::Quotes },
            { 2370, IconName::R },
            { 2371, IconName::Rabbit },
            { 2372, IconName::RabbitRunning },
            { 2373, IconName::Raccoon },
            { 2374, IconName::Racquet },
            { 2375, IconName::Radar },
            { 2376, IconName::Radiation },
            { 2377, IconName::Radio },
            { 2378, IconName::RadioTuner },
            { 2379, IconName::Rainbow },
            { 2380, IconName::Raindrops },
            { 2381, IconName::Ram },
            { 2382, IconName::RampLoading },
            { 2383, IconName::RankingStar },
            { 2384, IconName::Raygun },
            { 2385, IconName::Receipt },
            { 2386, IconName::RecordVinyl },
            { 2387, IconName::Rectangle },
            { 2388, IconName::RectangleAd },
            { 2389, IconName::RectangleBarcode },
            { 2390, IconName::RectangleCode },
            { 2391, IconName::RectangleHistory },
            { 2392, IconName::RectangleHistoryCirclePlus },
            { 2393, IconName::RectangleHistoryCircleUser },
            { 2394, IconName::RectangleList },
            { 2395, IconName::RectanglePro },
            { 2396, IconName::RectanglesMixed },
            { 2397, IconName::RectangleTerminal },
            { 2398, IconName::RectangleVertical },
            { 2399, IconName::RectangleVerticalHistory },
            { 2400, IconName::RectangleWide },
            { 2401, IconName::RectangleXmark },
            { 2402, IconName::Recycle },
            { 2403, IconName::Reel },
            { 2404, IconName::ReflectHorizontal },
            { 2405, IconName::ReflectVertical },
            { 2406, IconName::Refrigerator },
            { 2407, IconName::Registered },
            { 2408, IconName::Repeat },
            { 2409, IconName::Repeat1 },
            { 2410, IconName::Reply },
            { 2411, IconName::ReplyAll },
            { 2412, IconName::ReplyClock },
            { 2413, IconName::Republican },
            { 2414, IconName::Restroom },
            { 2415, IconName::RestroomSimple },
            { 2416, IconName::Retweet },
            { 2417, IconName::Rhombus },
            { 2418, IconName::Ribbon },
            { 2419, IconName::Right },
            { 2420, IconName::RightFromBracket },
            { 2421, IconName::RightFromLine },
            { 2422, IconName::RightLeft },
            { 2423, IconName::RightLeftLarge },
            { 2424, IconName::RightLong },
            { 2425, IconName::RightLongToLine },
            { 2426, IconName::RightToBracket },
            { 2427, IconName::RightToLine },
            { 2428, IconName::Ring },
            { 2429, IconName::RingDiamond },
            { 2430, IconName::RingsWedding },
            { 2431, IconName::Road },
            { 2432, IconName::RoadBarrier },
            { 2433, IconName::RoadBridge },
            { 2434, IconName::RoadCircleCheck },
            { 2435, IconName::RoadCircleExclamation },
            { 2436, IconName::RoadCircleXmark },
            { 2437, IconName::RoadLock },
            { 2438, IconName::RoadSpikes },
            { 2439, IconName::Robot },
            { 2440, IconName::RobotAstromech },
            { 2441, IconName::Rocket },
            { 2442, IconName::RocketLaunch },
            { 2443, IconName::RollerCoaster },
            { 2444, IconName::Rotate },
            { 2445, IconName::RotateExclamation },
            { 2446, IconName::RotateLeft },
            { 2447, IconName::RotateReverse },
            { 2448, IconName::RotateRight },
            { 2449, IconName::Route },
            { 2450, IconName::RouteHighway },
            { 2451, IconName::RouteInterstate },
            { 2452, IconName::Router },
            { 2453, IconName::Rss },
            { 2454, IconName::RubleSign },
            { 2455, IconName::Rug },
            { 2456, IconName::RugbyBall },
            { 2457, IconName::Ruler },
            { 2458, IconName::RulerCombined },
            { 2459, IconName::RulerHorizontal },
            { 2460, IconName::RulerTriangle },
            { 2461, IconName::RulerVertical },
            { 2462, IconName::RupeeSign },
            { 2463, IconName::RupiahSign },
            { 2464, IconName::Rv },
            { 2465, IconName::S },
            { 2466, IconName::Sack },
            { 2467, IconName::SackDollar },
            { 2468, IconName::SackXmark },
            { 2469, IconName::Sailboat },
            { 2470, IconName::Salad },
            { 2471, IconName::SaltShaker },
            { 2472, IconName::Sandwich },
            { 2473, IconName::Satellite },
            { 2474, IconName::SatelliteDish },
            { 2475, IconName::Sausage },
            { 2476, IconName::Saxophone },
            { 2477, IconName::SaxophoneFire },
            { 2478, IconName::ScaleBalanced },
            { 2479, IconName::ScaleUnbalanced },
            { 2480, IconName::ScaleUnbalancedFlip },
            { 2481, IconName::Scalpel },
            { 2482, IconName::ScalpelLineDashed },
            { 2483, IconName::ScannerGun },
            { 2484, IconName::ScannerImage },
            { 2485, IconName::ScannerKeyboard },
            { 2486, IconName::ScannerTouchscreen },
            { 2487, IconName::Scarecrow },
            { 2488, IconName::Scarf },
            { 2489, IconName::School },
            { 2490, IconName::SchoolCircleCheck },
            { 2491, IconName::SchoolCircleExclamation },
            { 2492, IconName::SchoolCircleXmark },
            { 2493, IconName::SchoolFlag },
            { 2494, IconName::SchoolLock },
            { 2495, IconName::Scissors },
            { 2496, IconName::Screencast },
            { 2497, IconName::ScreenUsers },
            { 2498, IconName::Screwdriver },
            { 2499, IconName::ScrewdriverWrench },
            { 2500, IconName::Scribble },
            { 2501, IconName::Scroll },
            { 2502, IconName::ScrollOld },
            { 2503, IconName::ScrollTorah },
            { 2504, IconName::Scrubber },
            { 2505, IconName::Scythe },
            { 2506, IconName::SdCard },
            { 2507, IconName::SdCards },
            { 2508, IconName::Seal },
            { 2509, IconName::SealExclamation },
            { 2510, IconName::SealQuestion },
            { 2511, IconName::SeatAirline },
            { 2512, IconName::Section },
            { 2513, IconName::Seedling },
            { 2514, IconName::Semicolon },
            { 2515, IconName::SendBack },
            { 2516, IconName::SendBackward },
            { 2517, IconName::Sensor },
            { 2518, IconName::SensorCloud },
            { 2519, IconName::SensorFire },
            { 2520, IconName::SensorOn },
            { 2521, IconName::SensorTriangleExclamation },
            { 2522, IconName::Server },
            { 2523, IconName::Shapes },
            { 2524, IconName::Share },
            { 2525, IconName::ShareAll },
            { 2526, IconName::ShareFromSquare },
            { 2527, IconName::ShareNodes },
            { 2528, IconName::Sheep },
            { 2529, IconName::SheetPlastic },
            { 2530, IconName::ShekelSign },
            { 2531, IconName::Shelves },
            { 2532, IconName::ShelvesEmpty },
            { 2533, IconName::Shield },
            { 2534, IconName::ShieldCat },
            { 2535, IconName::ShieldCheck },
            { 2536, IconName::ShieldCross },
            { 2537, IconName::ShieldDog },
            { 2538, IconName::ShieldExclamation },
            { 2539, IconName::ShieldHalved },
            { 2540, IconName::ShieldHeart },
            { 2541, IconName::ShieldKeyhole },
            { 2542, IconName::ShieldMinus },
            { 2543, IconName::ShieldPlus },
            { 2544, IconName::ShieldQuartered },
            { 2545, IconName::ShieldSlash },
            { 2546, IconName::ShieldVirus },
            { 2547, IconName::ShieldXmark },
            { 2548, IconName::Ship },
            { 2549, IconName::Shirt },
            { 2550, IconName::ShirtLongSleeve },
            { 2551, IconName::ShirtRunning },
            { 2552, IconName::ShirtTankTop },
            { 2553, IconName::ShishKebab },
            { 2554, IconName::ShoePrints },
            { 2555, IconName::Shop },
            { 2556, IconName::ShopLock },
            { 2557, IconName::ShopSlash },
            { 2558, IconName::Shovel },
            { 2559, IconName::ShovelSnow },
            { 2560, IconName::Shower },
            { 2561, IconName::ShowerDown },
            { 2562, IconName::Shredder },
            { 2563, IconName::Shrimp },
            { 2564, IconName::Shuffle },
            { 2565, IconName::Shutters },
            { 2566, IconName::Shuttlecock },
            { 2567, IconName::ShuttleSpace },
            { 2568, IconName::Sickle },
            { 2569, IconName::Sidebar },
            { 2570, IconName::SidebarFlip },
            { 2571, IconName::Sigma },
            { 2572, IconName::Signal },
            { 2573, IconName::SignalBars },
            { 2574, IconName::SignalBarsFair },
            { 2575, IconName::SignalBarsGood },
            { 2576, IconName::SignalBarsSlash },
            { 2577, IconName::SignalBarsWeak },
            { 2578, IconName::SignalFair },
            { 2579, IconName::SignalGood },
            { 2580, IconName::UserHairBuns },
            { 2581, IconName::SignalStream },
            { 2582, IconName::SignalStreamSlash },
            { 2583, IconName::SignalStrong },
            { 2584, IconName::SignalWeak },
            { 2585, IconName::Signature },
            { 2586, IconName::SignatureLock },
            { 2587, IconName::SignatureSlash },
            { 2588, IconName::SignHanging },
            { 2589, IconName::SignPost },
            { 2590, IconName::SignPosts },
            { 2591, IconName::SignPostsWrench },
            { 2592, IconName::SignsPost },
            { 2593, IconName::SimCard },
            { 2594, IconName::SimCards },
            { 2595, IconName::Sink },
            { 2596, IconName::Siren },
            { 2597, IconName::SirenOn },
            { 2598, IconName::Sitemap },
            { 2599, IconName::Skeleton },
            { 2600, IconName::SkeletonRibs },
            { 2601, IconName::SkiBoot },
            { 2602, IconName::SkiBootSki },
            { 2603, IconName::Skull },
            { 2604, IconName::SkullCow },
            { 2605, IconName::SkullCrossbones },
            { 2606, IconName::Slash },
            { 2607, IconName::SlashBack },
            { 2608, IconName::SlashForward },
            { 2609, IconName::Sleigh },
            { 2610, IconName::Slider },
            { 2611, IconName::Sliders },
            { 2612, IconName::SlidersSimple },
            { 2613, IconName::SlidersUp },
            { 2614, IconName::SlotMachine },
            { 2615, IconName::Smog },
            { 2616, IconName::Smoke },
            { 2617, IconName::Smoking },
            { 2618, IconName::Snake },
            { 2619, IconName::Snooze },
            { 2620, IconName::SnowBlowing },
            { 2621, IconName::Snowflake },
            { 2622, IconName::SnowflakeDroplets },
            { 2623, IconName::Snowflakes },
            { 2624, IconName::Snowman },
            { 2625, IconName::SnowmanHead },
            { 2626, IconName::Snowplow },
            { 2627, IconName::Soap },
            { 2628, IconName::Socks },
            { 2629, IconName::SoftServe },
            { 2630, IconName::SolarPanel },
            { 2631, IconName::SolarSystem },
            { 2632, IconName::Sort },
            { 2633, IconName::SortDown },
            { 2634, IconName::SortUp },
            { 2635, IconName::Spa },
            { 2636, IconName::SpaceStationMoon },
            { 2637, IconName::SpaceStationMoonConstruction },
            { 2638, IconName::Spade },
            { 2639, IconName::SpaghettiMonsterFlying },
            { 2640, IconName::Sparkles },
            { 2641, IconName::Speaker },
            { 2642, IconName::Speakers },
            { 2643, IconName::SpiderBlackWidow },
            { 2644, IconName::SpiderWeb },
            { 2645, IconName::Sparkle },
            { 2646, IconName::Spinner },
            { 2647, IconName::Spider },
            { 2648, IconName::SpinnerScale },
            { 2649, IconName::SpinnerThird },
            { 2650, IconName::Splotch },
            { 2651, IconName::Split },
            { 2652, IconName::SprayCan },
            { 2653, IconName::SprayCanSparkles },
            { 2654, IconName::Sportsball },
            { 2655, IconName::Sprinkler },
            { 2656, IconName::SprinklerCeiling },
            { 2657, IconName::Square },
            { 2658, IconName::Square3 },
            { 2659, IconName::Square0 },
            { 2660, IconName::Square1 },
            { 2661, IconName::SpellCheck },
            { 2662, IconName::Square4 },
            { 2663, IconName::Square5 },
            { 2664, IconName::Spoon },
            { 2665, IconName::Square2 },
            { 2666, IconName::Square7 },
            { 2667, IconName::Square8 },
            { 2668, IconName::Square6 },
            { 2669, IconName::Square9 },
            { 2670, IconName::SquareALock },
            { 2671, IconName::SquareA },
            { 2672, IconName::SquareArrowDown },
            { 2673, IconName::SquareAmpersand },
            { 2674, IconName::SquareArrowDownRight },
            { 2675, IconName::SquareArrowDownLeft },
            { 2676, IconName::SquareArrowLeft },
            { 2677, IconName::SquareArrowRight },
            { 2678, IconName::SquareArrowUp },
            { 2679, IconName::SquareArrowUpLeft },
            { 2680, IconName::SquareArrowUpRight },
            { 2681, IconName::SquareB },
            { 2682, IconName::SquareBolt },
            { 2683, IconName::SquareC },
            { 2684, IconName::SquareCaretDown },
            { 2685, IconName::SquareCaretLeft },
            { 2686, IconName::SquareCaretRight },
            { 2687, IconName::SquareCaretUp },
            { 2688, IconName::SquareCheck },
            { 2689, IconName::SquareChevronDown },
            { 2690, IconName::SquareChevronLeft },
            { 2691, IconName::SquareChevronRight },
            { 2692, IconName::SquareChevronUp },
            { 2693, IconName::SquareCode },
            { 2694, IconName::SquareD },
            { 2695, IconName::SquareDashed },
            { 2696, IconName::SquareDashedCirclePlus },
            { 2697, IconName::SquareDivide },
            { 2698, IconName::SquareDollar },
            { 2699, IconName::SquareDown },
            { 2700, IconName::SquareDownLeft },
            { 2701, IconName::SquareDownRight },
            { 2702, IconName::SquareE },
            { 2703, IconName::SquareEllipsis },
            { 2704, IconName::SquareEllipsisVertical },
            { 2705, IconName::SquareEnvelope },
            { 2706, IconName::SquareExclamation },
            { 2707, IconName::SquareF },
            { 2708, IconName::SquareFragile },
            { 2709, IconName::SquareFull },
            { 2710, IconName::SquareG },
            { 2711, IconName::SquareH },
            { 2712, IconName::SquareHeart },
            { 2713, IconName::SquareI },
            { 2714, IconName::SquareInfo },
            { 2715, IconName::SquareJ },
            { 2716, IconName::SquareK },
            { 2717, IconName::SquareKanban },
            { 2718, IconName::SquareL },
            { 2719, IconName::SquareLeft },
            { 2720, IconName::SquareList },
            { 2721, IconName::SquareM },
            { 2722, IconName::SquareMinus },
            { 2723, IconName::SquareN },
            { 2724, IconName::SquareNfi },
            { 2725, IconName::SquareO },
            { 2726, IconName::SquareP },
            { 2727, IconName::SquareParking },
            { 2728, IconName::SquareParkingSlash },
            { 2729, IconName::SquarePen },
            { 2730, IconName::SquarePersonConfined },
            { 2731, IconName::SquarePhone },
            { 2732, IconName::SquarePhoneFlip },
            { 2733, IconName::SquarePhoneHangup },
            { 2734, IconName::SquarePlus },
            { 2735, IconName::SquarePollHorizontal },
            { 2736, IconName::SquarePollVertical },
            { 2737, IconName::SquareQ },
            { 2738, IconName::SquareQuarters },
            { 2739, IconName::SquareQuestion },
            { 2740, IconName::SquareQuote },
            { 2741, IconName::SquareR },
            { 2742, IconName::SquareRight },
            { 2743, IconName::SquareRing },
            { 2744, IconName::SquareRoot },
            { 2745, IconName::SquareRootVariable },
            { 2746, IconName::SquareRss },
            { 2747, IconName::SquareS },
            { 2748, IconName::SquareShareNodes },
            { 2749, IconName::SquareSliders },
            { 2750, IconName::SquareSlidersVertical },
            { 2751, IconName::SquareSmall },
            { 2752, IconName::SquareStar },
            { 2753, IconName::SquareT },
            { 2754, IconName::SquareTerminal },
            { 2755, IconName::SquareThisWayUp },
            { 2756, IconName::SquareU },
            { 2757, IconName::SquareUp },
            { 2758, IconName::SquareUpLeft },
            { 2759, IconName::SquareUpRight },
            { 2760, IconName::SquareUser },
            { 2761, IconName::SquareV },
            { 2762, IconName::SquareVirus },
            { 2763, IconName::SquareW },
            { 2764, IconName::SquareX },
            { 2765, IconName::SquareXmark },
            { 2766, IconName::SquareY },
            { 2767, IconName::SquareZ },
            { 2768, IconName::Squid },
            { 2769, IconName::Squirrel },
            { 2770, IconName::Staff },
            { 2771, IconName::StaffSnake },
            { 2772, IconName::Stairs },
            { 2773, IconName::Stamp },
            { 2774, IconName::StandardDefinition },
            { 2775, IconName::Stapler },
            { 2776, IconName::Star },
            { 2777, IconName::StarAndCrescent },
            { 2778, IconName::StarChristmas },
            { 2779, IconName::StarExclamation },
            { 2780, IconName::Starfighter },
            { 2781, IconName::StarfighterTwinIonEngine },
            { 2782, IconName::StarfighterTwinIonEngineAdvanced },
            { 2783, IconName::StarHalf },
            { 2784, IconName::StarHalfStroke },
            { 2785, IconName::StarOfDavid },
            { 2786, IconName::StarOfLife },
            { 2787, IconName::Stars },
            { 2788, IconName::StarSharp },
            { 2789, IconName::StarSharpHalf },
            { 2790, IconName::StarSharpHalfStroke },
            { 2791, IconName::Starship },
            { 2792, IconName::StarshipFreighter },
            { 2793, IconName::StarShooting },
            { 2794, IconName::Steak },
            { 2795, IconName::SteeringWheel },
            { 2796, IconName::SterlingSign },
            { 2797, IconName::Stethoscope },
            { 2798, IconName::Stocking },
            { 2799, IconName::Stomach },
            { 2800, IconName::Stop },
            { 2801, IconName::Stopwatch },
            { 2802, IconName::Stopwatch20 },
            { 2803, IconName::Store },
            { 2804, IconName::StoreLock },
            { 2805, IconName::StoreSlash },
            { 2806, IconName::Strawberry },
            { 2807, IconName::StreetView },
            { 2808, IconName::Stretcher },
            { 2809, IconName::Strikethrough },
            { 2810, IconName::Stroopwafel },
            { 2811, IconName::Subscript },
            { 2812, IconName::Subtitles },
            { 2813, IconName::SubtitlesSlash },
            { 2814, IconName::Suitcase },
            { 2815, IconName::SuitcaseMedical },
            { 2816, IconName::SuitcaseRolling },
            { 2817, IconName::Sun },
            { 2818, IconName::SunBright },
            { 2819, IconName::SunCloud },
            { 2820, IconName::SunDust },
            { 2821, IconName::Sunglasses },
            { 2822, IconName::SunHaze },
            { 2823, IconName::SunPlantWilt },
            { 2824, IconName::Sunrise },
            { 2825, IconName::Sunset },
            { 2826, IconName::Superscript },
            { 2827, IconName::Sushi },
            { 2828, IconName::SushiRoll },
            { 2829, IconName::Swap },
            { 2830, IconName::SwapArrows },
            { 2831, IconName::Swatchbook },
            { 2832, IconName::Sword },
            { 2833, IconName::SwordLaser },
            { 2834, IconName::SwordLaserAlt },
            { 2835, IconName::Swords },
            { 2836, IconName::SwordsLaser },
            { 2837, IconName::Symbols },
            { 2838, IconName::Synagogue },
            { 2839, IconName::Syringe },
            { 2840, IconName::T },
            { 2841, IconName::Table },
            { 2842, IconName::TableCells },
            { 2843, IconName::TableCellsLarge },
            { 2844, IconName::TableColumns },
            { 2845, IconName::TableLayout },
            { 2846, IconName::TableList },
            { 2847, IconName::TablePicnic },
            { 2848, IconName::TablePivot },
            { 2849, IconName::TableRows },
            { 2850, IconName::Tablet },
            { 2851, IconName::TabletButton },
            { 2852, IconName::TableTennisPaddleBall },
            { 2853, IconName::TableTree },
            { 2854, IconName::TabletRugged },
            { 2855, IconName::Tablets },
            { 2856, IconName::TabletScreen },
            { 2857, IconName::TabletScreenButton },
            { 2858, IconName::TachographDigital },
            { 2859, IconName::Taco },
            { 2860, IconName::Tag },
            { 2861, IconName::Tags },
            { 2862, IconName::Tally },
            { 2863, IconName::Tally1 },
            { 2864, IconName::Tally2 },
            { 2865, IconName::Tally3 },
            { 2866, IconName::Tally4 },
            { 2867, IconName::Tamale },
            { 2868, IconName::TankWater },
            { 2869, IconName::Tape },
            { 2870, IconName::Tarp },
            { 2871, IconName::TarpDroplet },
            { 2872, IconName::Taxi },
            { 2873, IconName::TaxiBus },
            { 2874, IconName::TeddyBear },
            { 2875, IconName::Teeth },
            { 2876, IconName::TeethOpen },
            { 2877, IconName::Telescope },
            { 2878, IconName::TemperatureArrowDown },
            { 2879, IconName::TemperatureArrowUp },
            { 2880, IconName::TemperatureEmpty },
            { 2881, IconName::TemperatureFull },
            { 2882, IconName::TemperatureHalf },
            { 2883, IconName::TemperatureHigh },
            { 2884, IconName::TemperatureList },
            { 2885, IconName::TemperatureLow },
            { 2886, IconName::TemperatureQuarter },
            { 2887, IconName::TemperatureSnow },
            { 2888, IconName::TemperatureSun },
            { 2889, IconName::TemperatureThreeQuarters },
            { 2890, IconName::TengeSign },
            { 2891, IconName::TennisBall },
            { 2892, IconName::Tent },
            { 2893, IconName::TentArrowDownToLine },
            { 2894, IconName::TentArrowLeftRight },
            { 2895, IconName::TentArrowsDown },
            { 2896, IconName::TentArrowTurnLeft },
            { 2897, IconName::TentDoublePeak },
            { 2898, IconName::Tents },
            { 2899, IconName::Terminal },
            { 2900, IconName::Text },
            { 2901, IconName::TextHeight },
            { 2902, IconName::TextSize },
            { 2903, IconName::TextSlash },
            { 2904, IconName::TextWidth },
            { 2905, IconName::Thermometer },
            { 2906, IconName::Theta },
            { 2907, IconName::ThoughtBubble },
            { 2908, IconName::ThumbsDown },
            { 2909, IconName::ThumbsUp },
            { 2910, IconName::Thumbtack },
            { 2911, IconName::Tick },
            { 2912, IconName::Ticket },
            { 2913, IconName::TicketAirline },
            { 2914, IconName::TicketPerforated },
            { 2915, IconName::Tickets },
            { 2916, IconName::TicketsAirline },
            { 2917, IconName::TicketSimple },
            { 2918, IconName::TicketsPerforated },
            { 2919, IconName::TicketsSimple },
            { 2920, IconName::Tilde },
            { 2921, IconName::Timeline },
            { 2922, IconName::TimelineArrow },
            { 2923, IconName::Timer },
            { 2924, IconName::Tire },
            { 2925, IconName::TireFlat },
            { 2926, IconName::TirePressureWarning },
            { 2927, IconName::TireRugged },
            { 2928, IconName::ToggleLargeOff },
            { 2929, IconName::ToggleLargeOn },
            { 2930, IconName::ToggleOff },
            { 2931, IconName::ToggleOn },
            { 2932, IconName::Toilet },
            { 2933, IconName::ToiletPaper },
            { 2934, IconName::ToiletPaperBlank },
            { 2935, IconName::ToiletPaperBlankUnder },
            { 2936, IconName::ToiletPaperCheck },
            { 2937, IconName::ToiletPaperSlash },
            { 2938, IconName::ToiletPaperUnder },
            { 2939, IconName::ToiletPaperUnderSlash },
            { 2940, IconName::ToiletPaperXmark },
            { 2941, IconName::ToiletPortable },
            { 2942, IconName::ToiletsPortable },
            { 2943, IconName::Tomato },
            { 2944, IconName::Tombstone },
            { 2945, IconName::TombstoneBlank },
            { 2946, IconName::Toolbox },
            { 2947, IconName::Tooth },
            { 2948, IconName::Toothbrush },
            { 2949, IconName::ToriiGate },
            { 2950, IconName::Tornado },
            { 2951, IconName::TowerBroadcast },
            { 2952, IconName::TowerCell },
            { 2953, IconName::TowerControl },
            { 2954, IconName::TowerObservation },
            { 2955, IconName::Tractor },
            { 2956, IconName::Trademark },
            { 2957, IconName::TrafficCone },
            { 2958, IconName::TrafficLight },
            { 2959, IconName::TrafficLightGo },
            { 2960, IconName::TrafficLightSlow },
            { 2961, IconName::TrafficLightStop },
            { 2962, IconName::Trailer },
            { 2963, IconName::Train },
            { 2964, IconName::TrainSubway },
            { 2965, IconName::TrainSubwayTunnel },
            { 2966, IconName::TrainTrack },
            { 2967, IconName::TrainTram },
            { 2968, IconName::TrainTunnel },
            { 2969, IconName::TransformerBolt },
            { 2970, IconName::Transgender },
            { 2971, IconName::Transporter },
            { 2972, IconName::Transporter1 },
            { 2973, IconName::Transporter2 },
            { 2974, IconName::Transporter3 },
            { 2975, IconName::Transporter4 },
            { 2976, IconName::Transporter5 },
            { 2977, IconName::Transporter6 },
            { 2978, IconName::Transporter7 },
            { 2979, IconName::TransporterEmpty },
            { 2980, IconName::Trash },
            { 2981, IconName::TrashArrowUp },
            { 2982, IconName::TrashCan },
            { 2983, IconName::TrashCanArrowUp },
            { 2984, IconName::TrashCanCheck },
            { 2985, IconName::TrashCanClock },
            { 2986, IconName::TrashCanList },
            { 2987, IconName::TrashCanPlus },
            { 2988, IconName::TrashCanSlash },
            { 2989, IconName::TrashCanUndo },
            { 2990, IconName::TrashCanXmark },
            { 2991, IconName::TrashCheck },
            { 2992, IconName::TrashClock },
            { 2993, IconName::TrashList },
            { 2994, IconName::TrashPlus },
            { 2995, IconName::TrashSlash },
            { 2996, IconName::TrashUndo },
            { 2997, IconName::TrashXmark },
            { 2998, IconName::TreasureChest },
            { 2999, IconName::Tree },
            { 3000, IconName::TreeChristmas },
            { 3001, IconName::TreeCity },
            { 3002, IconName::TreeDeciduous },
            { 3003, IconName::TreeDecorated },
            { 3004, IconName::TreeLarge },
            { 3005, IconName::TreePalm },
            { 3006, IconName::Trees },
            { 3007, IconName::TRex },
            { 3008, IconName::Triangle },
            { 3009, IconName::TriangleExclamation },
            { 3010, IconName::TriangleInstrument },
            { 3011, IconName::TrianglePersonDigging },
            { 3012, IconName::Tricycle },
            { 3013, IconName::TricycleAdult },
            { 3014, IconName::Trillium },
            { 3015, IconName::Trophy },
            { 3016, IconName::TrophyStar },
            { 3017, IconName::Trowel },
            { 3018, IconName::TrowelBricks },
            { 3019, IconName::Truck },
            { 3020, IconName::TruckArrowRight },
            { 3021, IconName::TruckBolt },
            { 3022, IconName::TruckClock },
            { 3023, IconName::TruckContainer },
            { 3024, IconName::TruckContainerEmpty },
            { 3025, IconName::TruckDroplet },
            { 3026, IconName::TruckFast },
            { 3027, IconName::TruckField },
            { 3028, IconName::TruckFieldUn },
            { 3029, IconName::TruckFire },
            { 3030, IconName::TruckFlatbed },
            { 3031, IconName::TruckFront },
            { 3032, IconName::TruckLadder },
            { 3033, IconName::TruckMedical },
            { 3034, IconName::TruckMonster },
            { 3035, IconName::TruckMoving },
            { 3036, IconName::TruckPickup },
            { 3037, IconName::TruckPlane },
            { 3038, IconName::TruckPlow },
            { 3039, IconName::TruckRamp },
            { 3040, IconName::TruckRampBox },
            { 3041, IconName::TruckRampCouch },
            { 3042, IconName::TruckTow },
            { 3043, IconName::TruckUtensils },
            { 3044, IconName::Trumpet },
            { 3045, IconName::Tty },
            { 3046, IconName::TtyAnswer },
            { 3047, IconName::TugrikSign },
            { 3048, IconName::Turkey },
            { 3049, IconName::TurkishLiraSign },
            { 3050, IconName::TurnDown },
            { 3051, IconName::TurnDownLeft },
            { 3052, IconName::TurnDownRight },
            { 3053, IconName::TurnLeft },
            { 3054, IconName::TurnLeftDown },
            { 3055, IconName::TurnLeftUp },
            { 3056, IconName::TurnRight },
            { 3057, IconName::Turntable },
            { 3058, IconName::TurnUp },
            { 3059, IconName::Turtle },
            { 3060, IconName::Tv },
            { 3061, IconName::TvMusic },
            { 3062, IconName::TvRetro },
            { 3063, IconName::Typewriter },
            { 3064, IconName::U },
            { 3065, IconName::Ufo },
            { 3066, IconName::UfoBeam },
            { 3067, IconName::Umbrella },
            { 3068, IconName::UmbrellaBeach },
            { 3069, IconName::UmbrellaSimple },
            { 3070, IconName::Underline },
            { 3071, IconName::Unicorn },
            { 3072, IconName::UniformMartialArts },
            { 3073, IconName::Union },
            { 3074, IconName::UniversalAccess },
            { 3075, IconName::Unlock },
            { 3076, IconName::UnlockKeyhole },
            { 3077, IconName::Up },
            { 3078, IconName::UpDown },
            { 3079, IconName::UpDownLeftRight },
            { 3080, IconName::UpFromBracket },
            { 3081, IconName::UpFromDottedLine },
            { 3082, IconName::UpFromLine },
            { 3083, IconName::UpLeft },
            { 3084, IconName::Upload },
            { 3085, IconName::UpLong },
            { 3086, IconName::UpRight },
            { 3087, IconName::UpRightAndDownLeftFromCenter },
            { 3088, IconName::UpRightFromSquare },
            { 3089, IconName::UpToDottedLine },
            { 3090, IconName::UpToLine },
            { 3091, IconName::UsbDrive },
            { 3092, IconName::User },
            { 3093, IconName::UserAlien },
            { 3094, IconName::UserAstronaut },
            { 3095, IconName::UserBountyHunter },
            { 3096, IconName::UserCheck },
            { 3097, IconName::UserChef },
            { 3098, IconName::UserClock },
            { 3099, IconName::UserCowboy },
            { 3100, IconName::UserCrown },
            { 3101, IconName::UserDoctor },
            { 3102, IconName::UserDoctorHair },
            { 3103, IconName::UserDoctorHairLong },
            { 3104, IconName::UserDoctorMessage },
            { 3105, IconName::UserGear },
            { 3106, IconName::UserGraduate },
            { 3107, IconName::UserGroup },
            { 3108, IconName::UserGroupCrown },
            { 3109, IconName::UserGroupSimple },
            { 3110, IconName::UserHair },
            { 3111, IconName::XmarksLines },
            { 3112, IconName::XmarkToSlot },
            { 3113, IconName::XRay },
            { 3114, IconName::Y },
            { 3115, IconName::YenSign },
            { 3116, IconName::YinYang },
            { 3117, IconName::Z },
            { 3118, IconName::UserHairLong },
            { 3119, IconName::UserHairMullet },
            { 3120, IconName::UserHeadset },
            { 3121, IconName::UserHelmetSafety },
            { 3122, IconName::UserInjured },
            { 3123, IconName::UserLarge },
            { 3124, IconName::UserLargeSlash },
            { 3125, IconName::UserLock },
            { 3126, IconName::UserMagnifyingGlass },
            { 3127, IconName::UserMinus },
            { 3128, IconName::UserMusic },
            { 3129, IconName::UserNinja },
            { 3130, IconName::UserNurse },
            { 3131, IconName::UserNurseHair },
            { 3132, IconName::UserNurseHairLong },
            { 3133, IconName::UserPen },
            { 3134, IconName::UserPilot },
            { 3135, IconName::UserPilotTie },
            { 3136, IconName::UserPlus },
            { 3137, IconName::UserPolice },
            { 3138, IconName::UserPoliceTie },
            { 3139, IconName::UserRobot },
            { 3140, IconName::UserRobotXmarks },
            { 3141, IconName::Users },
            { 3142, IconName::UsersBetweenLines },
            { 3143, IconName::UserSecret },
            { 3144, IconName::UsersGear },
            { 3145, IconName::UserShakespeare },
            { 3146, IconName::UserShield },
            { 3147, IconName::UserSlash },
            { 3148, IconName::UsersLine },
            { 3149, IconName::UsersMedical },
            { 3150, IconName::UsersRectangle },
            { 3151, IconName::UsersSlash },
            { 3152, IconName::UsersViewfinder },
            { 3153, IconName::UserTag },
            { 3154, IconName::UserTie },
            { 3155, IconName::UserUnlock },
            { 3156, IconName::UserTieHairLong },
            { 3157, IconName::UserVisor },
            { 3158, IconName::UserVneck },
            { 3159, IconName::UserTieHair },
            { 3160, IconName::UserVneckHair },
            { 3161, IconName::UserVneckHairLong },
            { 3162, IconName::UsersRays },
            { 3163, IconName::UserXmark },
            { 3164, IconName::Utensils },
            { 3165, IconName::UtensilsSlash },
            { 3166, IconName::UtilityPole },
            { 3167, IconName::UtilityPoleDouble },
            { 3168, IconName::V },
            { 3169, IconName::Vacuum },
            { 3170, IconName::VacuumRobot },
            { 3171, IconName::ValueAbsolute },
            { 3172, IconName::VanShuttle },
            { 3173, IconName::VectorCircle },
            { 3174, IconName::Vault },
            { 3175, IconName::VectorPolygon },
            { 3176, IconName::VectorSquare },
            { 3177, IconName::Venus },
            { 3178, IconName::VenusDouble },
            { 3179, IconName::VenusMars },
            { 3180, IconName::VestPatches },
            { 3181, IconName::Vial },
            { 3182, IconName::VialCircleCheck },
            { 3183, IconName::Vials },
            { 3184, IconName::Vest },
            { 3185, IconName::Video },
            { 3186, IconName::VideoArrowDownLeft },
            { 3187, IconName::VideoSlash },
            { 3188, IconName::VideoPlus },
            { 3189, IconName::Violin },
            { 3190, IconName::VirusCovidSlash },
            { 3191, IconName::Viruses },
            { 3192, IconName::VirusSlash },
            { 3193, IconName::Virus },
            { 3194, IconName::Vihara },
            { 3195, IconName::VirusCovid },
            { 3196, IconName::Volcano },
            { 3197, IconName::VentDamper },
            { 3198, IconName::VolumeSlash },
            { 3199, IconName::VolumeXmark },
            { 3200, IconName::VrCardboard },
            { 3201, IconName::Volume },
            { 3202, IconName::VolumeLow },
            { 3203, IconName::Volleyball },
            { 3204, IconName::VolumeOff },
            { 3205, IconName::Waffle },
            { 3206, IconName::VialVirus },
            { 3207, IconName::VolumeHigh },
            { 3208, IconName::Voicemail },
            { 3209, IconName::W },
            { 3210, IconName::VideoArrowUpRight },
            { 3211, IconName::WagonCovered },
            { 3212, IconName::Walker },
            { 3213, IconName::WalkieTalkie },
            { 3214, IconName::Wallet },
            { 3215, IconName::Wand },
            { 3216, IconName::WandMagic },
            { 3217, IconName::WandMagicSparkles },
            { 3218, IconName::WandSparkles },
            { 3219, IconName::Warehouse },
            { 3220, IconName::WarehouseFull },
            { 3221, IconName::WashingMachine },
            { 3222, IconName::Watch },
            { 3223, IconName::WatchApple },
            { 3224, IconName::WatchCalculator },
            { 3225, IconName::WatchFitness },
            { 3226, IconName::WatchSmart },
            { 3227, IconName::Water },
            { 3228, IconName::WaterArrowDown },
            { 3229, IconName::WaterArrowUp },
            { 3230, IconName::WaterLadder },
            { 3231, IconName::WatermelonSlice },
            { 3232, IconName::Wave },
            { 3233, IconName::Waveform },
            { 3234, IconName::WaveformLines },
            { 3235, IconName::WavePulse },
            { 3236, IconName::WaveSine },
            { 3237, IconName::WaveSquare },
            { 3238, IconName::WavesSine },
            { 3239, IconName::WaveTriangle },
            { 3240, IconName::Webhook },
            { 3241, IconName::WeightHanging },
            { 3242, IconName::WeightScale },
            { 3243, IconName::Whale },
            { 3244, IconName::Wheat },
            { 3245, IconName::WheatAwn },
            { 3246, IconName::WheatAwnCircleExclamation },
            { 3247, IconName::WheatAwnSlash },
            { 3248, IconName::WheatSlash },
            { 3249, IconName::Wheelchair },
            { 3250, IconName::WheelchairMove },
            { 3251, IconName::WhiskeyGlass },
            { 3252, IconName::WhiskeyGlassIce },
            { 3253, IconName::Whistle },
            { 3254, IconName::Wifi },
            { 3255, IconName::WifiExclamation },
            { 3256, IconName::WifiFair },
            { 3257, IconName::WifiSlash },
            { 3258, IconName::WifiWeak },
            { 3259, IconName::Wind },
            { 3260, IconName::Window },
            { 3261, IconName::WindowFlip },
            { 3262, IconName::WindowFrame },
            { 3263, IconName::WindowFrameOpen },
            { 3264, IconName::WindowMaximize },
            { 3265, IconName::WindowMinimize },
            { 3266, IconName::WindowRestore },
            { 3267, IconName::Windsock },
            { 3268, IconName::WindTurbine },
            { 3269, IconName::WindWarning },
            { 3270, IconName::WineBottle },
            { 3271, IconName::WineGlass },
            { 3272, IconName::WineGlassCrack },
            { 3273, IconName::WineGlassEmpty },
            { 3274, IconName::WonSign },
            { 3275, IconName::Worm },
            { 3276, IconName::Wreath },
            { 3277, IconName::WreathLaurel },
            { 3278, IconName::Wrench },
            { 3279, IconName::WrenchSimple },
            { 3280, IconName::X },
            { 3281, IconName::Xmark },
            { 3282, IconName::XmarkLarge },
        }),
    };
    return QtMocHelpers::metaObjectData<void, qt_meta_tag_ZN11ElaIconTypeE_t>(QMC::PropertyAccessInStaticMetaCall, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}

static constexpr auto qt_staticMetaObjectContent_ZN11ElaIconTypeE =
    ElaIconType::qt_create_metaobjectdata<qt_meta_tag_ZN11ElaIconTypeE_t>();
static constexpr auto qt_staticMetaObjectStaticContent_ZN11ElaIconTypeE =
    qt_staticMetaObjectContent_ZN11ElaIconTypeE.staticData;
static constexpr auto qt_staticMetaObjectRelocatingContent_ZN11ElaIconTypeE =
    qt_staticMetaObjectContent_ZN11ElaIconTypeE.relocatingData;

Q_CONSTINIT const QMetaObject ElaIconType::staticMetaObject = { {
    nullptr,
    qt_staticMetaObjectStaticContent_ZN11ElaIconTypeE.stringdata,
    qt_staticMetaObjectStaticContent_ZN11ElaIconTypeE.data,
    nullptr,
    nullptr,
    qt_staticMetaObjectRelocatingContent_ZN11ElaIconTypeE.metaTypes,
    nullptr
} };

QT_WARNING_POP
