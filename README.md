# Civitai 图片查看器

一个基于 Qt 和 ElaWidgetTools 的现代化桌面应用程序，用于浏览和管理 Civitai 网站上的 AI 生成图片及其元数据。

## 功能特性

- 🖼️ **图片浏览**: 以网格布局浏览 Civitai 图片
- 📊 **元数据展示**: 支持 JSON 文本和结构化树状视图
- 🔍 **搜索功能**: 支持关键词搜索和多种筛选条件
- 💾 **智能缓存**: 内存和磁盘双重缓存机制
- 🎨 **现代界面**: 基于 ElaWidgetTools 的 Fluent Design 风格
- ⚙️ **配置管理**: 完整的设置和配置系统
- 🌓 **主题切换**: 支持亮色和暗色主题

## 技术架构

### 核心模块

1. **UI 模块**: 基于 Qt 和 ElaWidgetTools 的现代化界面
2. **Civitai API 客户端**: 处理所有网络通信和 API 交互
3. **数据处理模块**: 解析和处理图片元数据
4. **图片管理器**: 异步下载和缓存管理
5. **配置管理器**: 用户设置和配置持久化

### 技术栈

- **UI 框架**: Qt 6.x + ElaWidgetTools
- **编程语言**: C++17
- **构建系统**: CMake
- **网络通信**: QNetworkAccessManager
- **数据格式**: JSON

## 项目结构

```
Civitai_IMG/
├── CMakeLists.txt              # 主构建文件
├── README.md                   # 项目说明
├── Civitai_IMG开发文档.md      # 详细开发文档
├── src/                        # 源代码目录
│   ├── main.cpp               # 应用程序入口
│   ├── MainWindow.h/.cpp      # 主窗口
│   ├── CivitaiClient.h/.cpp   # API 客户端
│   ├── CivitaiImageInfo.h/.cpp # 图片信息数据模型
│   ├── ImageManager.h/.cpp    # 图片管理器
│   ├── ConfigManager.h/.cpp   # 配置管理器
│   ├── MetaDataProcessor.h/.cpp # 元数据处理器
│   └── ImageCardWidget.h/.cpp # 图片卡片组件
├── third_party/               # 第三方库目录
│   └── ElaWidgetTools/        # ElaWidgetTools 库
├── docs/                      # 文档目录
└── tests/                     # 测试目录
```

## 构建要求

### 系统要求

- **操作系统**: Windows 10/11, macOS 10.15+, Linux (Ubuntu 20.04+)
- **编译器**: 
  - Windows: MSVC 2019+ 或 MinGW
  - macOS: Xcode 12+
  - Linux: GCC 9+ 或 Clang 10+

### 依赖项

- **Qt**: 6.2+ (推荐 6.5 LTS)
  - Qt6Core
  - Qt6Widgets  
  - Qt6Network
- **CMake**: 3.16+
- **ElaWidgetTools**: 最新版本 (MIT 许可证)

## 构建步骤

### 1. 克隆项目

```bash
git clone <repository-url>
cd Civitai_IMG
```

### 2. 获取 ElaWidgetTools

```bash
# 克隆 ElaWidgetTools 到 third_party 目录
git clone https://github.com/Liniyous/ElaWidgetTools.git third_party/ElaWidgetTools
```

### 3. 配置和构建

```bash
# 创建构建目录
mkdir build
cd build

# 配置项目
cmake .. -DCMAKE_BUILD_TYPE=Release

# 构建项目
cmake --build . --config Release
```

### 4. 运行应用程序

```bash
# Windows
./bin/CivitaiImageViewer.exe

# Linux/macOS
./bin/CivitaiImageViewer
```

## 配置说明

### API 密钥配置

1. 访问 [Civitai 账户设置](https://civitai.com/user/account)
2. 生成 API 密钥
3. 在应用程序中通过 "设置" 菜单配置 API 密钥

### 缓存配置

- **内存缓存**: 默认 50MB，可在设置中调整
- **磁盘缓存**: 默认 1GB，存储在系统缓存目录
- **并发下载**: 默认 4 个并发连接

## 使用说明

### 基本操作

1. **搜索图片**: 在搜索框中输入关键词
2. **筛选结果**: 使用排序、时间范围和 NSFW 等级筛选
3. **查看详情**: 点击图片卡片查看元数据
4. **分页浏览**: 使用底部分页控件浏览更多结果

### 元数据查看

- **JSON 文本**: 查看原始 JSON 格式的元数据
- **结构化视图**: 以树状结构展示元数据层级

### 主题切换

通过 "视图" 菜单可以在亮色和暗色主题之间切换。

## 开发说明

### 代码规范

- 遵循 Qt 命名约定
- 使用 Doxygen 风格注释
- 类名采用 PascalCase
- 成员变量以 `m_` 开头

### 测试

```bash
# 运行单元测试
cd build
ctest --output-on-failure
```

### 调试

项目支持调试模式，可在配置中启用详细日志输出。

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 依赖项许可证

- **Qt**: LGPL v3 / Commercial
- **ElaWidgetTools**: MIT

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 问题反馈

如遇到问题或有功能建议，请在 GitHub Issues 中提交。

## 更新日志

### v1.0.0 (开发中)

- 初始版本
- 基本的图片浏览功能
- 元数据展示
- 缓存系统
- 配置管理

---

**注意**: 本项目仅用于学习和研究目的，请遵守 Civitai 的使用条款和 API 限制。
