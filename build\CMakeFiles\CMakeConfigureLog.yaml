
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/Program Files/LLVM/bin/clang++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is Clang, found in:
        C:/Users/<USER>/Desktop/Civitai_IMG/build/CMakeFiles/3.30.2/CompilerIdCXX/a.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1243 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:250 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1243 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:250 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Desktop/Civitai_IMG/build/CMakeFiles/CMakeScratch/TryCompile-95yr2t"
      binary: "C:/Users/<USER>/Desktop/Civitai_IMG/build/CMakeFiles/CMakeScratch/TryCompile-95yr2t"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/LLVM/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/Civitai_IMG/build/CMakeFiles/CMakeScratch/TryCompile-95yr2t'
        
        Run Build Command(s): C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/bin/cmake.exe -E env VERBOSE=1 mingw32-make -f Makefile cmTC_8b9cc/fast
        no such file or directory
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:56 (try_compile)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Check for working CXX compiler: C:/Program Files/LLVM/bin/clang++.exe"
    directories:
      source: "C:/Users/<USER>/Desktop/Civitai_IMG/build/CMakeFiles/CMakeScratch/TryCompile-ckz1nn"
      binary: "C:/Users/<USER>/Desktop/Civitai_IMG/build/CMakeFiles/CMakeScratch/TryCompile-ckz1nn"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/LLVM/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/Civitai_IMG/build/CMakeFiles/CMakeScratch/TryCompile-ckz1nn'
        
        Run Build Command(s): C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/bin/cmake.exe -E env VERBOSE=1 mingw32-make -f Makefile cmTC_fa730/fast
        no such file or directory
        
      exitCode: 1
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/Program Files/LLVM/bin/clang++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is Clang, found in:
        C:/Users/<USER>/Desktop/Civitai_IMG/build/CMakeFiles/3.30.2/CompilerIdCXX/a.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1243 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:250 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1243 (message)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:250 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python310/Lib/site-packages/cmake/data/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        
