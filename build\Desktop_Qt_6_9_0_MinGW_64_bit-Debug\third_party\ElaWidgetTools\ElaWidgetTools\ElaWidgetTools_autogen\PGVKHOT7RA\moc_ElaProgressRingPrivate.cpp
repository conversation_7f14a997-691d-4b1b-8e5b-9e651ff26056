/****************************************************************************
** Meta object code from reading C++ file 'ElaProgressRingPrivate.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../../../third_party/ElaWidgetTools/ElaWidgetTools/private/ElaProgressRingPrivate.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ElaProgressRingPrivate.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN22ElaProgressRingPrivateE_t {};
} // unnamed namespace

template <> constexpr inline auto ElaProgressRingPrivate::qt_create_metaobjectdata<qt_meta_tag_ZN22ElaProgressRingPrivateE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ElaProgressRingPrivate",
        "pBusyIndexChanged",
        "",
        "pBusyStartDegChanged",
        "pBusyContentDegChanged",
        "pBusyIndex",
        "pBusyStartDeg",
        "pBusyContentDeg"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'pBusyIndexChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pBusyStartDegChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pBusyContentDegChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'pBusyIndex'
        QtMocHelpers::PropertyData<int>(5, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 0),
        // property 'pBusyStartDeg'
        QtMocHelpers::PropertyData<int>(6, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 1),
        // property 'pBusyContentDeg'
        QtMocHelpers::PropertyData<int>(7, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable, 2),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ElaProgressRingPrivate, qt_meta_tag_ZN22ElaProgressRingPrivateE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ElaProgressRingPrivate::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN22ElaProgressRingPrivateE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN22ElaProgressRingPrivateE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN22ElaProgressRingPrivateE_t>.metaTypes,
    nullptr
} };

void ElaProgressRingPrivate::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ElaProgressRingPrivate *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->pBusyIndexChanged(); break;
        case 1: _t->pBusyStartDegChanged(); break;
        case 2: _t->pBusyContentDegChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ElaProgressRingPrivate::*)()>(_a, &ElaProgressRingPrivate::pBusyIndexChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaProgressRingPrivate::*)()>(_a, &ElaProgressRingPrivate::pBusyStartDegChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ElaProgressRingPrivate::*)()>(_a, &ElaProgressRingPrivate::pBusyContentDegChanged, 2))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = _t->_pBusyIndex; break;
        case 1: *reinterpret_cast<int*>(_v) = _t->_pBusyStartDeg; break;
        case 2: *reinterpret_cast<int*>(_v) = _t->_pBusyContentDeg; break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0:
            if (QtMocHelpers::setProperty(_t->_pBusyIndex, *reinterpret_cast<int*>(_v)))
                Q_EMIT _t->pBusyIndexChanged();
            break;
        case 1:
            if (QtMocHelpers::setProperty(_t->_pBusyStartDeg, *reinterpret_cast<int*>(_v)))
                Q_EMIT _t->pBusyStartDegChanged();
            break;
        case 2:
            if (QtMocHelpers::setProperty(_t->_pBusyContentDeg, *reinterpret_cast<int*>(_v)))
                Q_EMIT _t->pBusyContentDegChanged();
            break;
        default: break;
        }
    }
}

const QMetaObject *ElaProgressRingPrivate::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ElaProgressRingPrivate::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN22ElaProgressRingPrivateE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ElaProgressRingPrivate::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 3)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 3)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 3;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void ElaProgressRingPrivate::pBusyIndexChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ElaProgressRingPrivate::pBusyStartDegChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ElaProgressRingPrivate::pBusyContentDegChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}
QT_WARNING_POP
