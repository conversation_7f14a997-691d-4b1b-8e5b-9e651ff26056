{"classes": [{"className": "ElaProgressRingPrivate", "lineNumber": 9, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "member": "_pBusyIndex", "name": "pBusyIndex", "notify": "pBusyIndexChanged", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "member": "_pBusyStartDeg", "name": "pBusyStartDeg", "notify": "pBusyStartDegChanged", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "member": "_pBusyContentDeg", "name": "pBusyContentDeg", "notify": "pBusyContentDegChanged", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "ElaProgressRingPrivate", "signals": [{"access": "public", "index": 0, "name": "pBusyIndexChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pBusyStartDegChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "pBusyContentDegChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "ElaProgressRingPrivate.h", "outputRevision": 69}