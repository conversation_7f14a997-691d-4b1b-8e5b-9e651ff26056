# Civitai 图片查看器 - 快速开始指南

## 🚀 快速开始

### 前置要求

在开始之前，请确保您的系统满足以下要求：

#### 必需软件
- **Qt 6.2+** (推荐 Qt 6.5 LTS)
- **CMake 3.16+**
- **Git**
- **C++17 兼容编译器**:
  - Windows: Visual Studio 2019+ 或 MinGW
  - macOS: Xcode 12+
  - Linux: GCC 9+ 或 Clang 10+

#### 可选软件
- **Qt Creator** (推荐的 IDE)
- **Visual Studio Code** (配合 CMake 插件)

### 📦 获取项目

#### 1. 克隆主项目
```bash
git clone <repository-url>
cd Civitai_IMG
```

#### 2. 获取 ElaWidgetTools 依赖
```bash
# 克隆 ElaWidgetTools 到 third_party 目录
git clone https://github.com/Liniyous/ElaWidgetTools.git third_party/ElaWidgetTools
```

### 🔧 构建项目

#### Windows (推荐使用 Visual Studio)

##### 方法 1: 使用提供的批处理脚本
```cmd
# 双击运行或在命令行执行
build.bat
```

##### 方法 2: 手动构建
```cmd
# 创建构建目录
mkdir build
cd build

# 配置项目 (使用 Visual Studio)
cmake .. -G "Visual Studio 17 2022" -A x64

# 构建项目
cmake --build . --config Release
```

#### Linux/macOS

##### 方法 1: 使用提供的脚本
```bash
# 使脚本可执行并运行
chmod +x build.sh
./build.sh
```

##### 方法 2: 手动构建
```bash
# 创建构建目录
mkdir build
cd build

# 配置项目
cmake .. -DCMAKE_BUILD_TYPE=Release

# 构建项目
cmake --build . --config Release
```

### 🎯 运行应用程序

构建成功后，可执行文件位于：
- **Windows**: `build/bin/CivitaiImageViewer.exe`
- **Linux/macOS**: `build/bin/CivitaiImageViewer`

#### 直接运行
```bash
# Windows
build\bin\CivitaiImageViewer.exe

# Linux/macOS
./build/bin/CivitaiImageViewer
```

### ⚙️ 初始配置

#### 1. 获取 Civitai API 密钥

1. 访问 [Civitai 官网](https://civitai.com)
2. 登录您的账户
3. 进入 [账户设置页面](https://civitai.com/user/account)
4. 在 "API Keys" 部分生成新的 API 密钥
5. 复制生成的 API 密钥

#### 2. 配置应用程序

1. 启动应用程序
2. 点击菜单栏 "文件" → "设置"
3. 在 "API" 选项卡中：
   - 粘贴您的 API 密钥
   - 点击 "测试连接" 验证密钥有效性
   - 根据需要调整速率限制和重试次数
4. 在 "缓存" 选项卡中：
   - 设置内存缓存大小（推荐 50-100MB）
   - 选择磁盘缓存目录
   - 设置磁盘缓存最大大小（推荐 1-5GB）
5. 点击 "确定" 保存设置

### 🖼️ 开始使用

#### 基本操作

1. **搜索图片**
   - 在顶部搜索框输入关键词
   - 选择排序方式（最多反应、最新等）
   - 选择时间范围（全部时间、本月等）
   - 设置 NSFW 等级
   - 点击 "搜索" 按钮

2. **浏览图片**
   - 图片将以网格形式显示
   - 点击图片卡片查看详细信息
   - 使用底部分页控件浏览更多结果

3. **查看元数据**
   - 点击任意图片卡片
   - 右侧面板将显示图片的元数据
   - 可在 "JSON文本" 和 "结构化视图" 间切换

#### 高级功能

1. **主题切换**
   - 菜单栏 "视图" → "切换主题"
   - 或在设置中选择主题

2. **缓存管理**
   - 设置 → 缓存 → 查看缓存使用情况
   - 可清空缓存释放空间

3. **导入/导出设置**
   - 设置 → 高级 → 导入/导出设置
   - 便于在多台设备间同步配置

### 🔍 故障排除

#### 常见问题

**Q: 应用程序无法启动**
- 确保已安装 Qt 6.2+ 运行时库
- 检查系统是否缺少必要的 DLL/共享库
- 尝试在终端中运行以查看错误信息

**Q: 无法连接到 Civitai API**
- 检查网络连接
- 验证 API 密钥是否正确
- 确认 API 密钥未过期
- 检查防火墙设置

**Q: 图片加载缓慢或失败**
- 检查网络连接速度
- 调整并发下载数量（设置 → 缓存）
- 增加下载超时时间（设置 → 网络）
- 清空缓存后重试

**Q: 界面显示异常**
- 确保 ElaWidgetTools 正确编译
- 尝试切换主题
- 检查系统 DPI 设置

#### 获取帮助

如果遇到问题，可以：

1. **查看日志**
   - 启用调试模式（设置 → 通用 → 调试模式）
   - 查看控制台输出或日志文件

2. **重置设置**
   - 设置 → 高级 → 恢复默认设置
   - 或删除配置文件重新配置

3. **联系支持**
   - 在 GitHub 仓库提交 Issue
   - 提供详细的错误信息和系统环境

### 📚 进一步学习

- 阅读 [完整开发文档](../Civitai_IMG开发文档.md)
- 查看 [项目总结](项目总结.md)
- 浏览源代码了解实现细节
- 参考 [Civitai API 文档](https://github.com/civitai/civitai/wiki/REST-API-Reference)

### 🎉 开始探索

现在您已经成功设置了 Civitai 图片查看器！开始探索 AI 生成艺术的精彩世界吧！

---

**提示**: 首次使用时，建议先搜索一些热门关键词（如 "landscape", "portrait", "anime" 等）来熟悉应用程序的功能。
