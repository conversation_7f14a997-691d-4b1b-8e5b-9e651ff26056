#include <QtTest/QtTest>
#include <QJsonObject>
#include <QJsonDocument>
#include "../src/CivitaiImageInfo.h"

class TestCivitaiImageInfo : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    
    void testDefaultConstructor();
    void testJsonConstructor();
    void testCopyConstructor();
    void testAssignmentOperator();
    void testMoveConstructor();
    void testMoveAssignmentOperator();
    
    void testBasicProperties();
    void testMetaDataHandling();
    void testStatsHandling();
    void testValidation();
    void testDisplayMethods();
    void testJsonSerialization();

private:
    QJsonObject createTestImageJson();
    QJsonObject createTestMetaJson();
    QJsonObject createTestStatsJson();
};

void TestCivitaiImageInfo::initTestCase()
{
    // 测试初始化
}

void TestCivitaiImageInfo::cleanupTestCase()
{
    // 测试清理
}

void TestCivitaiImageInfo::testDefaultConstructor()
{
    CivitaiImageInfo imageInfo;
    
    QCOMPARE(imageInfo.id(), 0);
    QVERIFY(imageInfo.url().isEmpty());
    QVERIFY(imageInfo.thumbnailUrl().isEmpty());
    QCOMPARE(imageInfo.width(), 0);
    QCOMPARE(imageInfo.height(), 0);
    QVERIFY(imageInfo.nsfwLevel().isEmpty());
    QCOMPARE(imageInfo.isNsfw(), false);
    QCOMPARE(imageInfo.postId(), 0);
    QVERIFY(imageInfo.authorUsername().isEmpty());
    QVERIFY(!imageInfo.isValid());
}

void TestCivitaiImageInfo::testJsonConstructor()
{
    QJsonObject testJson = createTestImageJson();
    CivitaiImageInfo imageInfo(testJson);
    
    QCOMPARE(imageInfo.id(), 12345);
    QCOMPARE(imageInfo.url(), QString("https://example.com/image.jpg"));
    QCOMPARE(imageInfo.width(), 1024);
    QCOMPARE(imageInfo.height(), 768);
    QCOMPARE(imageInfo.nsfwLevel(), QString("None"));
    QCOMPARE(imageInfo.isNsfw(), false);
    QCOMPARE(imageInfo.postId(), 67890);
    QCOMPARE(imageInfo.authorUsername(), QString("testuser"));
    QVERIFY(imageInfo.isValid());
}

void TestCivitaiImageInfo::testCopyConstructor()
{
    QJsonObject testJson = createTestImageJson();
    CivitaiImageInfo original(testJson);
    CivitaiImageInfo copy(original);
    
    QCOMPARE(copy.id(), original.id());
    QCOMPARE(copy.url(), original.url());
    QCOMPARE(copy.width(), original.width());
    QCOMPARE(copy.height(), original.height());
    QCOMPARE(copy.authorUsername(), original.authorUsername());
}

void TestCivitaiImageInfo::testAssignmentOperator()
{
    QJsonObject testJson = createTestImageJson();
    CivitaiImageInfo original(testJson);
    CivitaiImageInfo assigned;
    
    assigned = original;
    
    QCOMPARE(assigned.id(), original.id());
    QCOMPARE(assigned.url(), original.url());
    QCOMPARE(assigned.width(), original.width());
    QCOMPARE(assigned.height(), original.height());
}

void TestCivitaiImageInfo::testMoveConstructor()
{
    QJsonObject testJson = createTestImageJson();
    CivitaiImageInfo original(testJson);
    int originalId = original.id();
    QString originalUrl = original.url();
    
    CivitaiImageInfo moved(std::move(original));
    
    QCOMPARE(moved.id(), originalId);
    QCOMPARE(moved.url(), originalUrl);
    QCOMPARE(original.id(), 0); // 原对象应该被重置
}

void TestCivitaiImageInfo::testMoveAssignmentOperator()
{
    QJsonObject testJson = createTestImageJson();
    CivitaiImageInfo original(testJson);
    int originalId = original.id();
    QString originalUrl = original.url();
    
    CivitaiImageInfo moved;
    moved = std::move(original);
    
    QCOMPARE(moved.id(), originalId);
    QCOMPARE(moved.url(), originalUrl);
    QCOMPARE(original.id(), 0); // 原对象应该被重置
}

void TestCivitaiImageInfo::testBasicProperties()
{
    CivitaiImageInfo imageInfo;
    
    // 测试设置和获取基本属性
    imageInfo.setId(123);
    QCOMPARE(imageInfo.id(), 123);
    
    imageInfo.setUrl("https://test.com/image.png");
    QCOMPARE(imageInfo.url(), QString("https://test.com/image.png"));
    
    imageInfo.setWidth(800);
    imageInfo.setHeight(600);
    QCOMPARE(imageInfo.width(), 800);
    QCOMPARE(imageInfo.height(), 600);
    
    imageInfo.setNsfwLevel("Soft");
    QCOMPARE(imageInfo.nsfwLevel(), QString("Soft"));
    
    imageInfo.setNsfw(true);
    QCOMPARE(imageInfo.isNsfw(), true);
    
    imageInfo.setAuthorUsername("testauthor");
    QCOMPARE(imageInfo.authorUsername(), QString("testauthor"));
}

void TestCivitaiImageInfo::testMetaDataHandling()
{
    CivitaiImageInfo imageInfo;
    QJsonObject testMeta = createTestMetaJson();
    
    imageInfo.setMetaData(testMeta);
    
    QJsonObject retrievedMeta = imageInfo.metaData();
    QCOMPARE(retrievedMeta, testMeta);
    
    QString formattedJson = imageInfo.formattedMetaJsonString();
    QVERIFY(!formattedJson.isEmpty());
    
    // 测试树模型创建
    QAbstractItemModel* model = imageInfo.createMetaTreeModel();
    QVERIFY(model != nullptr);
    QVERIFY(model->rowCount() > 0);
    delete model;
}

void TestCivitaiImageInfo::testStatsHandling()
{
    QJsonObject statsJson = createTestStatsJson();
    CivitaiImageStats stats(statsJson);
    
    QCOMPARE(stats.likeCount, 10);
    QCOMPARE(stats.commentCount, 5);
    QCOMPARE(stats.heartCount, 3);
    
    // 测试转换回JSON
    QJsonObject convertedJson = stats.toJson();
    QCOMPARE(convertedJson.value("likeCount").toInt(), 10);
    QCOMPARE(convertedJson.value("commentCount").toInt(), 5);
}

void TestCivitaiImageInfo::testValidation()
{
    CivitaiImageInfo imageInfo;
    
    // 默认情况下应该无效
    QVERIFY(!imageInfo.isValid());
    
    // 设置ID后应该有效
    imageInfo.setId(1);
    QVERIFY(imageInfo.isValid());
    
    // 重置ID后应该无效
    imageInfo.setId(0);
    QVERIFY(!imageInfo.isValid());
}

void TestCivitaiImageInfo::testDisplayMethods()
{
    CivitaiImageInfo imageInfo;
    
    // 测试显示标题
    imageInfo.setId(123);
    QString title = imageInfo.displayTitle();
    QVERIFY(title.contains("123"));
    
    imageInfo.setAuthorUsername("testuser");
    title = imageInfo.displayTitle();
    QVERIFY(title.contains("testuser"));
    
    // 测试尺寸字符串
    imageInfo.setWidth(1920);
    imageInfo.setHeight(1080);
    QString sizeStr = imageInfo.sizeString();
    QCOMPARE(sizeStr, QString("1920 x 1080"));
}

void TestCivitaiImageInfo::testJsonSerialization()
{
    QJsonObject testJson = createTestImageJson();
    CivitaiImageInfo imageInfo(testJson);
    
    // 转换回JSON
    QJsonObject serializedJson = imageInfo.toJson();
    
    // 验证关键字段
    QCOMPARE(serializedJson.value("id").toInt(), 12345);
    QCOMPARE(serializedJson.value("url").toString(), QString("https://example.com/image.jpg"));
    QCOMPARE(serializedJson.value("width").toInt(), 1024);
    QCOMPARE(serializedJson.value("height").toInt(), 768);
}

// 辅助方法
QJsonObject TestCivitaiImageInfo::createTestImageJson()
{
    QJsonObject json;
    json["id"] = 12345;
    json["url"] = "https://example.com/image.jpg";
    json["width"] = 1024;
    json["height"] = 768;
    json["nsfwLevel"] = "None";
    json["nsfw"] = false;
    json["postId"] = 67890;
    json["username"] = "testuser";
    json["createdAt"] = "2023-01-01T00:00:00Z";
    json["stats"] = createTestStatsJson();
    json["meta"] = createTestMetaJson();
    
    return json;
}

QJsonObject TestCivitaiImageInfo::createTestMetaJson()
{
    QJsonObject meta;
    meta["prompt"] = "beautiful landscape, mountains, sunset";
    meta["negative_prompt"] = "blurry, low quality";
    meta["steps"] = 20;
    meta["cfg_scale"] = 7.5;
    meta["seed"] = 123456789;
    meta["sampler"] = "DPM++ 2M Karras";
    meta["model"] = "Stable Diffusion v1.5";
    
    return meta;
}

QJsonObject TestCivitaiImageInfo::createTestStatsJson()
{
    QJsonObject stats;
    stats["likeCount"] = 10;
    stats["commentCount"] = 5;
    stats["heartCount"] = 3;
    stats["cryCount"] = 0;
    stats["laughCount"] = 1;
    
    return stats;
}

QTEST_MAIN(TestCivitaiImageInfo)
#include "test_civitai_image_info.moc"
